<!DOCTYPE html>
<html lang="tr">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Hakkımızda - Sultan Gazi Doğal Yaşam</title>
    <meta
      name="description"
      content="<PERSON> Gazi Doğal Yaşam - Binlerce yıllık geleneğin modern dokunuşu. Organik ürünlerimiz ve hikayemiz hakkında bilgi edinin."
    />
    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://cdnjs.cloudflare.com" crossorigin />
    <link rel="preconnect" href="https://fonts.googleapis.com" crossorigin />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link rel="preconnect" href="https://www.dogalyasam.net" crossorigin />
    <link rel="preconnect" href="https://player.vimeo.com" crossorigin />
    <link rel="preconnect" href="https://vimeocdn.com" crossorigin />

    <!-- Load critical CSS first -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.css"
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Lora:wght@400;500;600&display=swap"
      rel="stylesheet"
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&amp;display=swap"
      rel="stylesheet"
    />
    <!-- Optimize page loading -->
    <link rel="dns-prefetch" href="https://www.dogalyasam.net" />
    <meta http-equiv="x-dns-prefetch-control" content="on" />
    <!-- Content Security Policy to allow Vimeo video loading -->
    <meta
      http-equiv="Content-Security-Policy"
      content="script-src 'self' 'unsafe-inline' blob: https://*.vimeocdn.com https://player.vimeo.com https://cdnjs.cloudflare.com; frame-src https://player.vimeo.com;"
    />

    <style>
      :root {
        --main-green: #556b2f; /* Zeytin yeşili */
        --dark-green: #3b4a1f; /* Koyu zeytin yeşili */
        --light-green: #7d9b54; /* Açık zeytin yeşili */
        --gold: #d4af37; /* Altın sarısı */
        --cream: #f5f1e3; /* Krem */
        --earth-brown: #8b4513; /* Toprak kahvesi */
        --light-brown: #a67c52; /* Açık kahve */
        --text-color: #333333; /* Ana metin rengi */
        --dark-gray: #555555; /* Koyu gri */
        --light-gray: #f8f8f8; /* Açık gri */
        --bg-cream: #faf7f2; /* Arka plan krem */
      }
      body {
        font-family: "Lora", "Segoe UI", serif !important;
        line-height: 1.6;
        color: var(--text-color);
        overflow-x: hidden;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        width: 100%;
        max-width: 100%;
        margin: 0 !important;
        padding: 0;
      }
      .container {
        font-family: "Lora", "Segoe UI", serif !important;
        line-height: 1.6;
        color: var(--text-color);
        overflow-x: hidden;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        width: 100%;
        max-width: 100%;
        margin: 0;
        padding: 0;
        font-size: 16px; /* Temel font boyutu */
        position: relative;
        background: var(--bg-cream);
        overflow: hidden;
      }

      h1,
      h2,
      h3,
      h4,
      h5,
      h6 {
        font-family: "Playfair Display", serif !important;
        color: var(--main-green);
        font-weight: 600;
        letter-spacing: -0.01em;
      }

      /* Hero Section */
      .about-hero {
        position: relative;
        height: 85vh;
        /*background: linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.3)),
          url("https://www.dogalyasam.net/image/catalog/icons/new/olivePhoto00023.jpg")
            center/cover no-repeat;*/
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        color: white;
        overflow: hidden;
      }

      .hero-video-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
        z-index: -1;
        opacity: 0.8;
      }

      /* Optimize video loading */
      iframe[src*="vimeo.com"] {
        will-change: transform;
        transform: translate3d(-50%, -50%, 0);
        backface-visibility: hidden;
        -webkit-backface-visibility: hidden;
      }

      .hero-content {
        max-width: 800px;
        padding: 3rem;
        background-color: rgba(0, 0, 0, 0.4);
        backdrop-filter: blur(8px);
        -webkit-backdrop-filter: blur(8px);
        border-radius: 20px;
        border: 2px solid rgba(255, 255, 255, 0.3);
        z-index: 2;
      }

      .hero-title {
        font-size: 64px;
        line-height: 1.1;
        margin-bottom: 1.5rem;
        animation: fadeInUp 1s ease;
        color: #fff !important;
        font-weight: 700 !important;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      }

      .hero-subtitle {
        font-size: 24px;
        margin-bottom: 2rem;
        animation: fadeInUp 1s ease 0.3s forwards;
        opacity: 0;
        color: rgba(255, 255, 255, 0.9);
        font-weight: 400;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
      }

      .scroll-cta {
        position: absolute;
        bottom: 40px;
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        flex-direction: column;
        align-items: center;
        color: white;
        font-size: 16px;
        opacity: 0.8;
        transition: opacity 0.3s ease;
        cursor: pointer;
        z-index: 10;
      }

      .scroll-cta:hover {
        opacity: 1;
      }

      .scroll-icon {
        width: 30px;
        height: 50px;
        border: 2px solid white;
        border-radius: 15px;
        margin-top: 10px;
        position: relative;
      }

      .scroll-icon::before {
        content: "";
        position: absolute;
        top: 8px;
        left: 50%;
        width: 6px;
        height: 6px;
        background: white;
        border-radius: 50%;
        transform: translateX(-50%);
        animation: scrollAnim 2s infinite;
      }

      @keyframes scrollAnim {
        0% {
          top: 8px;
          opacity: 1;
        }
        80% {
          top: 32px;
          opacity: 0;
        }
        100% {
          top: 8px;
          opacity: 0;
        }
      }

      @keyframes fadeInUp {
        from {
          opacity: 0;
          transform: translateY(20px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      /* Section Styles */
      .section {
        padding: 80px 20px;
        position: relative;
      }

      .section-title {
        font-size: 48px;
        text-align: center;
        margin-bottom: 20px;
        position: relative;
        display: inline-block;
      }

      .section-title::after {
        content: "";
        position: absolute;
        bottom: -10px;
        left: 0;
        width: 100%;
        height: 8px;
        background: linear-gradient(
          90deg,
          transparent,
          var(--gold),
          transparent
        );
        border-radius: 4px;
      }

      /* Intro Section */
      .intro-section {
        background-color: white;
        position: relative;
      }

      .intro-content {
        max-width: 900px;
        margin: 0 auto;
        padding: 20px;
      }

      .intro-text {
        font-size: 18px;
        line-height: 1.8;
        color: var(--dark-gray);
        margin-bottom: 20px;
        text-align: justify;
      }

      /* About Section */
      .about-section {
        background: linear-gradient(135deg, #f5f1e3, #fff);
        position: relative;
      }

      .about-content {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: 40px;
        max-width: 1000px;
        margin: 0 auto;
        align-items: center;
      }

      .about-text {
        text-align: justify;
      }

      .about-text p {
        font-size: 17px;
        line-height: 1.8;
        color: var(--dark-gray);
        margin-bottom: 20px;
      }

      .about-image {
        position: relative;
        border-radius: 20px;
        overflow: hidden;
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
      }

      .about-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.5s ease;
      }

      .about-image:hover img {
        transform: scale(1.05);
      }

      .section-subtitle {
        font-size: 20px;
        text-align: center;
        color: var(--dark-gray);
        max-width: 800px;
        margin: 0 auto 50px;
      }

      .title-container {
        text-align: center;
        margin-bottom: 50px;
      }

      /* Map Section Styles */
      .map-section {
        background-color: var(--bg-cream);
        position: relative;
      }

      .map-container {
        position: relative;
        height: 500px;
        max-width: 1000px;
        margin: 0 auto 50px;
        overflow: hidden;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
      }

      .turkey-map {
        position: relative;
        width: 100%;
        height: 100%;
        background-color: #f5f5f5;
        transition: transform 3s ease;
        overflow: hidden;
      }

      .turkey-map img {
        width: 100%;
      }

      .map-pin {
        position: absolute;
        top: 290px;
        left: 631px;
        transform: translate(-50%, -50%);
        z-index: 10;
        opacity: 0;
        transition: opacity 1s ease 3s;
      }

      .pin-head {
        width: 20px;
        height: 20px;
        background-color: #d4af37;
        border-radius: 50%;
        border: 3px solid white;
        box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.2), 0 5px 10px rgba(0, 0, 0, 0.3);
        position: relative;
      }

      .pin-head::after {
        content: "";
        position: absolute;
        top: 50%;
        left: 50%;
        width: 30px;
        height: 30px;
        background-color: rgba(212, 175, 55, 0.3);
        border-radius: 50%;
        transform: translate(-50%, -50%);
        animation: pulse 2s infinite;
      }

      @keyframes pulse {
        0% {
          transform: translate(-50%, -50%) scale(0.5);
          opacity: 1;
        }
        100% {
          transform: translate(-50%, -50%) scale(1.5);
          opacity: 0;
        }
      }

      .pin-label {
        position: absolute;
        top: 25px;
        left: 50%;
        transform: translateX(-50%);
        background-color: white;
        color: var(--main-green);
        padding: 5px 10px;
        border-radius: 15px;
        font-size: 14px;
        font-weight: 600;
        white-space: nowrap;
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
      }

      .province-osmaniye {
        fill: var(--gold) !important;
        stroke-width: 1.5 !important;
        transition: fill 1s ease;
      }

      .region-stories {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 30px;
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
      }

      .region-story-card {
        background-color: white;
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
        text-align: center;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        border-top: 3px solid var(--gold);
      }

      .region-story-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
      }

      .region-icon {
        width: 80px;
        height: 80px;
        margin: 0 auto 20px;
        background-color: rgba(212, 175, 55, 0.1);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .region-icon img {
        width: 50px;
        height: 50px;
        object-fit: contain;
      }

      /* DNA Section Styles */
      .dna-section {
        background: linear-gradient(135deg, #f5f1e3, #fff);
        position: relative;
        overflow: hidden;
      }

      .dna-section::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: url("https://www.dogalyasam.net/image/catalog/icons/new/dna-bg.png")
          center/cover no-repeat;
        opacity: 0.05;
        z-index: 0;
      }

      .dna-visualization {
        position: relative;
        z-index: 1;
        max-width: 900px;
        margin: 0 auto;
      }

      .dna-strand {
        background-color: white;
        border-radius: 20px;
        padding: 40px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
      }

      .dna-compare {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 40px;
        margin-bottom: 30px;
      }

      .dna-column {
        text-align: center;
      }

      .dna-column h3 {
        font-size: 24px;
        margin-bottom: 20px;
        color: var(--main-green);
        position: relative;
        display: inline-block;
      }

      .dna-column h3::after {
        content: "";
        position: absolute;
        bottom: -8px;
        left: 0;
        width: 100%;
        height: 3px;
        background: var(--gold);
        border-radius: 2px;
      }

      .dna-list {
        list-style: none;
        padding: 0;
        margin: 0;
        text-align: left;
      }

      .dna-list li {
        padding: 12px 0;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        position: relative;
        padding-left: 30px;
      }

      .dna-list li::before {
        content: "🌿";
        position: absolute;
        left: 0;
        top: 12px;
      }

      .dna-list.modern li::before {
        content: "🏭";
      }

      .dna-highlight {
        background-color: rgba(212, 175, 55, 0.1);
        border-left: 4px solid var(--gold);
        padding: 20px;
        border-radius: 0 10px 10px 0;
      }

      .dna-highlight p {
        font-size: 18px;
        font-style: italic;
        color: var(--dark-green);
        margin: 0;
      }

      /* Family Section Styles */
      .family-section {
        background-color: white;
      }

      .family-story {
        max-width: 1200px;
        margin: 0 auto;
      }

      .video-container {
        width: 100%;
        max-width: 800px;
        margin: 0 auto 50px;
        border-radius: 20px;
        overflow: hidden;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      }

      .video-container video {
        width: 100%;
        display: block;
      }

      .family-generations {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 30px;
        margin-top: 50px;
      }

      .generation {
        background-color: white;
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
        text-align: center;
        transition: transform 0.3s ease;
        border: 1px solid rgba(0, 0, 0, 0.05);
      }

      .generation:hover {
        transform: translateY(-10px);
      }

      .generation-photo {
        width: 120px;
        height: 120px;
        margin: 0 auto 20px;
        border-radius: 50%;
        overflow: hidden;
        border: 3px solid var(--gold);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
      }

      .generation-photo img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      /* Sustainability Section Styles */
      .sustainability-section {
        background: linear-gradient(135deg, #f5f1e3, #fff);
        position: relative;
      }

      .sustainability-infographic {
        max-width: 1000px;
        margin: 0 auto;
      }

      .eco-impact {
        display: flex;
        align-items: center;
        background-color: white;
        border-radius: 20px;
        padding: 30px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
        margin-bottom: 40px;
        margin-top: 40px;
      }

      .impact-icon {
        flex: 0 0 100px;
        height: 100px;
        margin-right: 30px;
        background-color: rgba(85, 107, 47, 0.1);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .impact-icon img {
        width: 60px;
        height: 60px;
        object-fit: contain;
      }

      .impact-text {
        flex: 1;
      }

      .impact-text h3 {
        font-size: 24px;
        margin-bottom: 10px;
        color: var(--main-green);
      }

      .virtual-planting {
        background-color: white;
        border-radius: 20px;
        padding: 30px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
        margin-bottom: 40px;
        text-align: center;
      }

      .planting-area {
        margin-top: 30px;
      }

      .planting-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        grid-template-rows: repeat(3, 1fr);
        gap: 20px;
        max-width: 500px;
        margin: 0 auto 30px;
      }

      .tree-spot {
        height: 80px;
        background-color: rgba(85, 107, 47, 0.1);
        border-radius: 10px;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
      }

      .tree-spot:hover {
        background-color: rgba(85, 107, 47, 0.2);
      }

      .tree-spot.planted::before {
        content: "🌱";
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 30px;
      }

      .tree-spot.growing::before {
        content: "🌿";
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 30px;
      }

      .tree-spot.grown::before {
        content: "🌳";
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 30px;
      }

      .plant-button {
        background-color: var(--main-green);
        color: white;
        border: none;
        padding: 12px 30px;
        border-radius: 30px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 5px 15px rgba(85, 107, 47, 0.3);
      }

      .plant-button:hover {
        background-color: var(--dark-green);
        transform: translateY(-3px);
        box-shadow: 0 8px 20px rgba(85, 107, 47, 0.4);
      }

      .eco-practices {
        background-color: white;
        border-radius: 20px;
        padding: 30px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
      }

      .eco-practices h3 {
        text-align: center;
        margin-bottom: 20px;
        color: var(--main-green);
      }

      .eco-list {
        list-style: none;
        padding: 0;
        margin: 0;
      }

      .eco-list li {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
        padding-bottom: 15px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
      }

      .eco-list li:last-child {
        margin-bottom: 0;
        padding-bottom: 0;
        border-bottom: none;
      }

      .eco-icon {
        font-size: 24px;
        margin-right: 15px;
        flex: 0 0 30px;
        text-align: center;
      }

      .eco-text {
        flex: 1;
        font-size: 16px;
      }

      /* Philosophy Section Styles */
      .philosophy-section {
        background-color: white;
        position: relative;
      }

      .proverb-wall {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: 20px;
        max-width: 1000px;
        margin: 0 auto;
      }

      .proverb {
        background-color: var(--bg-cream);
        border-radius: 15px;
        padding: 20px 30px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        text-align: center;
        min-width: 250px;
        transition: transform 0.3s ease;
      }

      .proverb:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
      }

      .proverb p {
        font-size: 18px;
        font-style: italic;
        color: var(--dark-green);
        margin: 0;
        font-family: "Playfair Display", serif;
      }

      .philosophy-content {
        max-width: 800px;
        margin: 40px auto 0;
        text-align: center;
        padding: 30px;
        background-color: rgba(255, 255, 255, 0.7);
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
      }

      .philosophy-text {
        font-size: 18px;
        line-height: 1.8;
        color: var(--dark-gray);
        margin-bottom: 20px;
        font-style: italic;
      }

      .philosophy-text.signature {
        font-size: 22px;
        color: var(--main-green);
        font-weight: 600;
        text-align: right;
        margin-top: 30px;
        font-family: "Playfair Display", serif;
      }

      .easter-egg {
        position: absolute;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        gap: 10px;
        opacity: 0.1;
      }

      .hidden-letter {
        cursor: pointer;
        font-size: 16px;
        color: var(--dark-gray);
        transition: all 0.3s ease;
      }

      .hidden-letter:hover {
        color: var(--gold);
        transform: scale(1.2);
      }

      /* iOS Style Notification */
      .ios-notification {
        position: fixed;
        top: 20px;
        left: 50%;
        transform: translateX(-50%) translateY(-100px);
        background-color: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 12px 20px;
        border-radius: 12px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        z-index: 1000;
        opacity: 0;
        transition: all 0.5s cubic-bezier(0.68, -0.55, 0.27, 1.55);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        max-width: 90%;
      }

      .ios-notification.show {
        transform: translateX(-50%) translateY(0);
        opacity: 1;
      }

      .notification-content {
        display: flex;
        align-items: center;
      }

      .notification-icon {
        margin-right: 12px;
        font-size: 20px;
      }

      .notification-message {
        font-size: 16px;
        font-weight: 500;
      }

      /* Timeline Section */
      .timeline-section {
        background: linear-gradient(
            rgba(255, 255, 255, 0.9),
            rgba(255, 255, 255, 0.7)
          ),
          url("https://www.dogalyasam.net/image/catalog/icons/new/olivePhoto00014.png");
        background-size: cover;
        background-position: center;
        background-attachment: fixed;
      }

      .timeline-container {
        max-width: 1200px;
        margin: 0 auto;
        position: relative;
        padding: 40px 0;
      }

      .timeline-tree {
        position: absolute;
        left: 50%;
        top: 0;
        bottom: 0;
        width: 80px;
        transform: translateX(-50%);
        background: url("https://www.dogalyasam.net/image/catalog/icons/new/tree-root.png")
          no-repeat;
        background-size: contain;
        background-position: center;
        z-index: 1;
        opacity: 0.3;
      }

      .timeline-line {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 50%;
        width: 4px;
        background: linear-gradient(
          to bottom,
          rgba(85, 107, 47, 0.2),
          var(--main-green),
          rgba(85, 107, 47, 0.2)
        );
        transform: translateX(-50%);
        border-radius: 4px;
      }

      .timeline-item {
        position: relative;
        margin-bottom: 2rem;
        width: 100%;
        display: flex;
        justify-content: center;
      }

      .timeline-item:last-child {
        margin-bottom: 0;
      }

      .timeline-item:nth-child(odd) .timeline-content {
        margin-right: calc(50% + 30px);
        text-align: right;
      }

      .timeline-item:nth-child(even) .timeline-content {
        margin-left: calc(50% + 30px);
        text-align: left;
      }

      .timeline-item::before {
        content: "";
        position: absolute;
        left: 50%;
        top: 30px;
        width: 24px;
        height: 24px;
        background: white;
        border: 3px solid var(--gold);
        border-radius: 50%;
        z-index: 1;
        transform: translateX(-50%);
        box-shadow: 0 0 0 4px rgba(212, 175, 55, 0.3);
      }

      .timeline-content {
        background-color: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border-radius: 12px;
        padding: 25px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        width: 45%;
        position: relative;
        transition: all 0.3s ease;
        border-top: 3px solid var(--gold);
      }

      .timeline-content:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
      }

      .timeline-date {
        position: absolute;
        top: -15px;
        background-color: var(--gold);
        color: white;
        padding: 5px 15px;
        border-radius: 20px;
        font-size: 0.9rem;
        font-weight: 600;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
      }

      .timeline-item:nth-child(odd) .timeline-date {
        right: 20px;
      }

      .timeline-item:nth-child(even) .timeline-date {
        left: 20px;
      }

      .timeline-image {
        width: 100%;
        height: 200px;
        border-radius: 8px;
        overflow: hidden;
        margin-bottom: 20px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
      }

      .timeline-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.5s ease;
      }

      .timeline-content:hover .timeline-image img {
        transform: scale(1.05);
      }

      .timeline-content h3 {
        font-size: 24px;
        color: var(--main-green);
        margin-bottom: 15px;
        font-weight: 600;
      }

      .timeline-content p {
        color: var(--dark-gray);
        font-size: 16px;
        line-height: 1.6;
      }

      /* Media Queries */
      @media (max-width: 768px) {
        .hero-title {
          font-size: 42px;
        }

        .hero-subtitle {
          font-size: 18px;
        }

        .section {
          padding: 60px 15px;
        }

        .section-title {
          font-size: 36px;
        }

        /* Timeline Responsive */
        .timeline-item:nth-child(odd) .timeline-content,
        .timeline-item:nth-child(even) .timeline-content {
          margin-left: 0;
          margin-right: 0;
          width: 100%;
          text-align: center;
        }

        .timeline-line {
          left: 20px;
        }

        .timeline-item::before {
          left: 20px;
        }

        .timeline-item:nth-child(odd) .timeline-date,
        .timeline-item:nth-child(even) .timeline-date {
          left: 20px;
          right: auto;
        }

        /* About Section Responsive */
        .about-content {
          grid-template-columns: 1fr;
          gap: 30px;
        }

        .about-image {
          height: 250px;
          max-width: 400px;
          margin: 0 auto;
        }

        /* Region Stories Responsive */
        .region-stories {
          grid-template-columns: 1fr;
          gap: 20px;
        }

        /* DNA Section Responsive */
        .dna-compare {
          grid-template-columns: 1fr;
          gap: 30px;
        }

        /* Sustainability Section Responsive */
        .eco-impact {
          flex-direction: column;
          text-align: center;
        }

        .impact-icon {
          margin: 0 auto 20px;
        }

        /* Philosophy Section Responsive */
        .proverb {
          width: 100%;
        }

        .philosophy-content {
          padding: 20px;
        }
      }

      @media (max-width: 480px) {
        .hero-title {
          font-size: 32px;
        }

        .hero-content {
          padding: 2rem;
        }

        .section-title {
          font-size: 28px;
        }

        .map-container {
          height: 250px;
        }

        .timeline-content h3 {
          font-size: 20px;
        }

        .timeline-image {
          height: 150px;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <!-- Hero Section -->
      <div class="about-hero">
        <!-- Vimeo Video Background -->
        <div
          style="
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 0;
            overflow: hidden;
          "
        >
          <div style="height: 100%; position: relative; width: 100%">
            <!-- Video yüklenene kadar gösterilecek poster görüntüsü -->
            <div
              class="video-poster"
              style="
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: url('https://www.dogalyasam.net/image/catalog/icons/new/thumb.jpg')
                  center/cover no-repeat;
                z-index: 1;
                transition: opacity 3s ease;
              "
            ></div>

            <iframe
              id="heroVideo"
              src="https://player.vimeo.com/video/1086716379?h=583398e7e1&amp;background=1&amp;autoplay=1&amp;loop=1&amp;byline=0&amp;title=0&amp;controls=0&amp;muted=1&amp;dnt=1&amp;quality=auto&amp;app_id=58479&amp;preload=auto"
              frameborder="0"
              allow="autoplay; fullscreen; picture-in-picture; clipboard-write; encrypted-media"
              loading="eager"
              style="
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 100vw;
                height: 56.25vw; /* 16:9 oranı */
                min-height: 100vh;
                min-width: 152vh;
                object-fit: cover;
                z-index: 0;
              "
              title="zeytinyagi-video"
              onload=" setTimeout(() => { this.parentNode.querySelector('.video-poster').style.opacity = '0';}, 4000); setTimeout(() => { this.parentNode.querySelector('.video-poster').style.display = 'none'; }, 4000);"
            ></iframe>
            <!-- Semi-transparent overlay for better text readability -->
            <div
              style="
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: 1;
              "
            ></div>
          </div>
        </div>

        <!-- Fallback image background -->
        <div
          style="
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.3)),
              url('https://www.dogalyasam.net/image/catalog/icons/new/olivePhoto00023.jpg')
                center/cover no-repeat;
            z-index: -1;
          "
        ></div>

        <div class="hero-content" style="z-index: 2">
          <h1 class="hero-title" data-aos="fade-up">Sultan Gazi Doğal Yaşam</h1>
          <p class="hero-subtitle" data-aos="fade-up" data-aos-delay="200">
            Binlerce Yıllık Geleneksel Doğal Yaşamın Ürünleri
          </p>
        </div>
        <div class="scroll-cta" style="z-index: 2">
          <span>Keşfetmek İçin Kaydırın</span>
          <div class="scroll-icon"></div>
        </div>
      </div>

      <!-- Intro Section -->
      <div class="section intro-section">
        <div class="container">
          <div class="intro-content" data-aos="fade-up">
            <p class="intro-text">
              Sultan Gazi E-Ticaret Gıd Trm Bil Rek San Tic Ltd. Şti Firmasının
              tescilli markası olan doğal yaşam, kullanımı ve geçmişi binlerce
              yıla ulaşan geleneksel doğal yaşamın ürünlerinin günümüzün modern
              insanına ulaşmasını sağlıyor. Gelişen teknolojinin ve hızlı
              tüketimin sunduğu kolaylıklar sayesinde sağlıklı yaşamdan yavaş
              yavaş uzaklaşıyoruz. İçinde petrole dayalı çeşitli maddelerin ve
              birçok kimyasalın bulunduğu ürünler, evlerimizi, mutfaklarımızı
              abluka altına almış durumda. Günlük yaşamımızı kolaylaştırdığı
              için zararlarını bilsek de çoğunlukla görmezden geliyoruz.
            </p>
            <p class="intro-text">
              Doğanın bir parçası olan insanoğlu, ondan uzaklaştıkça, kimyasal
              ve elektronik yaşama gömüldükçe, her nesilde sağlığından biraz
              daha uzaklaşıyor. Oysa doğaya saygılı, insanı köklerinden
              koparmayan geleneksel yöntemler, her zaman en sağlıklı yollar
              olarak öne çıkıyor. Önce kendi hayatımıza doğal katkısız ürünleri
              kattık ve kendimizi özel hissettik. Herkes bu duyguyu yaşasın
              diyerek yolculuğumuz başladı. Her ürünü en doğal şekli ile
              herhangi bir katkı eklemeden ya ürettik yada yerinden özenle
              seçerek temin ettik. Yolculuğumuz sizin sofralarınızda son bulur,
              ve sofranız doğanın bir parçası olur.
            </p>
          </div>
        </div>
      </div>

      <!-- About Us Section -->
      <div class="section about-section">
        <div class="title-container">
          <h2 class="section-title" data-aos="fade-up">Biz Kimiz?</h2>
          <p class="section-subtitle" data-aos="fade-up" data-aos-delay="100">
            Doğal ve geleneksel yaşam ilkelerimizden taviz vermeden
          </p>
        </div>

        <div class="about-content" data-aos="fade-up">
          <div class="about-text">
            <p>
              Türkiye'nin dört bir köşesinde o yöreye özgü birçok yöresel ürün
              doğal ve katkısız olarak üretilmekte, yıllardan beri bilinmekte ve
              talep edilmekte ancak birçok sebepten yöresinin dışına
              çıkamamaktadır. www.dogalyasam.net 'in amacı farklı lezzetleri
              denemek isteyenlere, çocukluğunun tatlarını arayanlara en
              kaliteli, en güzel yöresel ürünleri bir araya getirerek sunmaktır.
              Osmaniye merkezli bir firmayız ve Çukurova Hatay sofrası Maras
              yöresi ilk önceliğimiz ve üretimlerimizdir. Ürünlerimiz için
              kullandığımız markamız SULTAN GAZİ ise Wipo onaylı Avrupada 11
              ülke içinde tescilli markamızdır.
            </p>
            <p>
              Doğal Yaşam ürünleri olarak ise kaliteli, güvenilir, dürüst
              ticarete inanan, çevreye duyarlı bir aile işletmesiyiz, şubemiz
              yoktur. Doğal ve geleneksel yaşam ilkelerimizden taviz vermeden,
              emek ve özveri ile işimizi sürdürüyoruz. Çok satış yapma hedefleri
              olmayan butik bir işletmeyiz. Başkaca sektörlerde faaliyet
              göstermekteyiz. Bu işi biraz daha hobi ve fayda mantığı ile
              yapmayı arzu ettik. Hepsi bu kadar. olduğu kadar üretebildiğimiz
              yada bulabildiğimiz kadar dönem ve sezona bağlı olarak güncellenen
              stoklarımız ile yapmaktayız..
            </p>
          </div>
          <div class="about-image" data-aos="fade-left" data-aos-delay="200">
            <img
              src="https://www.dogalyasam.net/image/catalog/icons/new/persinal.jpg"
              alt="Aile İşletmesi"
            />
          </div>
        </div>
      </div>

      <!-- Timeline Section -->
      <div class="section timeline-section">
        <div class="title-container">
          <h2 class="section-title" data-aos="fade-up">Zaman Tüneli</h2>
          <p class="section-subtitle" data-aos="fade-up" data-aos-delay="100">
            M.Ö. 6000'den günümüze zeytinyağı kültürünün yolculuğu
          </p>
        </div>
        <div class="timeline-container">
          <div class="timeline-tree"></div>
          <div class="timeline-line"></div>

          <!-- Timeline Items -->
          <div class="timeline-item" data-aos="fade-up">
            <div class="timeline-content">
              <div class="timeline-date">M.Ö. 6000</div>
              <div class="timeline-image">
                <img
                  src="https://www.dogalyasam.net/image/catalog/icons/new/ancient-olive.jpg"
                  alt="Antik Dönem"
                />
              </div>
              <h3>Hitit Dönemi</h3>
              <p>
                Anadolu'da zeytinyağı üretiminin ilk izleri. Hititler,
                zeytinyağını hem beslenme hem de aydınlatma amaçlı kullanıyordu.
                Arkeolojik kazılarda bulunan zeytinyağı küpleri, bu kadim
                geleneğin kanıtıdır.
              </p>
            </div>
          </div>

          <div class="timeline-item" data-aos="fade-up" data-aos-delay="100">
            <div class="timeline-content">
              <div class="timeline-date">M.Ö. 1000</div>
              <div class="timeline-image">
                <img
                  src="https://www.dogalyasam.net/image/catalog/icons/new/ancient-press.jpg"
                  alt="Antik Pres"
                />
              </div>
              <h3>İlk Zeytinyağı Presleri</h3>
              <p>
                Doğu Akdeniz'de taş presler kullanılarak zeytinyağı üretimi
                yaygınlaştı. Bu dönemde zeytinyağı, değerli bir ticaret malı
                haline geldi ve Akdeniz ticaretinin önemli bir parçası oldu.
              </p>
            </div>
          </div>

          <div class="timeline-item" data-aos="fade-up" data-aos-delay="200">
            <div class="timeline-content">
              <div class="timeline-date">1923</div>
              <div class="timeline-image">
                <img
                  src="https://www.dogalyasam.net/image/catalog/icons/new/republic-era.jpg"
                  alt="Cumhuriyet Dönemi"
                />
              </div>
              <h3>Cumhuriyet Dönemi</h3>
              <p>
                Türkiye Cumhuriyeti'nin kuruluşuyla birlikte zeytincilik
                desteklendi ve modern üretim teknikleri geliştirildi.
                Anadolu'nun bereketli topraklarında zeytin ağaçları çoğaldı.
              </p>
            </div>
          </div>

          <div class="timeline-item" data-aos="fade-up" data-aos-delay="300">
            <div class="timeline-content">
              <div class="timeline-date">2024</div>
              <div class="timeline-image">
                <img
                  src="https://www.dogalyasam.net/image/catalog/icons/new/modern-production.jpg"
                  alt="Modern Üretim"
                />
              </div>
              <h3>Sultan Gazi Doğal Yaşam</h3>
              <p>
                Geleneksel yöntemlerle modern teknolojinin birleşimi. Soğuk
                sıkım tekniğiyle üretilen, erken hasat zeytinyağlarımız, bin
                yıllık geleneği günümüze taşıyor. Amanos Dağları'nın
                eteklerindeki bahçelerimizden sofranıza.
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Geographic Map Section -->
      <div class="section map-section">
        <div class="title-container">
          <h2 class="section-title" data-aos="fade-up">Coğrafi Kökenimiz</h2>
          <p class="section-subtitle" data-aos="fade-up" data-aos-delay="100">
            Zeytinliklerimiz Osmaniye'nin Akyar bölgesinde, Amanos Dağları'nın
            eteklerinde yer almaktadır
          </p>
        </div>

        <div class="map-container" data-aos="fade-up" data-aos-delay="200">
          <div id="turkeyMap" class="turkey-map">
            <!-- SVG haritası buraya eklenecek -->
            <img
              src="https://www.dogalyasam.net/image/catalog/icons/new/olivePhoto00027.jpg"
              alt=""
            />
            <!-- Pin marker -->
            <div id="mapPin" class="map-pin">
              <div class="pin-head"></div>
              <div class="pin-label">Bahçemiz burada</div>
            </div>
          </div>
        </div>

        <div class="region-stories" data-aos="fade-up">
          <div
            class="region-story-card"
            data-aos="fade-up"
            data-aos-delay="100"
          >
            <div class="region-icon">
              <img
                src="https://www.dogalyasam.net/image/catalog/icons/new/osmaniye-icon.png"
                alt="Osmaniye"
              />
            </div>
            <h3>Osmaniye</h3>
            <p>
              Amanos Dağları'nın eteklerinde, Akdeniz ikliminin tüm
              nimetlerinden faydalanan zeytinliklerimiz, eşsiz bir terroir'a
              sahiptir. Burada yetişen zeytinler, mineral açısından zengin
              toprak ve temiz hava sayesinde benzersiz bir aroma profiline
              kavuşur.
            </p>
          </div>

          <div
            class="region-story-card"
            data-aos="fade-up"
            data-aos-delay="200"
          >
            <div class="region-icon">
              <img
                src="https://www.dogalyasam.net/image/catalog/icons/new/hatay-icon.png"
                alt="Hatay"
              />
            </div>
            <h3>Hatay</h3>
            <p>
              Medeniyetlerin beşiği Hatay'ın zengin gastronomik mirası,
              zeytinyağı kültürümüzün temelini oluşturur. Yüzyıllardır süregelen
              geleneksel tarifler ve pişirme teknikleri, ürünlerimizin kullanım
              alanlarını şekillendirir.
            </p>
          </div>

          <div
            class="region-story-card"
            data-aos="fade-up"
            data-aos-delay="300"
          >
            <div class="region-icon">
              <img
                src="https://www.dogalyasam.net/image/catalog/icons/new/kahramanmaras-icon.png"
                alt="Kahramanmaraş"
              />
            </div>
            <h3>Kahramanmaraş</h3>
            <p>
              Kahramanmaraş'ın köklü el sanatları geleneği, ambalajlarımızın
              tasarımından üretim sürecindeki el işçiliğine kadar her aşamada
              hissedilir. Yerel ustalarla işbirliği yaparak, geleneksel
              değerleri modern dünyaya taşıyoruz.
            </p>
          </div>
        </div>
      </div>

      <!-- Deep Storytelling Section 1 -->
      <div class="section story-section dna-section">
        <div class="title-container">
          <h2 class="section-title" data-aos="fade-up">
            Kaybolan Lezzet Genleri
          </h2>
          <p class="section-subtitle" data-aos="fade-up" data-aos-delay="100">
            Binlerce yıllık geleneksel beslenme şeklinin genetik mirasımıza
            karşı bir sorumluluk olduğuna inanıyoruz
          </p>
        </div>

        <div class="dna-visualization" data-aos="fade-up">
          <div class="dna-strand">
            <div class="dna-compare">
              <div class="dna-column">
                <h3>Geleneksel Yöntemler</h3>
                <ul class="dna-list">
                  <li>El değirmeni ile öğütme</li>
                  <li>Soğuk sıkım yöntemi</li>
                  <li>Yerel ürün çeşitleri</li>
                  <li>Doğal fermantasyon</li>
                  <li>Taş baskı sistemleri</li>
                </ul>
              </div>
              <div class="dna-column">
                <h3>Modern Endüstri</h3>
                <ul class="dna-list modern">
                  <li>Endüstriyel üretim</li>
                  <li>Yüksek sıcaklıkta işleme</li>
                  <li>Hibrit türler</li>
                  <li>Kimyasal katkılar</li>
                  <li>Yapay koruyucular</li>
                </ul>
              </div>
            </div>
            <div class="dna-highlight">
              <p>
                Doğanın bir parçası olan insanoğlu, ondan uzaklaştıkça, kimyasal
                ve elektronik yaşama gömüldükçe, her nesilde sağlığından biraz
                daha uzaklaşıyor. Oysa doğaya saygılı, insanı köklerinden
                koparmayan geleneksel yöntemler, her zaman en sağlıklı yollar
                olarak öne çıkıyor.
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Deep Storytelling Section 2 -->
      <div class="section story-section family-section">
        <div class="title-container">
          <h2 class="section-title" data-aos="fade-up">
            Aile Sofrasından Evrensel Mirasa
          </h2>
          <p class="section-subtitle" data-aos="fade-up" data-aos-delay="100">
            Üç kuşaktır devam eden zeytinyağı üretim geleneğimiz
          </p>
        </div>

        <div class="family-story" data-aos="fade-up">
          <div class="video-container">
            <video
              controls
              poster="https://www.dogalyasam.net/image/catalog/icons/new/family-production-poster.jpg"
            >
              <source
                src="https://www.dogalyasam.net/image/catalog/video/family-production.mp4"
                type="video/mp4"
              />
              <!-- Fallback image if video doesn't load -->
              <img
                src="https://www.dogalyasam.net/image/catalog/icons/new/family-production-poster.jpg"
                alt="Aile Üretimi"
              />
            </video>
          </div>

          <div class="family-generations">
            <div class="generation" data-aos="fade-up" data-aos-delay="100">
              <div class="generation-photo">
                <img
                  src="https://www.dogalyasam.net/image/catalog/icons/new/grandfather.jpg"
                  alt="Dede"
                />
              </div>
              <h3>Dede: Geleneksel Bilgelik</h3>
              <p>
                Taş değirmende, sabırla ve özenle zeytinyağı üretiminin
                inceliklerini öğrendi. Toprağın dilini, zeytinin karakterini ve
                hasat zamanının önemini bilen bir usta.
              </p>
            </div>

            <div class="generation" data-aos="fade-up" data-aos-delay="200">
              <div class="generation-photo">
                <img
                  src="https://www.dogalyasam.net/image/catalog/icons/new/father.jpg"
                  alt="Baba"
                />
              </div>
              <h3>Baba: Yenilikçi Yaklaşım</h3>
              <p>
                Geleneksel yöntemleri modern tekniklerle birleştirerek üretim
                sürecini geliştirdi. Kalite standartlarını yükselterek,
                zeytinyağımızı ulusal pazara taşıdı.
              </p>
            </div>

            <div class="generation" data-aos="fade-up" data-aos-delay="300">
              <div class="generation-photo">
                <img
                  src="https://www.dogalyasam.net/image/catalog/icons/new/grandson.jpg"
                  alt="Torun"
                />
              </div>
              <h3>Torun: Dijital Dönüşüm</h3>
              <p>
                Aile mirasını dijital dünyaya taşıyarak, geleneksel değerleri
                modern tüketiciyle buluşturuyor. Sürdürülebilirlik ve organik
                tarım konularında yeni yaklaşımlar getiriyor.
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Sustainability Section -->
      <div class="section sustainability-section">
        <div class="title-container">
          <h2 class="section-title" data-aos="fade-up">
            Sürdürülebilir Gelecek
          </h2>
          <p class="section-subtitle" data-aos="fade-up" data-aos-delay="100">
            Sağlıklı Yaşam Doğada
          </p>
        </div>

        <div class="sustainability-infographic" data-aos="fade-up">
          <div class="eco-impact">
            <div class="impact-icon">
              <img
                src="https://www.dogalyasam.net/image/catalog/icons/new/tree-icon.png"
                alt="Ağaç"
              />
            </div>
            <div class="impact-text">
              <h3>Doğaya Saygılı Üretim</h3>
              <p>
                Doğaya saygılı, insanı köklerinden koparmayan geleneksel
                yöntemler, her zaman en sağlıklı yollar olarak öne çıkıyor. Önce
                kendi hayatımıza doğal katkısız ürünleri kattık ve kendimizi
                özel hissettik. Herkes bu duyguyu yaşasın diyerek yolculuğumuz
                başladı.
              </p>
            </div>
          </div>

          <div class="eco-practices" data-aos="fade-up" data-aos-delay="200">
            <h3>Sürdürülebilir Uygulamalarımız</h3>
            <ul class="eco-list">
              <li>
                <span class="eco-icon">♻️</span>
                <span class="eco-text"
                  >Geri dönüştürülebilir ambalaj kullanımı</span
                >
              </li>
              <li>
                <span class="eco-icon">💧</span>
                <span class="eco-text"
                  >Damla sulama sistemi ile su tasarrufu</span
                >
              </li>
              <li>
                <span class="eco-icon">🌱</span>
                <span class="eco-text">Organik tarım uygulamaları</span>
              </li>
              <li>
                <span class="eco-icon">🔄</span>
                <span class="eco-text"
                  >Doğal atıkların gübre olarak değerlendirilmesi</span
                >
              </li>
            </ul>
          </div>

          <div class="eco-impact" data-aos="fade-up" data-aos-delay="300">
            <div class="impact-icon">
              <img
                src="https://www.dogalyasam.net/image/catalog/icons/new/health-icon.png"
                alt="Sağlık"
              />
            </div>
            <div class="impact-text">
              <h3>Sağlıklı Yaşam</h3>
              <p>
                Sağlıklı Yaşam Doğada diyor, binlerce yıllık geleneksel beslenme
                şeklinin genetik mirasımıza karşı bir sorumluluk olduğuna
                inanıyoruz. İhtiyarlamadan yaşlanarak konforlu kalalım,
                Sağlıcakla..
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Philosophy Corner -->
      <div class="section philosophy-section">
        <div class="title-container">
          <h2 class="section-title" data-aos="fade-up">Felsefe Köşemiz</h2>
          <p class="section-subtitle" data-aos="fade-up" data-aos-delay="100">
            Atalarımızdan miras kalan bilgelik
          </p>
        </div>

        <div class="proverb-wall" data-aos="fade-up">
          <div class="proverb" data-aos="fade-up" data-aos-delay="100">
            <p>"Ekşi yoğurt çalkalanmakla tatlı olmaz."</p>
          </div>
          <div class="proverb" data-aos="fade-up" data-aos-delay="200">
            <p>"Ağaç yaşken eğilir."</p>
          </div>
          <div class="proverb" data-aos="fade-up" data-aos-delay="300">
            <p>"Damlaya damlaya göl olur."</p>
          </div>
          <div class="proverb" data-aos="fade-up" data-aos-delay="400">
            <p>"Sabır acıdır, meyvesi tatlıdır."</p>
          </div>
          <div class="proverb" data-aos="fade-up" data-aos-delay="500">
            <p>"Bir elin nesi var, iki elin sesi var."</p>
          </div>
        </div>

        <div class="philosophy-content" data-aos="fade-up" data-aos-delay="300">
          <p class="philosophy-text">
            Her ürünü en doğal şekli ile herhangi bir katkı eklemeden ya ürettik
            yada yerinden özenle seçerek temin ettik. Yolculuğumuz sizin
            sofralarınızda son bulur, ve sofranız doğanın bir parçası olur.
          </p>
          <p class="philosophy-text">
            Sağlıklı Yaşam Doğada diyor, binlerce yıllık geleneksel beslenme
            şeklinin genetik mirasımıza karşı bir sorumluluk olduğuna
            inanıyoruz. İhtiyarlamadan yaşlanarak konforlu kalalım.
          </p>
          <p class="philosophy-text signature">Sağlıcakla..</p>
        </div>

        <!-- Easter Egg - Hidden Recipe -->
        <div class="easter-egg">
          <span class="hidden-letter" data-letter="S">S</span>
          <span class="hidden-letter" data-letter="A">A</span>
          <span class="hidden-letter" data-letter="Ğ">Ğ</span>
          <span class="hidden-letter" data-letter="L">L</span>
          <span class="hidden-letter" data-letter="I">I</span>
          <span class="hidden-letter" data-letter="K">K</span>
        </div>
      </div>
    </div>
  </body>
  <!-- Scripts -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.js"></script>
  <!-- Video yükleme işlemini yönetmek için geliştirilmiş script -->
  <script>
    document.addEventListener("DOMContentLoaded", function () {
      var videoFrame = document.getElementById("heroVideo");
      var videoPoster = document.querySelector(".video-poster");
      var videoReady = false;
      var minDisplayTime = 2000; // Poster'ın minimum görünme süresi (ms)
      var startTime = new Date().getTime();

      if (videoFrame && videoPoster) {
        // Video iframe'i yüklendiğinde
        videoFrame.addEventListener("load", function () {
          console.log("Video iframe yüklendi");

          // Video hazır olduğunda kontrol et
          checkVideoAndHidePoster();

          // Periyodik olarak video durumunu kontrol et
          var checkInterval = setInterval(function () {
            if (videoReady) {
              clearInterval(checkInterval);
            } else {
              checkVideoAndHidePoster();
            }
          }, 500);
        });

        // Video hazır olduğunda poster'ı gizle
        function checkVideoAndHidePoster() {
          // Cross-origin kısıtlamaları nedeniyle video içeriğini doğrudan kontrol edemeyiz
          // Bu nedenle iframe'in yüklenme durumunu ve zaman aşımını kullanıyoruz

          // iframe yüklendikten sonra belirli bir süre bekle
          var currentTime = new Date().getTime();
          var elapsedTime = currentTime - startTime;

          // Video yüklendiyse ve minimum süre geçtiyse
          if (elapsedTime >= 4000) {
            // 4 saniye bekle - video yüklenmesi için yeterli süre
            videoReady = true;
            hidePoster();
          }
        }

        // Poster'ı yumuşak geçişle gizle
        function hidePoster() {
          if (videoPoster.style.opacity !== "0") {
            videoPoster.style.opacity = "0";
            setTimeout(function () {
              videoPoster.style.display = "none";
            }, 800); // Geçiş süresini uzattık
          }
        }

        // Güvenlik önlemi: 10 saniye sonra poster'ı her durumda gizle
        setTimeout(function () {
          if (!videoReady) {
            console.log(
              "Video 10 saniye içinde hazır olmadı, poster'ı gizliyoruz"
            );
            videoReady = true;
            hidePoster();
          }
        }, 10000);
      }
    });
  </script>
  <script>
    // Defer non-critical JavaScript execution
    window.addEventListener("load", function () {
      // Initialize AOS with optimized settings - disable on mobile for better performance
      AOS.init({
        duration: 800,
        easing: "ease-out",
        once: true,
        offset: 50,
        delay: 0,
        disable: window.innerWidth < 768,
      });
    });

    document.addEventListener("DOMContentLoaded", function () {
      // Smooth scroll for scroll CTA
      const scrollCta = document.querySelector(".scroll-cta");
      if (scrollCta) {
        scrollCta.addEventListener("click", function () {
          const introSection = document.querySelector(".intro-section");
          if (introSection) {
            window.scrollTo({
              top: introSection.offsetTop,
              behavior: "smooth",
            });
          }
        });
      }

      // Easter Egg - Hidden Recipe - Lazy initialize
      const easterEggInit = function () {
        const hiddenLetters = document.querySelectorAll(".hidden-letter");
        let clickedLetters = [];
        const secretWord = "SAĞLIK";

        if (hiddenLetters.length > 0) {
          hiddenLetters.forEach((letter) => {
            letter.addEventListener("click", function () {
              const letterValue = this.getAttribute("data-letter");
              clickedLetters.push(letterValue);

              // Highlight clicked letter
              this.style.color = "var(--gold)";
              this.style.opacity = "1";

              // Check if secret word is complete
              if (clickedLetters.join("") === secretWord) {
                // Reset for next attempt
                clickedLetters = [];

                // Reset letter styling
                hiddenLetters.forEach((l) => {
                  l.style.color = "var(--dark-gray)";
                  l.style.opacity = "0.1";
                });

                // Show recipe PDF
                showNotification("Geleneksel tarif bulundu! İndiriliyor...");

                // Simulate download (in real implementation, link to actual PDF)
                setTimeout(() => {
                  window.open(
                    "https://www.dogalyasam.net/image/catalog/pdf/geleneksel-tarif.pdf",
                    "_blank"
                  );
                }, 1000);
              }
            });
          });
        }
      };

      // Lazy initialize easter egg when user scrolls near the section
      const observer = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              easterEggInit();
              observer.disconnect(); // Only initialize once
            }
          });
        },
        { threshold: 0.1 }
      );

      const philosophySection = document.querySelector(".philosophy-section");
      if (philosophySection) {
        observer.observe(philosophySection);
      }

      // Helper function for notifications
      function showNotification(message) {
        const notification = document.createElement("div");
        notification.className = "ios-notification";
        notification.innerHTML = `
            <div class="notification-content">
              <div class="notification-icon">🔔</div>
              <div class="notification-message">${message}</div>
            </div>
          `;

        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
          notification.classList.add("show");
        }, 100);

        // Remove after delay
        setTimeout(() => {
          notification.classList.remove("show");
          setTimeout(() => {
            document.body.removeChild(notification);
          }, 500);
        }, 3000);
      }
    });
  </script>
</html>
