<!DOCTYPE html>
<html lang="tr">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title><PERSON><PERSON><PERSON>gor<PERSON></title>
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <style>
      /* Reset and Base Styles */
      .naturel-modern * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      .naturel-modern {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
        line-height: 1.6;
        color: #374151;
        background: linear-gradient(
          135deg,
          #f5f5dc 0%,
          #fff8dc 50%,
          #fffacd 100%
        );
        min-height: 100vh;
        padding: 40px 0;
      }

      /* Container */
      .naturel-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 16px;
      }

      /* Header */
      .naturel-header {
        text-align: center;
        margin-bottom: 48px;
      }

      .naturel-title {
        font-size: 48px;
        font-weight: bold;
        color: #1f2937;
        margin-bottom: 16px;
      }

      .naturel-highlight {
        background: linear-gradient(45deg, #8b9a5b, #a4b494);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      .naturel-subtitle {
        font-size: 18px;
        color: #6b7280;
        max-width: 600px;
        margin: 0 auto 24px;
      }

      .naturel-divider {
        width: 80px;
        height: 4px;
        background: linear-gradient(
          135deg,
          #8b9a5b 0%,
          #a4b494 50%,
          #c8d5b9 100%
        );
        margin: 0 auto;
        border-radius: 2px;
      }

      /* Info Section */
      .naturel-info-section {
        background: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(12px);
        border-radius: 24px;
        padding: 40px;
        margin-bottom: 48px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
      }

      .naturel-info-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 40px;
        align-items: center;
      }

      .naturel-info-content h2 {
        font-size: 32px;
        font-weight: bold;
        color: #1f2937;
        margin-bottom: 16px;
      }

      .naturel-info-content p {
        color: #6b7280;
        font-size: 16px;
        line-height: 1.7;
        margin-bottom: 24px;
      }

      .naturel-highlight-box {
        background: linear-gradient(
          135deg,
          rgba(139, 154, 91, 0.1) 0%,
          rgba(164, 180, 148, 0.1) 100%
        );
        border: 2px solid rgba(139, 154, 91, 0.2);
        border-radius: 16px;
        padding: 20px;
        margin-bottom: 24px;
      }

      .naturel-highlight-box h3 {
        color: #8b9a5b;
        font-weight: bold;
        margin-bottom: 8px;
        font-size: 18px;
      }

      .naturel-highlight-box p {
        color: #374151;
        margin: 0;
        font-size: 14px;
      }

      .naturel-stats {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 20px;
        margin-top: 24px;
      }

      .naturel-stat {
        text-align: center;
        padding: 16px;
        background: rgba(255, 255, 255, 0.6);
        border-radius: 12px;
      }

      .naturel-stat-number {
        font-size: 24px;
        font-weight: bold;
        color: #8b9a5b;
        display: block;
      }

      .naturel-stat-label {
        font-size: 12px;
        color: #6b7280;
        margin-top: 4px;
      }

      .naturel-info-image {
        position: relative;
      }

      .naturel-info-image img {
        width: 100%;
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      }

      .naturel-floating-badge {
        position: absolute;
        top: -10px;
        right: -10px;
        background: linear-gradient(135deg, #8b9a5b 0%, #a4b494 100%);
        color: white;
        padding: 12px 20px;
        border-radius: 20px;
        font-weight: bold;
        font-size: 14px;
        box-shadow: 0 8px 20px rgba(139, 154, 91, 0.3);
      }

      /* Usage Areas */
      .naturel-usage-section {
        margin: 48px 0;
      }

      .naturel-usage-title {
        text-align: center;
        font-size: 28px;
        font-weight: bold;
        color: #1f2937;
        margin-bottom: 32px;
      }

      .naturel-usage-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 24px;
      }

      .naturel-usage-card {
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(8px);
        border-radius: 20px;
        padding: 24px;
        text-align: center;
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
        border: 2px solid transparent;
      }

      .naturel-usage-card:hover {
        transform: translateY(-5px);
        border-color: rgba(139, 154, 91, 0.3);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
      }

      .naturel-usage-icon {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, #8b9a5b 0%, #a4b494 100%);
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 16px;
        transform: rotate(8deg);
      }

      .naturel-usage-icon i {
        color: white;
        font-size: 24px;
        transform: rotate(-8deg);
      }

      .naturel-usage-card h3 {
        font-size: 18px;
        font-weight: bold;
        color: #1f2937;
        margin-bottom: 8px;
      }

      .naturel-usage-card p {
        color: #6b7280;
        font-size: 14px;
        line-height: 1.5;
      }

      /* CTA Section */
      .naturel-cta-section {
        background: linear-gradient(135deg, #8b9a5b 0%, #a4b494 100%);
        border-radius: 24px;
        padding: 40px;
        text-align: center;
        color: white;
        margin-top: 48px;
      }

      .naturel-cta-title {
        font-size: 32px;
        font-weight: bold;
        margin-bottom: 16px;
      }

      .naturel-cta-subtitle {
        font-size: 18px;
        opacity: 0.9;
        margin-bottom: 32px;
      }

      .naturel-cta-buttons {
        display: flex;
        gap: 16px;
        justify-content: center;
        flex-wrap: wrap;
      }

      .naturel-btn-primary {
        background: white;
        color: #8b9a5b;
        border: none;
        padding: 16px 32px;
        border-radius: 16px;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 16px;
      }

      .naturel-btn-primary:hover {
        transform: translateY(-3px);
        box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);
      }

      .naturel-btn-secondary {
        background: transparent;
        color: white;
        border: 2px solid white;
        padding: 14px 30px;
        border-radius: 16px;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 16px;
      }

      .naturel-btn-secondary:hover {
        background: white;
        color: #8b9a5b;
      }

      /* Animations */
      @keyframes naturelFadeIn {
        from {
          opacity: 0;
          transform: translateY(30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .naturel-fade-in {
        animation: naturelFadeIn 0.6s ease-out forwards;
      }

      .naturel-delay-1 {
        animation-delay: 0.1s;
      }
      .naturel-delay-2 {
        animation-delay: 0.2s;
      }
      .naturel-delay-3 {
        animation-delay: 0.3s;
      }
      .naturel-delay-4 {
        animation-delay: 0.4s;
      }

      /* Responsive */
      @media (max-width: 1024px) {
        .naturel-info-grid {
          grid-template-columns: 1fr;
          gap: 32px;
        }

        .naturel-usage-grid {
          grid-template-columns: 1fr;
          gap: 24px;
        }
      }

      @media (max-width: 768px) {
        .naturel-title {
          font-size: 36px;
        }

        .naturel-subtitle {
          font-size: 16px;
        }

        .naturel-info-section {
          padding: 24px;
        }

        .naturel-info-content h2 {
          font-size: 24px;
        }

        .naturel-stats {
          grid-template-columns: 1fr;
          gap: 16px;
        }
      }
    </style>
  </head>
  <body>
    <div class="naturel-modern">
      <div class="naturel-container">
        <!-- Category Header -->
        <div class="naturel-header naturel-fade-in">
          <h1 class="naturel-title">
            <span class="naturel-highlight">Natürel Birinci</span> Zeytinyağı
          </h1>
          <div class="naturel-divider"></div>
          <p class="naturel-subtitle">
            Amanos Dağları'ndan gelen doğal lezzet. Olgun zeytinlerden elde
            edilen, günlük mutfak kullanımı için ideal zeytinyağı kategorisi.
          </p>
        </div>

        <!-- Category Info Section -->
        <div class="naturel-info-section naturel-fade-in naturel-delay-1">
          <div class="naturel-info-grid">
            <div class="naturel-info-content">
              <h2>
                Amanos Dağları'ndan
                <span class="naturel-highlight">Doğal Lezzet</span>
              </h2>
              <p>
                Natürel Birinci Zeytinyağımız, Amanos Dağları'nın eteklerinde
                yetişen zeytin ağaçlarımızdan, Kasım-Aralık aylarında özenle
                toplanan olgun zeytinlerle elde edilir.
              </p>

              <div class="naturel-highlight-box">
                <h3>Özel Üretim Süreci</h3>
                <p>
                  Zeytinler, geleneksel yöntemlerle, hiçbir kimyasal işlem
                  uygulanmadan 35°C ortamda, katkısız ve doğal sıkım yöntemiyle
                  yağa dönüştürülür.
                </p>
              </div>

              <div class="naturel-stats">
                <div class="naturel-stat">
                  <span class="naturel-stat-number">%0.8-2.0</span>
                  <div class="naturel-stat-label">Asitlik Oranı</div>
                </div>
                <div class="naturel-stat">
                  <span class="naturel-stat-number">35°C</span>
                  <div class="naturel-stat-label">Sıkım Sıcaklığı</div>
                </div>
                <div class="naturel-stat">
                  <span class="naturel-stat-number">%100</span>
                  <div class="naturel-stat-label">Doğal</div>
                </div>
              </div>
            </div>

            <div class="naturel-info-image">
              <img
                src="https://images.unsplash.com/photo-1474979266404-7eaacbcd87c5?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                alt="Natürel Birinci Zeytinyağı"
              />
              <div class="naturel-floating-badge">Olgun Hasat</div>
            </div>
          </div>
        </div>

        <!-- Taste Profile Section -->
        <div class="naturel-info-section naturel-fade-in naturel-delay-2">
          <div class="naturel-info-grid">
            <div class="naturel-info-image">
              <img
                src="https://images.unsplash.com/photo-1596040033229-a9821ebd058d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                alt="Dengeli Tat Profili"
              />
              <div class="naturel-floating-badge">Zengin Aroma</div>
            </div>

            <div class="naturel-info-content">
              <h2>
                <span class="naturel-highlight">Dengeli Tat</span>, Zengin Aroma
              </h2>
              <p>
                %0.8 – %2.0 arası asitlik oranına sahip olan bu özel
                zeytinyağımız; hafif meyvemsi, yumuşak ve dengeli aromasıyla ön
                plana çıkar.
              </p>

              <div class="naturel-highlight-box">
                <h3>Tat Notaları</h3>
                <p>
                  Çağla, badem ve çimen notalarıyla zenginleşen bu tat profili,
                  hem lezzet hem de kalite arayanlara hitap eder. Diğer
                  zeytinyağlarımıza göre daha kıvamlı yapısı, daha yoğun bir
                  aromayla birleşir.
                </p>
              </div>

              <div class="naturel-stats">
                <div class="naturel-stat">
                  <span class="naturel-stat-number">Çağla</span>
                  <div class="naturel-stat-label">Tat Notu</div>
                </div>
                <div class="naturel-stat">
                  <span class="naturel-stat-number">Badem</span>
                  <div class="naturel-stat-label">Aroma</div>
                </div>
                <div class="naturel-stat">
                  <span class="naturel-stat-number">Çimen</span>
                  <div class="naturel-stat-label">Son Tat</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Usage Areas -->
        <div class="naturel-usage-section naturel-fade-in naturel-delay-3">
          <h2 class="naturel-usage-title">
            <span class="naturel-highlight">Kullanım</span> Alanları
          </h2>

          <div class="naturel-usage-grid">
            <div class="naturel-usage-card">
              <div class="naturel-usage-icon">
                <i class="fas fa-fire"></i>
              </div>
              <h3>Sıcak Yemeklerde</h3>
              <p>
                Dengeli aromasıyla sıcak yemeklerinizde mükemmel uyum sağlar.
                Asit oranının sağladığı yumuşak içimiyle lezzet dengesi yaratır.
              </p>
            </div>

            <div class="naturel-usage-card">
              <div class="naturel-usage-icon">
                <i class="fas fa-utensils"></i>
              </div>
              <h3>Kızartmalarda</h3>
              <p>
                Kıvamlı yapısı sayesinde kızartmalarda ideal performans
                gösterir. Yoğun aromasıyla yemeklerinize derinlik katar.
              </p>
            </div>

            <div class="naturel-usage-card">
              <div class="naturel-usage-icon">
                <i class="fas fa-leaf"></i>
              </div>
              <h3>Salatalarda</h3>
              <p>
                Çağla, badem ve çimen notalarıyla salatalarınıza özel bir tat
                katar. Sızma zeytinyağlarına kıyasla daha sade ve dengeli
                lezzet.
              </p>
            </div>
          </div>
        </div>

        <!-- CTA Section -->
        <div class="naturel-cta-section naturel-fade-in naturel-delay-4">
          <h2 class="naturel-cta-title">
            Natürel Birinci Zeytinyağı'nı Keşfedin
          </h2>
          <p class="naturel-cta-subtitle">
            Günlük mutfak kullanımı için oldukça uygun olan zeytinyağı
            çeşitlerimizi inceleyin
          </p>
          <div class="naturel-cta-buttons">
            <button class="naturel-btn-primary">
              <i class="fas fa-shopping-cart" style="margin-right: 8px"></i>
              Ürünleri İncele
            </button>
            <button class="naturel-btn-secondary">
              <i class="fas fa-phone" style="margin-right: 8px"></i>
              İletişime Geç
            </button>
          </div>
        </div>
      </div>
    </div>

    <script>
      // Intersection Observer for animations
      const naturelObserver = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              entry.target.style.opacity = "1";
              entry.target.style.transform = "translateY(0)";
            }
          });
        },
        { threshold: 0.1 }
      );

      // Observe all animated elements
      document.querySelectorAll(".naturel-fade-in").forEach((el) => {
        el.style.opacity = "0";
        el.style.transform = "translateY(30px)";
        naturelObserver.observe(el);
      });
    </script>
  </body>
</html>
