<!DOCTYPE html>
<html lang="tr">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></title>
    <!-- FontAwesome İkonları için -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <style>
      /* ------------------------------------------------------------
       1. <PERSON><PERSON> (Reset) ve Body Ayarları
    ------------------------------------------------------------ */
      * {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
      }
      html,
      body {
        height: 100%;
      }
      body {
        font-family: Arial, sans-serif;
        background-color: #f3f4f6; /* Tailwind bg-gray-100 eşdeğeri */
        color: #1f2937; /* Genel metin rengi (iste<PERSON>e ba<PERSON>l<PERSON>) */
      }

      /* ------------------------------------------------------------
       2. Animasyon Tanımları
    ------------------------------------------------------------ */
      @keyframes fadeIn {
        from {
          opacity: 0;
        }
        to {
          opacity: 1;
        }
      }

      @keyframes slideUp {
        from {
          transform: translateY(20px);
          opacity: 0;
        }
        to {
          transform: translateY(0);
          opacity: 1;
        }
      }

      @keyframes pulse {
        0% {
          transform: scale(1) rotate(15deg);
        }
        50% {
          transform: scale(1.05) rotate(15deg);
        }
        100% {
          transform: scale(1) rotate(15deg);
        }
      }

      /* ------------------------------------------------------------
       3. Gizleme-Saklama (Hidden)
    ------------------------------------------------------------ */
      .hidden {
        display: none !important;
      }

      /* ------------------------------------------------------------
       4. Popup Overlay ve İçeriği
    ------------------------------------------------------------ */
      .popup-overlay {
        position: fixed;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        z-index: 1000;
        display: flex;
        align-items: center;
        justify-content: center;
        animation: fadeIn 0.3s ease-out;
        background-color: rgba(0, 0, 0, 0.5);
      }
      .popup-content {
        position: relative;
        width: 100%;
        max-width: 448px; /* Tailwind max-w-md = 28rem = 448px */
        margin: 0 16px; /* Tailwind mx-4 */
        background-color: #ffffff;
        border-radius: 12px; /* Tailwind rounded-xl = 0.75rem ~ 12px */
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25); /* Yaklaşık Tailwind shadow-2xl */
        overflow: hidden;
        animation: slideUp 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
      }

      /* ------------------------------------------------------------
       5. Zeytin Dalı (Dekoratif İkonlar)
    ------------------------------------------------------------ */
      .olive-branch {
        position: absolute;
        width: 120px;
        height: 120px;
        opacity: 0.1;
        z-index: 0;
        color: #047857; /* Tailwind text-green-700 eşdeğeri */
      }
      .olive-branch.left {
        top: 10%;
        left: 5%;
        transform: rotate(-15deg);
      }
      .olive-branch.right {
        bottom: 10%;
        right: 5%;
        transform: rotate(15deg);
      }

      /* ------------------------------------------------------------
       6. İndirim Rozeti
    ------------------------------------------------------------ */
      .discount-badge img {
        position: absolute;
        top: -10px;
        right: -10px;
        width: 100px;
        height: 100px;
        border-radius: 50%;
        color: #ffffff;
        font-weight: bold;
        font-size: 18px; /* Yaklaşık Tailwind text-lg */
        display: flex;
        align-items: center;
        justify-content: center;
        transform: rotate(15deg);
        animation: pulse 2s infinite;
        z-index: 10;
      }

      /* ------------------------------------------------------------
       7. Popup Başlık & Metin Stilleri
    ------------------------------------------------------------ */
      .popup-header {
        text-align: center;
        margin-bottom: 24px; /* Tailwind mb-6 = 1.5rem = 24px */
        position: relative;
        z-index: 10;
      }
      .popup-title {
        font-size: 30px; /* Tailwind text-3xl = 1.875rem = 30px */
        line-height: 36px; /* Tailwind leading-9 = 2.25rem = 36px */
        font-weight: bold;
        color: #065f46; /* Tailwind text-green-800 */
        margin-bottom: 8px; /* Yaklaşık Tailwind mb-2 */
      }
      .popup-subtitle {
        font-size: 16px; /* Varsayılan paragraf */
        color: #4b5563; /* Tailwind text-gray-600 */
      }

      /* ------------------------------------------------------------
       8. Video Konteyneri
    ------------------------------------------------------------ */
      .video-container {
        position: relative;
        display: flex;
        justify-content: center;
        min-height: 300px; /* iframe yerini korumak için (isteğe bağlı) */
      }
      .video-container iframe {
        object-fit: cover;
        position: absolute;
        top: -106px;
        width: 500px;
        height: 500px;
        border: none;
        z-index: 0;
      }

      /* ------------------------------------------------------------
       9. Bilgi Kutusu (Alert Box)
    ------------------------------------------------------------ */
      .info-box {
        background-color: #fffbeb; /* Tailwind bg-amber-50 */
        border-left: 4px solid #f59e0b; /* Tailwind border-amber-400 */
        padding: 16px; /* Tailwind p-4 */
        margin-bottom: 24px; /* Tailwind mb-6 */
        display: flex;
        align-items: flex-start;
        position: relative;
        z-index: 10;
      }
      .info-icon {
        color: #f59e0b; /* Tailwind text-amber-500 */
        font-size: 18px; /* Yaklaşık Tailwind text-xl */
        margin-right: 12px; /* Tailwind ml-3 için horizontal ayar */
        flex-shrink: 0;
      }
      .info-text {
        font-size: 15px; /* Tailwind text-sm */
        color: #b45309; /* Tailwind text-amber-700 */
        line-height: 18px; /* Yaklaşık leading-5 = 1.25rem */
      }
      .info-text b {
        font-weight: bold;
      }

      /* ------------------------------------------------------------
       10. Buton Konteyneri ve Buton Stilleri
    ------------------------------------------------------------ */
      .button-container {
        display: flex;
        flex-direction: column;
        gap: 12px; /* Tailwind space-y-3 = 0.75rem = 12px */
        position: relative;
        z-index: 10;
      }
      .button-container a {
        display: contents;
      }
      .button-primary {
        background-color: #047857; /* Tailwind bg-green-700 */
        color: #ffffff;
        font-weight: bold;
        padding: 12px 16px; /* Tailwind py-3 px-4 */
        border: none;
        border-radius: 8px; /* Tailwind rounded-lg = 0.5rem = 8px */
        font-size: 16px;
        cursor: pointer;
        transition: transform 0.3s ease, background-color 0.3s ease;
        display: inline-flex;
        align-items: center;
        justify-content: center;
      }
      .button-primary i {
        margin-right: 8px;
      }
      .button-primary:hover {
        background-color: #065f46; /* Tailwind bg-green-800 */
        transform: scale(1.05);
      }

      .button-secondary {
        background-color: transparent;
        color: #374151; /* Tailwind text-gray-700 */
        font-weight: 500; /* Tailwind font-medium */
        padding: 12px 16px; /* Tailwind py-3 px-4 */
        border: 1px solid #d1d5db; /* Tailwind border-gray-300 */
        border-radius: 8px; /* Tailwind rounded-lg */
        font-size: 16px;
        cursor: pointer;
        transition: background-color 0.3s ease;
      }
      .button-secondary:hover {
        background-color: #f3f4f6; /* Tailwind hover:bg-gray-100 */
      }

      /* ------------------------------------------------------------
       11. Ek Küçük Yardımcı Sınıflar
    ------------------------------------------------------------ */
      .text-center {
        text-align: center;
      }
      .mb-6 {
        margin-bottom: 24px; /* 1.5rem */
      }
    </style>
  </head>
  <body>
    <!-- Popup Overlay Başlangıcı -->
    <div id="popup" class="popup-overlay hidden">
      <!-- Arka Plan Karartması (Overlay) -->
      <!-- Zaten .popup-overlay içinde background-color tanımlı olduğu için ayrıca içerik gerekmez -->

      <!-- Popup İçeriği -->
      <div class="popup-content">
        <!-- Sol ve Sağ Zeytin Dalı İkonları -->
        <i class="olive-branch left fa-solid fa-leaf"></i>
        <i class="olive-branch right fa-solid fa-leaf"></i>

        <!-- İndirim Rozeti -->
        <div class="discount-badge">
          <img
            src="https://www.dogalyasam.net/image/cache/catalog/icons/new/yuzda10-200x200.png.webp"
            alt=""
          />
        </div>

        <!-- İçerik Alanı -->
        <div style="position: relative; z-index: 10; padding: 32px">
          <!-- Başlık ve Alt Metin -->
          <div class="popup-header">
            <h2 class="popup-title">Özel İndirim!</h2>
            <p class="popup-subtitle">
              Zeytinyağlarında sepette %10 indirim fırsatını kaçırmayın!
            </p>
          </div>

          <!-- Video Bölümü -->
          <div class="video-container">
            <iframe
              id="heroVideo"
              src="https://player.vimeo.com/video/1086716402?h=5691e6cdd5&amp;background=1&amp;autoplay=1&amp;loop=1&amp;byline=0&amp;title=0&amp;controls=0&amp;muted=1&amp;dnt=1&amp;quality=auto&amp;app_id=58479&amp;preload=auto"
              allow="autoplay; fullscreen; picture-in-picture; clipboard-write; encrypted-media"
              loading="eager"
              title="zeytinyagi-video"
              onload="
              setTimeout(() => {
                const poster = this.parentNode.querySelector('.video-poster');
                if (poster) {
                  poster.style.opacity = '0';
                }
              }, 4000);
              setTimeout(() => {
                const poster = this.parentNode.querySelector('.video-poster');
                if (poster) {
                  poster.style.display = 'none';
                }
              }, 4000);
            "
            ></iframe>
          </div>

          <!-- Bilgi Kutusu -->
          <div class="info-box">
            <div class="info-icon">
              <i class="fa-solid fa-exclamation-circle"></i>
            </div>
            <div class="info-text">
              Bu indirim ödeme sayfasında <b>Otomatik</b> uygulanacaktır. Hemen
              alışverişe başlayın!
            </div>
          </div>

          <!-- Butonlar -->
          <div class="button-container">
            <a
              href="https://www.dogalyasam.net/zeytinyagi/naturel-sizma-zeytinyagi/"
            >
              <button id="acceptBtn" class="button-primary">
                <i class="fa-solid fa-shopping-cart"></i> Hemen Alışveriş Yap
              </button>
            </a>
            <button id="closeBtn" class="button-secondary">Daha Sonra</button>
          </div>
        </div>
      </div>
    </div>
    <!-- Popup Overlay Bitişi -->

    <script>
      document.addEventListener("DOMContentLoaded", function () {
        const popup = document.getElementById("popup");
        const closeBtn = document.getElementById("closeBtn");
        const acceptBtn = document.getElementById("acceptBtn");

        // Eğer daha önce cookie set edilmişse popup'ı gizle
        function getCookie(name) {
          const cookieName = name + "=";
          const decodedCookie = decodeURIComponent(document.cookie);
          const cookieArray = decodedCookie.split(";");
          for (let i = 0; i < cookieArray.length; i++) {
            let cookie = cookieArray[i].trim();
            if (cookie.indexOf(cookieName) === 0) {
              return cookie.substring(cookieName.length, cookie.length);
            }
          }
          return "";
        }

        function setCookie(name, value, days) {
          const date = new Date();
          date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000);
          const expires = "expires=" + date.toUTCString();
          document.cookie = name + "=" + value + ";" + expires + ";path=/";
        }

        if (getCookie("oliveDiscountSeen") === "true") {
          // Cookie varsa popup görünmesin
          popup.classList.add("hidden");
        } else {
          // 2 saniye sonra popup'ı göster
          setTimeout(function () {
            popup.classList.remove("hidden");
          }, 2000);
        }

        // "Daha Sonra" butonuna tıklanınca popup'ı kapat ve cookie set et
        closeBtn.addEventListener("click", function () {
          popup.classList.add("hidden");
          setCookie("oliveDiscountSeen", "true", 1);
        });

        // "Hemen Alışveriş Yap" butonuna tıklanınca alert göster ve popup'ı kapat
        acceptBtn.addEventListener("click", function () {
          alert(
            "Sepetinize yönlendiriliyorsunuz! Kupon kodunuz otomatik uygulanacaktır."
          );
          popup.classList.add("hidden");
          // Eğer gerçek yönlendirme isteniyorsa aşağıdaki satırın yorumunu kaldırabilirsiniz:
          // window.location.href = '/products?category=olive-oils';
        });
      });
    </script>
  </body>
</html>
