<!DOCTYPE html>
<html lang="tr">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title><PERSON><PERSON><PERSON><PERSON>i - Ultra Modern Kategori</title>
    <style>
      body {
        margin: 0;
      }
      /* Reset and Base Styles */
      .copper-modern * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      .copper-modern {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
        line-height: 1.6;
        color: #1d1d1f;
        background: linear-gradient(135deg, #f5f5f7 0%, #e8e8ea 100%);
        min-height: 100vh;
        overflow-x: hidden;
      }

      /* Natural Copper Background */
      .aurora-bg {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: -1;
        background: linear-gradient(
          135deg,
          #8b4513 0%,
          #a0522d 25%,
          #cd853f 50%,
          #deb887 75%,
          #f4a460 100%
        );
        background-size: 300% 300%;
        animation: copperFlow 20s ease infinite;
      }

      @keyframes copperFlow {
        0% {
          background-position: 0% 50%;
        }
        50% {
          background-position: 100% 50%;
        }
        100% {
          background-position: 0% 50%;
        }
      }

      /* Glassmorphism Base */
      .glass {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      }

      /* Neumorphism Base */
      .neuro {
        background: linear-gradient(145deg, #f0f0f0, #cacaca);
        box-shadow: 20px 20px 60px #bebebe, -20px -20px 60px #ffffff;
        border-radius: 20px;
      }

      /* Container */
      .copper-container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 0 24px;
      }

      /* Hero Section - Ultra Modern */
      .copper-hero {
        position: relative;
        height: 100vh;
        min-height: 700px;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
        perspective: 1000px;
        background: url("https://www.dogalyasam.net/image/catalog/icons/new/Bakir/copper-photo-3.jpg")
          center/cover no-repeat;
      }

      .hero-content-wrapper {
        position: relative;
        z-index: 10;
        text-align: center;
        max-width: 900px;
        padding: 60px;
      }

      .hero-glass-card {
        background: rgba(139, 69, 19, 0.1);
        backdrop-filter: blur(25px);
        border: 1px solid rgba(205, 133, 63, 0.3);
        border-radius: 30px;
        padding: 80px 60px;
        box-shadow: 0 25px 50px rgba(139, 69, 19, 0.2);
        transform: translateZ(0);
        transition: all 0.6s cubic-bezier(0.23, 1, 0.32, 1);
      }

      .hero-glass-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 35px 70px rgba(139, 69, 19, 0.3);
        border-color: rgba(205, 133, 63, 0.5);
      }

      .copper-hero-title {
        font-size: 72px;
        font-weight: 800;
        margin-bottom: 24px;
        background: linear-gradient(
          135deg,
          #ffffff 0%,
          #f4a460 30%,
          #cd853f 70%,
          #8b4513 100%
        );
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        letter-spacing: -3px;
        line-height: 1.1;
        text-shadow: 0 2px 20px rgba(139, 69, 19, 0.3);
      }

      .copper-hero-subtitle {
        font-size: 28px;
        font-weight: 300;
        color: rgba(255, 255, 255, 0.95);
        letter-spacing: 0.5px;
        line-height: 1.4;
        text-shadow: 0 1px 10px rgba(0, 0, 0, 0.2);
      }

      /* Floating Elements */
      .floating-elements {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: 5;
      }

      .floating-orb {
        position: absolute;
        border-radius: 50%;
        background: linear-gradient(
          135deg,
          rgba(205, 133, 63, 0.2),
          rgba(139, 69, 19, 0.1)
        );
        backdrop-filter: blur(10px);
        border: 1px solid rgba(205, 133, 63, 0.3);
        animation: float 6s ease-in-out infinite;
        display: flex;
      }

      .orb-1 {
        width: 120px;
        height: 120px;
        top: 15%;
        left: 10%;
        animation-delay: 0s;
      }

      .orb-2 {
        width: 80px;
        height: 80px;
        top: 25%;
        right: 15%;
        animation-delay: 2s;
      }

      .orb-3 {
        width: 150px;
        height: 150px;
        bottom: 20%;
        left: 8%;
        animation-delay: 4s;
      }

      .orb-4 {
        width: 100px;
        height: 100px;
        bottom: 30%;
        right: 12%;
        animation-delay: 1s;
      }

      @keyframes float {
        0%,
        100% {
          transform: translateY(0px);
        }
        33% {
          transform: translateY(-20px);
        }
        66% {
          transform: translateY(10px);
        }
      }

      /* Bento Grid Categories Section */
      .copper-categories {
        padding: 60px 0;
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        position: relative;
      }

      .section-title-modern {
        font-size: 64px;
        font-weight: 900;
        text-align: center;
        margin-bottom: 80px;
        background: linear-gradient(
          135deg,
          #8b4513 0%,
          #a0522d 50%,
          #cd853f 100%
        );
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        letter-spacing: -2px;
        line-height: 1.1;
        position: relative;
      }

      .section-title-modern::after {
        content: "";
        position: absolute;
        bottom: -20px;
        left: 50%;
        transform: translateX(-50%);
        width: 120px;
        height: 4px;
        background: linear-gradient(90deg, #8b4513, #cd853f, #deb887);
        border-radius: 2px;
      }

      /* Bento Grid Layout */
      .bento-grid {
        display: grid;
        grid-template-columns: repeat(10, 1fr);
        grid-template-rows: repeat(8, 120px);
        gap: 24px;
        max-width: 1400px;
        margin: 0 auto;
      }

      /* Bento Grid Items */
      .bento-item {
        position: relative;
        border-radius: 24px;
        overflow: hidden;
        cursor: pointer;
        transition: all 0.6s cubic-bezier(0.23, 1, 0.32, 1);
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
      }

      .bento-item:hover {
        transform: translateY(-6px);
        box-shadow: 0 20px 40px rgba(139, 69, 19, 0.2);
      }

      /* Large Featured Item */
      .bento-large {
        grid-column: span 5;
        grid-row: span 4;
        background: linear-gradient(135deg, #8b4513 0%, #a0522d 100%);
      }

      /* Medium Items */
      .bento-medium {
        grid-column: span 5;
        grid-row: span 4;
      }

      .bento-medium-wide {
        grid-column: span 5;
        grid-row: span 4;
      }

      /* Small Items */
      .bento-small {
        grid-column: span 5;
        grid-row: span 4;
      }

      /* Bento Content */
      .bento-content {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        padding: 32px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        color: white;
        z-index: 2;
      }

      .bento-image {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-size: cover;
        background-position: center;
        z-index: 1;
        transition: transform 0.6s ease;
      }

      .bento-item:hover .bento-image {
        transform: scale(1.1);
      }

      .bento-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          135deg,
          rgba(0, 0, 0, 0.4) 0%,
          rgba(0, 0, 0, 0.1) 100%
        );
        z-index: 1;
      }

      .bento-title {
        font-size: 28px;
        font-weight: 700;
        margin-bottom: 12px;
        text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
      }

      .bento-large .bento-title {
        font-size: 42px;
        font-weight: 800;
      }

      .bento-subtitle {
        font-size: 16px;
        opacity: 0.9;
        margin-bottom: 20px;
        text-shadow: 0 1px 5px rgba(0, 0, 0, 0.3);
      }

      .bento-features {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        margin-bottom: 20px;
      }

      .bento-tag {
        background: rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(10px);
        padding: 6px 12px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
        border: 1px solid rgba(255, 255, 255, 0.1);
      }

      .bento-cta {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: white;
        padding: 12px 24px;
        border-radius: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-align: center;
        text-decoration: none;
        display: inline-block;
      }

      .bento-cta:hover {
        background: rgba(255, 255, 255, 0.25);
        transform: translateY(-2px);
      }

      /* Natural Hover Effects */
      .bento-item {
        transition: all 0.4s ease;
      }

      /* Modern Features Section */
      .features-section {
        padding: 60px 0;
        background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
        position: relative;
        overflow: hidden;
      }

      .features-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 40px;
        max-width: 1200px;
        margin: 0 auto;
      }

      .feature-card-modern {
        background: rgba(255, 255, 255, 0.05);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 24px;
        padding: 40px;
        transition: all 0.6s cubic-bezier(0.23, 1, 0.32, 1);
        position: relative;
        overflow: hidden;
        justify-content: center;
        align-items: center;
        text-align: center;
        display: flex;
        flex-direction: column;
      }

      .feature-card-modern::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          135deg,
          rgba(255, 255, 255, 0.1) 0%,
          transparent 50%
        );
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      .feature-card-modern:hover::before {
        opacity: 1;
      }

      .feature-card-modern:hover {
        transform: translateY(-8px);
        box-shadow: 0 25px 50px rgba(139, 69, 19, 0.2);
        border-color: rgba(205, 133, 63, 0.3);
      }

      .feature-icon {
        width: 150px;
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 24px;
        font-size: 32px;
        color: white;
      }
      .feature-icon img {
        width: 100%;
      }
      .feature-title {
        font-size: 24px;
        font-weight: 700;
        color: white;
        margin-bottom: 16px;
      }

      .feature-description {
        color: rgba(255, 255, 255, 0.8);
        line-height: 1.6;
        font-size: 16px;
      }

      /* Responsive Design */
      @media (max-width: 1200px) {
        .bento-grid {
          grid-template-columns: repeat(8, 1fr);
          grid-template-rows: repeat(10, 100px);
        }

        .bento-large {
          grid-column: span 5;
          grid-row: span 4;
        }

        .bento-medium {
          grid-column: span 4;
          grid-row: span 3;
        }

        .bento-medium-wide {
          grid-column: span 5;
          grid-row: span 4;
        }
      }

      @media (max-width: 768px) {
        .copper-hero {
          height: 80vh;
          min-height: 600px;
        }

        .copper-hero-title {
          font-size: 48px;
          letter-spacing: -2px;
        }

        .copper-hero-subtitle {
          font-size: 20px;
        }

        .hero-glass-card {
          padding: 60px 40px;
        }

        .section-title-modern {
          font-size: 42px;
        }

        .bento-grid {
          grid-template-columns: 1fr;
          grid-template-rows: auto;
          gap: 20px;
        }

        .bento-large,
        .bento-medium,
        .bento-medium-wide,
        .bento-small {
          grid-column: span 5;
          grid-row: span 4;
          min-height: 300px;
        }

        .bento-content {
          padding: 24px;
        }

        .bento-title {
          font-size: 24px;
        }

        .bento-large .bento-title {
          font-size: 32px;
        }

        .floating-orb {
          display: none;
        }

        .features-grid {
          grid-template-columns: 1fr;
          gap: 24px;
        }

        .feature-card-modern {
          padding: 32px;
        }
      }

      @media (max-width: 480px) {
        .copper-hero-title {
          font-size: 36px;
        }

        .copper-hero-subtitle {
          font-size: 18px;
        }

        .hero-glass-card {
          padding: 40px 24px;
        }

        .section-title-modern {
          font-size: 32px;
        }

        .bento-content {
          padding: 20px;
        }

        .bento-title {
          font-size: 20px;
        }

        .bento-large .bento-title {
          font-size: 28px;
        }
      }

      /* Scroll Animations */
      .scroll-reveal {
        opacity: 0;
        transform: translateY(50px);
        transition: all 0.8s cubic-bezier(0.23, 1, 0.32, 1);
      }

      .scroll-reveal.revealed {
        opacity: 1;
        transform: translateY(0);
      }

      /* Micro Interactions */
      .micro-bounce {
        animation: microBounce 2s ease-in-out infinite;
      }

      @keyframes microBounce {
        0%,
        100% {
          transform: translateY(0);
        }
        50% {
          transform: translateY(-5px);
        }
      }

      /* FAQ Section */
      .faq-section {
        padding: 60px 0;
        background: white;
      }

      .faq-container {
        max-width: 800px;
        margin: 0 auto;
      }

      .faq-item {
        border: 1px solid rgba(139, 69, 19, 0.1);
        border-radius: 16px;
        margin-bottom: 20px;
        overflow: hidden;
        transition: all 0.3s ease;
      }

      .faq-item:hover {
        box-shadow: 0 8px 25px rgba(139, 69, 19, 0.1);
      }

      .faq-question {
        padding: 24px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-weight: 600;
        font-size: 18px;
        color: #8b4513;
        transition: all 0.3s ease;
      }

      .faq-question:hover {
        background: linear-gradient(135deg, #deb887 0%, #cd853f 100%);
        color: white;
      }

      .faq-icon {
        font-size: 20px;
        transition: transform 0.3s ease;
      }

      .faq-item.active .faq-icon {
        transform: rotate(45deg);
      }

      .faq-answer {
        padding: 0 24px;
        max-height: 0;
        overflow: hidden;
        transition: all 0.4s ease;
        background: white;
      }

      .faq-item.active .faq-answer {
        padding: 24px;
        max-height: 200px;
      }

      .faq-answer p {
        color: #6c757d;
        line-height: 1.6;
      }

      /* Care Instructions Section */
      .care-section {
        padding: 60px 0;
        background: linear-gradient(135deg, #deb887 0%, #f4a460 100%);
      }

      .care-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 30px;
        margin-top: 60px;
      }

      .care-card {
        background: rgba(255, 255, 255, 0.9);
        border-radius: 20px;
        padding: 32px;
        text-align: center;
        transition: all 0.4s ease;
        border: 1px solid rgba(139, 69, 19, 0.1);
      }

      .care-card:hover {
        transform: translateY(-6px);
        box-shadow: 0 20px 40px rgba(139, 69, 19, 0.15);
        background: white;
      }

      .care-icon {
        width: 120px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 24px;
        font-size: 32px;
        color: white;
      }
      .care-icon img {
        width: 100%;
      }
      .care-title {
        font-size: 20px;
        font-weight: 600;
        color: #8b4513;
        margin-bottom: 16px;
      }

      .care-description {
        color: #6c757d;
        line-height: 1.6;
      }

      /* Product Filter Section */
      .filter-section {
        padding: 60px 0;
        background: white;
        border-bottom: 1px solid rgba(139, 69, 19, 0.1);
      }

      .filter-tabs {
        display: flex;
        justify-content: center;
        gap: 20px;
        flex-wrap: wrap;
      }

      .filter-tab {
        padding: 12px 24px;
        border: 2px solid rgba(139, 69, 19, 0.2);
        border-radius: 25px;
        background: transparent;
        color: #8b4513;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
      }

      .filter-tab:hover,
      .filter-tab.active {
        background: linear-gradient(135deg, #8b4513, #cd853f);
        color: white;
        border-color: transparent;
        transform: translateY(-2px);
      }
    </style>
  </head>
  <body>
    <div class="copper-modern">
      <!-- Aurora Background -->
      <div class="aurora-bg"></div>

      <!-- Hero Section -->
      <section class="copper-hero">
        <!-- Floating Elements -->
        <div class="floating-elements">
          <div class="floating-orb orb-1">
            <img
              src="https://www.dogalyasam.net/image/catalog/icons/new/Bakir/copp-7.png"
              alt=""
            />
          </div>
          <div class="floating-orb orb-2">
            <img
              src="https://www.dogalyasam.net/image/catalog/icons/new/Bakir/copp-icon-7.png"
              alt=""
            />
          </div>
          <div class="floating-orb orb-3">
            <img
              src="https://www.dogalyasam.net/image/catalog/icons/new/Bakir/copp-icon-8.png"
              alt=""
            />
          </div>
          <div class="floating-orb orb-4">
            <img
              src="https://www.dogalyasam.net/image/catalog/icons/new/Bakir/copp-icon-9.png"
              alt=""
            />
          </div>
        </div>

        <div class="hero-content-wrapper">
          <div class="hero-glass-card">
            <h1 class="copper-hero-title">Bakır Ürünleri</h1>
            <p class="copper-hero-subtitle">
              Yüzyıllardır Sağlıklı Yaşamın Vazgeçilmez Parçası
            </p>
          </div>
        </div>
      </section>

      <!-- Bento Grid Categories -->
      <section class="copper-categories">
        <div class="copper-container">
          <h2 class="section-title-modern scroll-reveal">
            Ürün Kategorilerimiz
          </h2>

          <div class="bento-grid scroll-reveal">
            <!-- Large Featured Item - Bakır Bardaklar -->
            <div class="bento-item bento-medium">
              <div
                class="bento-image"
                style="
                  background-image: url('https://www.dogalyasam.net/image/catalog/icons/new/Bakir/copper-photo-1.jpg');
                "
              ></div>
              <div class="bento-overlay"></div>
              <div class="bento-content">
                <div>
                  <h3 class="bento-title">Bakır Bardaklar</h3>
                  <p class="bento-subtitle">
                    Oligodinamik özelliği ile suyu doğal yolla sterilize eden,
                    zararlı bakterileri yok ederek sağlıklı su içmenizi sağlayan
                    el yapımı bakır bardaklar.
                  </p>
                  <div class="bento-features">
                    <span class="bento-tag">Antibakteriyel</span>
                    <span class="bento-tag">El İşçiliği</span>
                    <span class="bento-tag">500ml</span>
                  </div>
                </div>
                <a
                  href="https://www.dogalyasam.net/ev-yasam/bakir-mutfak-esyalari/bakir-bardak"
                  class="bento-cta"
                  >Keşfet →</a
                >
              </div>
            </div>

            <!-- Medium Item - Bakır Bileklikler -->
            <div
              class="bento-item bento-medium"
              style="
                background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
              "
            >
              <div
                class="bento-image"
                style="
                  background-image: url('https://www.dogalyasam.net/image/catalog/icons/new/Bakir/copper-KB-3.jpg');
                "
              ></div>
              <div class="bento-overlay"></div>
              <div class="bento-content">
                <div>
                  <h3 class="bento-title">Bakır Bileklikler</h3>
                  <p class="bento-subtitle">
                    Eklem sağlığına destek veren, romatizma şikayetlerini
                    hafifletmeye yardımcı olan %100 saf bakır bileklikler.
                  </p>
                  <div class="bento-features">
                    <span class="bento-tag">Eklem Desteği</span>
                    <span class="bento-tag">Hipoalerjenik</span>
                  </div>
                </div>
                <a
                  href="https://www.dogalyasam.net/ev-yasam/bakir-mutfak-esyalari/bakir-bileklik-dalga-model"
                  class="bento-cta"
                  >İncele →</a
                >
              </div>
            </div>

            <!-- Medium Wide Item - Bakır Çaydanlıklar -->
            <div
              class="bento-item bento-medium"
              style="
                background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
              "
            >
              <div
                class="bento-image"
                style="
                  background-image: url('https://www.dogalyasam.net/image/catalog/icons/new/Bakir/copper-photo-3.jpg');
                "
              ></div>
              <div class="bento-overlay"></div>
              <div class="bento-content">
                <div>
                  <h3 class="bento-title">Bakır Çaydanlıklar</h3>
                  <p class="bento-subtitle">
                    Geleneksel el dövmesi tekniği ile üretilen, üstün ısı
                    iletkenliği sayesinde mükemmel çay demleme deneyimi.
                  </p>
                  <div class="bento-features">
                    <span class="bento-tag">El Dövmesi</span>
                    <span class="bento-tag">2+1 Lt</span>
                  </div>
                </div>
                <a
                  href="https://www.dogalyasam.net/ev-yasam/bakir-mutfak-esyalari/bakir-caydanlik"
                  class="bento-cta"
                  >Görüntüle →</a
                >
              </div>
            </div>

            <!-- Small Item - Mutfak Eşyaları -->
            <div
              class="bento-item bento-medium"
              style="
                background: linear-gradient(135deg, #f4a460 0%, #cd853f 100%);
              "
            >
              <div
                class="bento-image"
                style="
                  background-image: url('https://www.dogalyasam.net/image/catalog/icons/new/Bakir/copper-photo-6.jpg');
                "
              ></div>
              <div class="bento-overlay"></div>
              <div class="bento-content">
                <div>
                  <h3 class="bento-title">Mutfak Eşyaları</h3>
                  <p class="bento-subtitle">
                    Sağlıklı pişirme için kaliteli bakır mutfak eşyaları.
                  </p>
                </div>
                <a
                  href="https://www.dogalyasam.net/ev-yasam/bakir-mutfak-esyalari/bakir-tava"
                  class="bento-cta"
                  >Gör →</a
                >
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Modern Features Section -->
      <section class="features-section">
        <div class="copper-container">
          <h2 class="section-title-modern scroll-reveal" style="color: white">
            Bakırın Sağlık Faydaları
          </h2>

          <div class="features-grid">
            <!-- Oligodinamik Özellik -->
            <div class="feature-card-modern scroll-reveal">
              <div class="feature-icon">
                <img
                  src="https://www.dogalyasam.net/image/catalog/icons/new/Bakir/copp-icon-6.png"
                  alt=""
                />
              </div>
              <h3 class="feature-title">Oligodinamik Özellik</h3>
              <p class="feature-description">
                Bakırın oligodinamik özelliği, suyun içinde bulunan E. coli ve
                S. aureus gibi zararlı bakterileri etkili biçimde yok eder ve
                böylece suyu doğal yolla sterilize eder. Bu sayede bakır kapta
                saklanan su, uzun süre taze kalır.
              </p>
            </div>

            <!-- Zihinsel Verimlilik -->
            <div class="feature-card-modern scroll-reveal">
              <div class="feature-icon">
                <img
                  src="https://www.dogalyasam.net/image/catalog/icons/new/Bakir/copp-icon-5.png"
                  alt=""
                />
              </div>
              <h3 class="feature-title">Zihinsel Verimlilik</h3>
              <p class="feature-description">
                Bakır bardakla içilen su, vücudunuzun ihtiyaç duyduğu iz
                elementleri sunar. Zihinsel verimlilik, bakırın sinir
                taşıyıcılarının miyelin kılıfını destekleyerek nöronlar
                arasındaki iletişimi hızlandırmasıyla artar.
              </p>
            </div>

            <!-- Eklem Sağlığı -->
            <div class="feature-card-modern scroll-reveal">
              <div class="feature-icon">
                <img
                  src="https://www.dogalyasam.net/image/catalog/icons/new/Bakir/copp-icon-4.png"
                  alt=""
                />
              </div>
              <h3 class="feature-title">Eklem Sağlığı</h3>
              <p class="feature-description">
                Eklem sağlığına da olumlu etkileri bulunan bakır, suyla
                etkileşime girdiğinde romatizma ve kireçlenme şikâyetlerini
                hafifletmeye yardımcı olabilir. Ayrıca sinir sisteminin
                dengelenmesine destek verir.
              </p>
            </div>

            <!-- Cilt Yenilenmesi -->
            <div class="feature-card-modern scroll-reveal">
              <div class="feature-icon">
                <img
                  src="https://www.dogalyasam.net/image/catalog/icons/new/Bakir/copp-icon-1.png"
                  alt=""
                />
              </div>
              <h3 class="feature-title">Cilt Yenilenmesi</h3>
              <p class="feature-description">
                Cilt yenilenmesi konusunda da bakır, suya geçen minerallerle
                cildin daha canlı ve pürüzsüz görünmesini destekler. Doğal
                güzellik için ideal mineral desteği sağlar.
              </p>
            </div>

            <!-- Kalite Özellikleri -->
            <div class="feature-card-modern scroll-reveal">
              <div class="feature-icon">
                <img
                  src="https://www.dogalyasam.net/image/catalog/icons/new/Bakir/copp-icon-2.png"
                  alt=""
                />
              </div>
              <h3 class="feature-title">Premium Kalite</h3>
              <p class="feature-description">
                Bakır mutfak eşyaları, mevcut en kaliteli malzemeler
                kullanılarak yapılır. Tutarlılık ve kaliteyi sağlamak için
                mutfak eşyalarımız el yapımıdır. Dayanıklı ve uzun ömürlüdürler.
              </p>
            </div>

            <!-- Günlük Kullanım -->
            <div class="feature-card-modern scroll-reveal">
              <div class="feature-icon">
                <img
                  src="https://www.dogalyasam.net/image/catalog/icons/new/Bakir/copp-icon-3.png"
                  alt=""
                />
              </div>
              <h3 class="feature-title">Günlük Sağlık</h3>
              <p class="feature-description">
                Her gün bir miktar bakır suyu tüketmek, hem minerallerinizi
                tamamlar hem de sağlıklı bir su arıtma yöntemi sunar. Sofranıza
                ve sağlığınıza doğal bir dokunuş katabilirsiniz.
              </p>
            </div>
          </div>
        </div>
      </section>

      <!-- Care Instructions Section -->
      <section class="care-section">
        <div class="copper-container">
          <h2 class="section-title-modern scroll-reveal">Bakım Talimatları</h2>

          <div class="care-grid">
            <div class="care-card scroll-reveal">
              <div class="care-icon">
                <img
                  src="https://www.dogalyasam.net/image/catalog/icons/new/Bakir/copp-10.png"
                  alt=""
                />
              </div>
              <h3 class="care-title">Günlük Temizlik</h3>
              <p class="care-description">
                Ilık su ve yumuşak sünger ile temizleyin. Sert kimyasallar
                kullanmayın. Kuruladıktan sonra kuru bez ile parlatın.
              </p>
            </div>

            <div class="care-card scroll-reveal">
              <div class="care-icon">
                <img
                  src="https://www.dogalyasam.net/image/catalog/icons/new/Bakir/copp-18.png"
                  alt=""
                />
              </div>
              <h3 class="care-title">Su Teması</h3>
              <p class="care-description">
                Bakır kapta suyu 6-8 saat bekletin. Daha uzun süre bekletmeyin.
                Her kullanımdan önce iyice çalkalayın.
              </p>
            </div>

            <div class="care-card scroll-reveal">
              <div class="care-icon">
                <img
                  src="https://www.dogalyasam.net/image/catalog/icons/new/Bakir/copp-8.png"
                  alt=""
                />
              </div>
              <h3 class="care-title">Doğal Parlatma</h3>
              <p class="care-description">
                Limon ve tuz karışımı ile parlatabilirsiniz. Sirke ile de doğal
                temizlik yapabilirsiniz. Sonrasında bol su ile durulayın.
              </p>
            </div>

            <div class="care-card scroll-reveal">
              <div class="care-icon">
                <img
                  src="https://www.dogalyasam.net/image/catalog/icons/new/Bakir/copp-12.png"
                  alt=""
                />
              </div>
              <h3 class="care-title">Saklama</h3>
              <p class="care-description">
                Kuru ve havadar yerde saklayın. Nemli ortamlardan uzak tutun.
                Diğer metal eşyalarla temas ettirmeyin.
              </p>
            </div>
          </div>
        </div>
      </section>

      <!-- FAQ Section -->
      <section class="faq-section">
        <div class="copper-container">
          <h2 class="section-title-modern scroll-reveal">
            Sık Sorulan Sorular
          </h2>

          <div class="faq-container">
            <div class="faq-item scroll-reveal">
              <div class="faq-question">
                <span>Bakır ürünler güvenli mi?</span>
                <span class="faq-icon">+</span>
              </div>
              <div class="faq-answer">
                <p>
                  Evet, %100 saf bakır ürünlerimiz tamamen güvenlidir. Bakır
                  doğal bir antibakteriyel mineral olup, yüzyıllardır güvenle
                  kullanılmaktadır.
                </p>
              </div>
            </div>

            <div class="faq-item scroll-reveal">
              <div class="faq-question">
                <span>Bakır su ne kadar süre bekletilmeli?</span>
                <span class="faq-icon">+</span>
              </div>
              <div class="faq-answer">
                <p>
                  Bakır kapta suyu 6-8 saat bekletmeniz yeterlidir. Bu süre
                  bakırın suya geçmesi için optimal süredir.
                </p>
              </div>
            </div>

            <div class="faq-item scroll-reveal">
              <div class="faq-question">
                <span>Bakır bileklik nasıl kullanılır?</span>
                <span class="faq-icon">+</span>
              </div>
              <div class="faq-answer">
                <p>
                  Bakır bilekliği temiz cilde takın. Günde 8-12 saat kullanım
                  önerilir. Cilt hassasiyeti varsa kullanım süresini azaltın.
                </p>
              </div>
            </div>

            <div class="faq-item scroll-reveal">
              <div class="faq-question">
                <span>Kargo süresi ne kadar?</span>
                <span class="faq-icon">+</span>
              </div>
              <div class="faq-answer">
                <p>
                  Siparişleriniz 1-3 iş günü içinde kargoya verilir. Türkiye
                  geneline 2-5 iş günü içinde teslim edilir.
                </p>
              </div>
            </div>

            <div class="faq-item scroll-reveal">
              <div class="faq-question">
                <span>İade politikanız nedir?</span>
                <span class="faq-icon">+</span>
              </div>
              <div class="faq-answer">
                <p>
                  14 gün içinde koşulsuz iade hakkınız bulunmaktadır. Ürün
                  orijinal ambalajında ve kullanılmamış olmalıdır.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>

    <script>
      // Modern Intersection Observer for scroll animations
      const observerOptions = {
        threshold: 0.1,
        rootMargin: "0px 0px -100px 0px",
      };

      const scrollObserver = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("revealed");
          }
        });
      }, observerOptions);

      // Parallax effect for floating orbs
      function updateParallax() {
        const scrolled = window.pageYOffset;
        const parallaxElements = document.querySelectorAll(".floating-orb");

        parallaxElements.forEach((element, index) => {
          const speed = 0.5 + index * 0.1;
          const yPos = -(scrolled * speed);
          element.style.transform = `translateY(${yPos}px) rotate(${
            scrolled * 0.1
          }deg)`;
        });
      }

      // Natural hover effect for bento items
      function addNaturalHover() {
        document.querySelectorAll(".bento-item").forEach((item) => {
          item.addEventListener("mouseenter", () => {
            item.style.transform = "translateY(-6px)";
            item.style.boxShadow = "0 20px 40px rgba(139, 69, 19, 0.2)";
          });

          item.addEventListener("mouseleave", () => {
            item.style.transform = "translateY(0)";
            item.style.boxShadow = "0 8px 32px rgba(0, 0, 0, 0.1)";
          });
        });
      }

      // Magnetic effect for CTA buttons
      function addMagneticEffect() {
        document.querySelectorAll(".bento-cta").forEach((button) => {
          button.addEventListener("mousemove", (e) => {
            const rect = button.getBoundingClientRect();
            const x = e.clientX - rect.left - rect.width / 2;
            const y = e.clientY - rect.top - rect.height / 2;

            button.style.transform = `translate(${x * 0.1}px, ${y * 0.1}px)`;
          });

          button.addEventListener("mouseleave", () => {
            button.style.transform = "translate(0, 0)";
          });
        });
      }

      // Smooth reveal animation for features
      function animateFeatures() {
        const features = document.querySelectorAll(".feature-card-modern");
        features.forEach((feature, index) => {
          setTimeout(() => {
            feature.style.opacity = "1";
            feature.style.transform = "translateY(0) rotateX(0)";
          }, index * 200);
        });
      }

      // FAQ Functionality
      function initializeFAQ() {
        document.querySelectorAll(".faq-question").forEach((question) => {
          question.addEventListener("click", () => {
            const faqItem = question.parentElement;
            const isActive = faqItem.classList.contains("active");

            // Close all other FAQ items
            document.querySelectorAll(".faq-item").forEach((item) => {
              item.classList.remove("active");
            });

            // Toggle current item
            if (!isActive) {
              faqItem.classList.add("active");
            }
          });
        });
      }

      // Initialize everything
      document.addEventListener("DOMContentLoaded", () => {
        // Observe scroll reveal elements
        document.querySelectorAll(".scroll-reveal").forEach((el) => {
          scrollObserver.observe(el);
        });

        // Add smooth scrolling
        document.documentElement.style.scrollBehavior = "smooth";

        // Initialize effects
        addNaturalHover();
        addMagneticEffect();
        initializeFAQ();

        // Parallax on scroll
        window.addEventListener("scroll", updateParallax);

        // Animate features when they come into view
        const featuresSection = document.querySelector(".features-section");
        const featuresObserver = new IntersectionObserver((entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              animateFeatures();
              featuresObserver.unobserve(entry.target);
            }
          });
        });

        if (featuresSection) {
          featuresObserver.observe(featuresSection);
        }

        // Add click ripple effect to all buttons
        document
          .querySelectorAll(".bento-cta, .hero-glass-card")
          .forEach((element) => {
            element.addEventListener("click", function (e) {
              const ripple = document.createElement("span");
              const rect = this.getBoundingClientRect();
              const size = Math.max(rect.width, rect.height);
              const x = e.clientX - rect.left - size / 2;
              const y = e.clientY - rect.top - size / 2;

              ripple.style.cssText = `
              position: absolute;
              width: ${size}px;
              height: ${size}px;
              left: ${x}px;
              top: ${y}px;
              background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%);
              border-radius: 50%;
              transform: scale(0);
              animation: rippleEffect 0.6s ease-out;
              pointer-events: none;
            `;

              this.style.position = "relative";
              this.style.overflow = "hidden";
              this.appendChild(ripple);

              setTimeout(() => ripple.remove(), 600);
            });
          });

        // Performance optimization: throttle scroll events
        let ticking = false;
        function requestTick() {
          if (!ticking) {
            requestAnimationFrame(updateParallax);
            ticking = true;
          }
        }

        window.addEventListener("scroll", requestTick);
      });

      // Add ripple animation CSS
      const style = document.createElement("style");
      style.textContent = `
        @keyframes rippleEffect {
          to {
            transform: scale(2);
            opacity: 0;
          }
        }


      `;
      document.head.appendChild(style);
    </script>
  </body>
</html>
