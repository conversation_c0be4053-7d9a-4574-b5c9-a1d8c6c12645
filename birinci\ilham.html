<!DOCTYPE html>
<html lang="tr">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title><PERSON>iri<PERSON><PERSON>ı - Uygun Fiyatlı Yemeklik Zeytinyağı</title>
    <meta
      name="description"
      content="Birinci zeytinyağı - Günlük yemekleriniz için uygun fiyatlı, kaliteli zeytinyağı. Sofralarınıza doğal lezzet katın."
    />
    <script src="https://cdn.tailwindcss.com"></script>
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <style>
      @keyframes fadeInUp {
        from {
          opacity: 0;
          transform: translateY(30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      @keyframes scaleIn {
        from {
          opacity: 0;
          transform: scale(0.9);
        }
        to {
          opacity: 1;
          transform: scale(1);
        }
      }

      .fade-in-up {
        animation: fadeInUp 0.8s ease-out forwards;
      }

      .scale-in {
        animation: scaleIn 0.6s ease-out forwards;
      }

      .delay-1 {
        animation-delay: 0.2s;
      }
      .delay-2 {
        animation-delay: 0.4s;
      }
      .delay-3 {
        animation-delay: 0.6s;
      }

      .olive-gradient {
        background: linear-gradient(
          135deg,
          #8b9a5b 0%,
          #a4b494 50%,
          #c8d5b9 100%
        );
      }

      .cream-gradient {
        background: linear-gradient(
          135deg,
          #f5f5dc 0%,
          #fff8dc 50%,
          #fffacd 100%
        );
      }

      .hover-lift {
        transition: all 0.3s ease;
      }

      .hover-lift:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
      }

      .price-highlight {
        background: linear-gradient(45deg, #8b9a5b, #a4b494);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      /* Custom px-based font sizes and spacing */
      .text-xs {
        font-size: 12px !important;
      }
      .text-sm {
        font-size: 14px !important;
      }
      .text-base {
        font-size: 16px !important;
      }
      .text-lg {
        font-size: 18px !important;
      }
      .text-xl {
        font-size: 20px !important;
      }
      .text-2xl {
        font-size: 24px !important;
      }
      .text-3xl {
        font-size: 30px !important;
      }
      .text-4xl {
        font-size: 36px !important;
      }
      .text-5xl {
        font-size: 48px !important;
      }
      .text-6xl {
        font-size: 60px !important;
      }

      /* Padding */
      .p-1 {
        padding: 4px !important;
      }
      .p-2 {
        padding: 8px !important;
      }
      .p-3 {
        padding: 12px !important;
      }
      .p-4 {
        padding: 16px !important;
      }
      .p-6 {
        padding: 24px !important;
      }
      .p-8 {
        padding: 32px !important;
      }
      .px-3 {
        padding-left: 12px !important;
        padding-right: 12px !important;
      }
      .px-4 {
        padding-left: 16px !important;
        padding-right: 16px !important;
      }
      .px-6 {
        padding-left: 24px !important;
        padding-right: 24px !important;
      }
      .px-8 {
        padding-left: 32px !important;
        padding-right: 32px !important;
      }
      .py-3 {
        padding-top: 12px !important;
        padding-bottom: 12px !important;
      }
      .py-4 {
        padding-top: 16px !important;
        padding-bottom: 16px !important;
      }
      .py-16 {
        padding-top: 54px !important;
        padding-bottom: 54px !important;
      }
      .py-20 {
        padding-top: 50px !important;
        padding-bottom: 50px !important;
      }
      .pt-0 {
        padding-top: 0px !important;
      }

      /* Margins */
      .m-2 {
        margin: 8px !important;
      }
      .m-3 {
        margin: 12px !important;
      }
      .m-4 {
        margin: 16px !important;
      }
      .mb-2 {
        margin-bottom: 8px !important;
      }
      .mb-3 {
        margin-bottom: 12px !important;
      }
      .mb-4 {
        margin-bottom: 16px !important;
      }
      .mb-6 {
        margin-bottom: 24px !important;
      }
      .mb-8 {
        margin-bottom: 32px !important;
      }
      .mb-12 {
        margin-bottom: 48px !important;
      }
      .mb-16 {
        margin-bottom: 64px !important;
      }
      .mr-2 {
        margin-right: 8px !important;
      }
      .mr-3 {
        margin-right: 12px !important;
      }
      .mr-4 {
        margin-right: 16px !important;
      }

      /* Gaps */
      .gap-4 {
        gap: 16px !important;
      }
      .gap-6 {
        gap: 24px !important;
      }
      .gap-8 {
        gap: 32px !important;
      }
      .gap-12 {
        gap: 48px !important;
      }

      /* Space between */
      .space-y-4 > * + * {
        margin-top: 16px !important;
      }
      .space-y-16 > * + * {
        margin-top: 64px !important;
      }
    </style>
  </head>
  <body class="cream-gradient min-h-screen">
    <!-- Hero Section -->
    <section class="py-16 px-4 min-h-screen flex items-center">
      <div class="container mx-auto max-w-6xl">
        <div class="text-center mb-12 fade-in-up">
          <h1 class="text-4xl md:text-6xl font-bold mb-4">
            <span class="text-gray-800">Birinci</span>
            <span class="price-highlight">Zeytinyağı</span>
          </h1>
          <p class="text-xl md:text-2xl text-gray-600 max-w-2xl mx-auto">
            Günlük yemekleriniz için uygun fiyatlı, kaliteli zeytinyağı
          </p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <!-- Product Image -->
          <div class="fade-in-up delay-1">
            <div class="relative">
              <div
                class="absolute inset-0 olive-gradient rounded-3xl transform rotate-3 opacity-20"
              ></div>
              <img
                src="https://images.unsplash.com/photo-1474979266404-7eaacbcd87c5?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                alt="Birinci Zeytinyağı"
                class="relative rounded-3xl shadow-2xl w-full max-w-lg mx-auto hover-lift"
              />
            </div>
          </div>

          <!-- Product Info -->
          <div class="fade-in-up delay-2">
            <div
              class="bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-xl hover-lift"
            >
              <div class="flex items-center mb-6">
                <div class="olive-gradient p-4 rounded-2xl mr-4">
                  <i class="fas fa-leaf text-white text-2xl"></i>
                </div>
                <div>
                  <h2 class="text-2xl font-bold text-gray-800">
                    Yemeklik Kalite
                  </h2>
                  <p class="text-gray-600">Günlük kullanım için ideal</p>
                </div>
              </div>

              <div class="space-y-4 mb-8">
                <div class="flex items-center">
                  <i class="fas fa-check-circle text-green-600 mr-3"></i>
                  <span class="text-gray-700">Doğal ve katkısız</span>
                </div>
                <div class="flex items-center">
                  <i class="fas fa-check-circle text-green-600 mr-3"></i>
                  <span class="text-gray-700">Uygun fiyatlı</span>
                </div>
                <div class="flex items-center">
                  <i class="fas fa-check-circle text-green-600 mr-3"></i>
                  <span class="text-gray-700"
                    >Tüm yemeklerde kullanılabilir</span
                  >
                </div>
              </div>

              <button
                class="w-full olive-gradient text-white font-bold py-4 px-6 rounded-2xl hover:shadow-lg transition-all duration-300 transform hover:scale-105"
              >
                <i class="fas fa-shopping-cart mr-2"></i>
                Hemen Sipariş Ver
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="py-20 px-4 bg-white">
      <div class="container mx-auto max-w-5xl">
        <div class="text-center mb-16 fade-in-up">
          <h2 class="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
            Neden <span class="price-highlight">Birinci Zeytinyağı?</span>
          </h2>
          <div class="w-24 h-1 olive-gradient mx-auto rounded-full"></div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
          <!-- Feature 1 -->
          <div class="text-center fade-in-up delay-1">
            <div
              class="olive-gradient w-20 h-20 rounded-2xl flex items-center justify-center mx-auto mb-6 transform rotate-12"
            >
              <i
                class="fas fa-seedling text-white text-2xl transform -rotate-12"
              ></i>
            </div>
            <h3 class="text-xl font-bold text-gray-800 mb-3">Doğal Üretim</h3>
            <p class="text-gray-600">
              Geleneksel yöntemlerle üretilmiş, katkısız zeytinyağı
            </p>
          </div>

          <!-- Feature 2 -->
          <div class="text-center fade-in-up delay-2">
            <div
              class="olive-gradient w-20 h-20 rounded-2xl flex items-center justify-center mx-auto mb-6 transform -rotate-12"
            >
              <i
                class="fas fa-utensils text-white text-2xl transform rotate-12"
              ></i>
            </div>
            <h3 class="text-xl font-bold text-gray-800 mb-3">
              Yemeklik Kalite
            </h3>
            <p class="text-gray-600">
              Günlük yemeklerinizde rahatlıkla kullanabilirsiniz
            </p>
          </div>

          <!-- Feature 3 -->
          <div class="text-center fade-in-up delay-3">
            <div
              class="olive-gradient w-20 h-20 rounded-2xl flex items-center justify-center mx-auto mb-6 transform rotate-12"
            >
              <i
                class="fas fa-wallet text-white text-2xl transform -rotate-12"
              ></i>
            </div>
            <h3 class="text-xl font-bold text-gray-800 mb-3">Uygun Fiyat</h3>
            <p class="text-gray-600">
              Kaliteli zeytinyağını herkesin bütçesine uygun fiyatta
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- Usage Section -->
    <section class="py-20 px-4 cream-gradient">
      <div class="container mx-auto max-w-6xl">
        <div class="text-center mb-16 fade-in-up">
          <h2 class="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
            Kullanım <span class="price-highlight">Alanları</span>
          </h2>
          <p class="text-xl text-gray-600">Günlük hayatınızda her yerde</p>
        </div>

        <div class="space-y-16">
          <!-- Usage 1 - Salatalarda (Fotoğraf Sol, Yazı Sağ) -->
          <div
            class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center fade-in-up delay-1"
          >
            <div class="order-1">
              <img
                src="https://images.unsplash.com/photo-1512621776951-a57141f2eefd?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                alt="Salatalarda zeytinyağı kullanımı"
                class="rounded-3xl shadow-2xl w-full hover-lift"
              />
            </div>
            <div class="order-2 lg:pl-8">
              <div
                class="bg-white/80 backdrop-blur-sm rounded-3xl p-8 hover-lift"
              >
                <h3 class="text-2xl md:text-3xl font-bold text-gray-800 mb-4">
                  Salatalarda
                </h3>
                <p class="text-lg text-gray-600 mb-6">
                  Çiğ olarak salatalarınıza ekleyerek doğal lezzeti yakalayın.
                  Taze sebzelerinizin tadını çıkarmanın en doğal yolu.
                </p>
                <div class="flex items-center text-green-600">
                  <i class="fas fa-check-circle mr-2"></i>
                  <span class="text-gray-700">Vitamin kaybı olmaz</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Usage 2 - Pişirmede (Yazı Sol, Fotoğraf Sağ) -->
          <div
            class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center fade-in-up delay-2"
          >
            <div class="order-2 lg:order-1 lg:pr-8">
              <div
                class="bg-white/80 backdrop-blur-sm rounded-3xl p-8 hover-lift"
              >
                <h3 class="text-2xl md:text-3xl font-bold text-gray-800 mb-4">
                  Pişirmede
                </h3>
                <p class="text-lg text-gray-600 mb-6">
                  Yemeklerinizde ve kızartmalarınızda güvenle kullanabilirsiniz.
                  Yüksek ısıya dayanıklı yapısı ile lezzet kaybı yaşamaz.
                </p>
                <div class="flex items-center text-green-600">
                  <i class="fas fa-check-circle mr-2"></i>
                  <span class="text-gray-700">Yüksek ısıya dayanıklı</span>
                </div>
              </div>
            </div>
            <div class="order-1 lg:order-2">
              <img
                src="https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                alt="Pişirmede zeytinyağı kullanımı"
                class="rounded-3xl shadow-2xl w-full hover-lift"
              />
            </div>
          </div>

          <!-- Usage 3 - Kahvaltıda (Fotoğraf Sol, Yazı Sağ) -->
          <div
            class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center fade-in-up delay-3"
          >
            <div class="order-1">
              <img
                src="https://images.unsplash.com/photo-1509440159596-0249088772ff?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                alt="Kahvaltıda zeytinyağı kullanımı"
                class="rounded-3xl shadow-2xl w-full hover-lift"
              />
            </div>
            <div class="order-2 lg:pl-8">
              <div
                class="bg-white/80 backdrop-blur-sm rounded-3xl p-8 hover-lift"
              >
                <h3 class="text-2xl md:text-3xl font-bold text-gray-800 mb-4">
                  Kahvaltıda
                </h3>
                <p class="text-lg text-gray-600 mb-6">
                  Ekmeğinize sürerek kahvaltılarınıza lezzet katın. Güne
                  sağlıklı başlamanın en doğal yolu.
                </p>
                <div class="flex items-center text-green-600">
                  <i class="fas fa-check-circle mr-2"></i>
                  <span class="text-gray-700">Doğal enerji kaynağı</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Garden Location Section -->
    <section class="py-20 px-4 bg-white">
      <div class="container mx-auto max-w-6xl">
        <div class="text-center mb-16 fade-in-up">
          <h2 class="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
            Bahçemizin <span class="price-highlight">Konumu</span>
          </h2>
          <div class="w-24 h-1 olive-gradient mx-auto rounded-full"></div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <!-- Map -->
          <div class="fade-in-up delay-1">
            <div
              class="bg-white rounded-3xl shadow-2xl overflow-hidden hover-lift"
            >
              <iframe
                src="https://www.google.com/maps/embed?pb=!1m10!1m8!1m3!1d203923.19801876709!2d36.18020420449887!3d37.002639536855916!3m2!1i1024!2i768!4f13.1!5e1!3m2!1str!2str!4v1747155157388!5m2!1str!2str"
                width="100%"
                height="400"
                style="border: 0"
                allowfullscreen=""
                loading="lazy"
                referrerpolicy="no-referrer-when-downgrade"
                class="w-full h-96"
              ></iframe>
            </div>
          </div>

          <!-- Location Info -->
          <div class="fade-in-up delay-2">
            <div
              class="bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-xl hover-lift"
            >
              <div class="flex items-center mb-6">
                <div class="olive-gradient p-4 rounded-2xl mr-4">
                  <i class="fas fa-map-marker-alt text-white text-2xl"></i>
                </div>
                <div>
                  <h3 class="text-2xl font-bold text-gray-800">
                    Osmaniye - Merkez
                  </h3>
                  <p class="text-gray-600">Amanosların Akyar Dağı Etekleri</p>
                </div>
              </div>

              <div class="space-y-4 mb-6">
                <div class="flex items-center">
                  <i class="fas fa-mountain text-green-600 mr-3"></i>
                  <span class="text-gray-700">300 metre rakım</span>
                </div>
                <div class="flex items-center">
                  <i class="fas fa-sun text-green-600 mr-3"></i>
                  <span class="text-gray-700">Yıl boyunca güneş ışığı</span>
                </div>
                <div class="flex items-center">
                  <i class="fas fa-seedling text-green-600 mr-3"></i>
                  <span class="text-gray-700">Organik yapılı topraklar</span>
                </div>
              </div>

              <p class="text-gray-600 leading-relaxed">
                Doğu Akdeniz'in bereketli toprakları üzerinde, zeytin
                yetiştiriciliği için Türkiye'nin en verimli havzalarından
                birinde yer alan bahçemiz.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- FAQ Section -->
    <section class="py-20 px-4 cream-gradient">
      <div class="container mx-auto max-w-4xl">
        <div class="text-center mb-16 fade-in-up">
          <h2 class="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
            Sıkça Sorulan <span class="price-highlight">Sorular</span>
          </h2>
          <p class="text-xl text-gray-600">Merak ettikleriniz</p>
        </div>

        <div class="space-y-4 fade-in-up delay-1">
          <!-- FAQ Item 1 -->
          <div
            class="bg-white/80 backdrop-blur-sm rounded-2xl overflow-hidden shadow-lg hover-lift"
          >
            <button
              class="faq-question w-full text-left p-6 flex justify-between items-center transition-all duration-300"
            >
              <span class="font-bold text-lg text-gray-800"
                >Zeytinyağını nasıl saklamalıyım?</span
              >
              <i
                class="fas fa-chevron-down text-green-600 transition-transform duration-300"
              ></i>
            </button>
            <div class="faq-answer hidden p-6 pt-0">
              <p class="text-gray-600">
                Zeytinyağınızı serin ve karanlık bir yerde, ışık geçirmeyen bir
                kapta saklamanızı öneririz. Oda sıcaklığında muhafaza
                edilebilir.
              </p>
            </div>
          </div>

          <!-- FAQ Item 2 -->
          <div
            class="bg-white/80 backdrop-blur-sm rounded-2xl overflow-hidden shadow-lg hover-lift"
          >
            <button
              class="faq-question w-full text-left p-6 flex justify-between items-center transition-all duration-300"
            >
              <span class="font-bold text-lg text-gray-800"
                >Raf ömrü ne kadar?</span
              >
              <i
                class="fas fa-chevron-down text-green-600 transition-transform duration-300"
              ></i>
            </button>
            <div class="faq-answer hidden p-6 pt-0">
              <p class="text-gray-600">
                Zeytinyağımızın raf ömrü 2 yıldır. Ancak açıldıktan sonra 6 ay
                içinde tüketmenizi öneririz.
              </p>
            </div>
          </div>

          <!-- FAQ Item 3 -->
          <div
            class="bg-white/80 backdrop-blur-sm rounded-2xl overflow-hidden shadow-lg hover-lift"
          >
            <button
              class="faq-question w-full text-left p-6 flex justify-between items-center transition-all duration-300"
            >
              <span class="font-bold text-lg text-gray-800"
                >Nasıl tüketmeliyim?</span
              >
              <i
                class="fas fa-chevron-down text-green-600 transition-transform duration-300"
              ></i>
            </button>
            <div class="faq-answer hidden p-6 pt-0">
              <p class="text-gray-600">
                Kaliteli zeytinyağımızı çiğ olarak salatalarda, ekmek üzerinde
                veya yemeklere son dokunuş olarak kullanmanızı öneririz.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Other Products Section -->
    <section class="py-20 px-4 bg-white">
      <div class="container mx-auto max-w-6xl">
        <div class="text-center mb-16 fade-in-up">
          <h2 class="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
            Diğer <span class="price-highlight">Zeytinyağlarımız</span>
          </h2>
          <p class="text-xl text-gray-600">Kalite seçeneklerimizi keşfedin</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
          <!-- Product 1 -->
          <div
            class="bg-white rounded-3xl shadow-xl overflow-hidden hover-lift fade-in-up delay-1 group"
          >
            <div class="relative overflow-hidden">
              <img
                src="https://www.dogalyasam.net/image/catalog/icons/FOTO-KB/OlivePhoto00000022-KB.jpg"
                alt="Erken Hasat Zeytinyağı"
                class="w-full h-64 object-cover group-hover:scale-110 transition-transform duration-500"
              />
              <div
                class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"
              ></div>
              <div class="absolute bottom-4 left-4 text-white">
                <span
                  class="bg-green-600 px-3 py-1 rounded-full text-sm font-bold"
                  >Premium</span
                >
              </div>
            </div>
            <div class="p-6">
              <h3 class="text-xl font-bold text-gray-800 mb-3">
                Erken Hasat Zeytinyağı
              </h3>
              <p class="text-gray-600 mb-4">
                Zeytinlerin tam olgunlaşmadan toplanmasıyla elde edilen, daha
                yoğun aromaya sahip zeytinyağı.
              </p>
              <a
                href="https://www.dogalyasam.net/zeytinyagi/naturel-sizma-zeytinyagi/"
              >
                <button
                  class="w-full olive-gradient text-white font-bold py-3 px-4 rounded-xl hover:shadow-lg transition-all duration-300"
                >
                  <i class="fas fa-eye mr-2"></i>
                  İncele
                </button>
              </a>
            </div>
          </div>

          <!-- Product 2 -->
          <div
            class="bg-white rounded-3xl shadow-xl overflow-hidden hover-lift fade-in-up delay-2 group"
          >
            <div class="relative overflow-hidden">
              <img
                src="https://www.dogalyasam.net/image/catalog/icons/FOTO-KB/OlivePhoto00000021-KB.jpg"
                alt="Soğuk Sıkım Zeytinyağı"
                class="w-full h-64 object-cover group-hover:scale-110 transition-transform duration-500"
              />
              <div
                class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"
              ></div>
              <div class="absolute bottom-4 left-4 text-white">
                <span
                  class="bg-yellow-600 px-3 py-1 rounded-full text-sm font-bold"
                  >Özel</span
                >
              </div>
            </div>
            <div class="p-6">
              <h3 class="text-xl font-bold text-gray-800 mb-3">
                Soğuk Sıkım Zeytinyağı
              </h3>
              <p class="text-gray-600 mb-4">
                En yüksek kalitede zeytinlerden soğuk sıkım yöntemiyle üretilen
                premium zeytinyağı.
              </p>
              <a
                href="https://www.dogalyasam.net/zeytinyagi/soguk-sikim-zeytinyagi/"
              >
                <button
                  class="w-full olive-gradient text-white font-bold py-3 px-4 rounded-xl hover:shadow-lg transition-all duration-300"
                >
                  <i class="fas fa-eye mr-2"></i>
                  İncele
                </button>
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>

    <script>
      // Smooth scroll animations
      const observerOptions = {
        threshold: 0.1,
        rootMargin: "0px 0px -50px 0px",
      };

      const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.style.opacity = "1";
            entry.target.style.transform = "translateY(0)";
          }
        });
      }, observerOptions);

      // Observe all animated elements
      document.querySelectorAll(".fade-in-up, .scale-in").forEach((el) => {
        el.style.opacity = "0";
        el.style.transform = "translateY(30px)";
        observer.observe(el);
      });

      // FAQ functionality
      document.querySelectorAll(".faq-question").forEach((button) => {
        button.addEventListener("click", () => {
          const faqItem = button.parentElement;
          const answer = faqItem.querySelector(".faq-answer");
          const icon = button.querySelector("i");

          // Toggle answer visibility
          answer.classList.toggle("hidden");

          // Rotate icon
          icon.classList.toggle("rotate-180");

          // Close other open FAQs
          document.querySelectorAll(".faq-question").forEach((otherButton) => {
            if (otherButton !== button) {
              const otherFaqItem = otherButton.parentElement;
              const otherAnswer = otherFaqItem.querySelector(".faq-answer");
              const otherIcon = otherButton.querySelector("i");

              otherAnswer.classList.add("hidden");
              otherIcon.classList.remove("rotate-180");
            }
          });
        });
      });
    </script>
  </body>
</html>
