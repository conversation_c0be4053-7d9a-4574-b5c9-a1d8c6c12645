<!DOCTYPE html>
<html lang="tr">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Erken <PERSON>at Naturel Sızma Zeytinyağı - Doğal <PERSON></title>
    <meta
      name="description"
      content="Amanos <PERSON>'nın eteklerinden gelen %100 doğal erken hasat naturel sızma zeytinyağı. Yüksek antioksidan, keskin meyvemsi aroma ve saf tazelik."
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.css"
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Cormorant+Garamond:wght@400;500;600;700&family=Montserrat:wght@300;400;500;600;700&family=Italiana&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <style>
      :root {
        /* Ana Renkler */
        --olive-green: #3a5311; /* Daha koyu ve zengin zeytin yeşili */
        --light-olive: #5c7a1f; /* Daha canlı açık yeşil */
        --dark-olive: #2c3f0e; /* Çok koyu yeşil (vurgular için) */

        /* Nötr Renkler */
        --cream: #f7f3e9; /* Daha rafine krem rengi */
        --light-cream: #fffbf2; /* Çok açık krem (arka plan için) */
        --beige: #e8dfc6; /* Bej tonu (alternatif arka plan) */

        /* Aksan Renkler */
        --gold: #c9a83f; /* Altın tonu (vurgular için) */
        --copper: #b56c45; /* Bakır tonu (ikincil vurgular) */
        --earth-brown: #7d5c3c; /* Toprak kahvesi */

        /* Metin Renkleri */
        --text-dark: #2a2a2a; /* Neredeyse siyah */
        --text-medium: #4a4a4a; /* Orta ton gri */
        --text-light: #6e6e6e; /* Açık gri */

        /* Gölge ve Efekt Renkleri */
        --shadow-color: rgba(0, 0, 0, 0.08);
        --overlay-light: rgba(255, 255, 255, 0.85);
        --overlay-dark: rgba(58, 83, 17, 0.85);
      }

      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Montserrat", sans-serif;
        line-height: 1.6;
        color: var(--text-dark);
        background-color: var(--light-cream);
        overflow-x: hidden;
        font-size: 16px;
        font-weight: 400;
        letter-spacing: 0.01em;
      }

      h1,
      h2,
      h3,
      h4,
      h5,
      h6 {
        font-family: "Cormorant Garamond", serif;
        font-weight: 600;
        color: var(--olive-green);
        line-height: 1.2;
        letter-spacing: -0.02em;
        margin-bottom: 0.5em;
      }

      h1 {
        font-size: 4rem;
        font-weight: 700;
      }

      h2 {
        font-size: 3rem;
        position: relative;
      }

      h3 {
        font-size: 2rem;
      }

      h4 {
        font-size: 1.5rem;
      }

      p {
        margin-bottom: 1.5rem;
        font-size: 1.05rem;
        line-height: 1.7;
      }

      .accent-font {
        font-family: "Italiana", serif;
        letter-spacing: 0.05em;
      }

      .text-gold {
        color: var(--gold);
      }

      .text-copper {
        color: var(--copper);
      }

      .text-olive {
        color: var(--olive-green);
      }

      .container {
        width: 90%;
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
      }

      .section {
        padding: 100px 0;
        position: relative;
        overflow: hidden;
      }

      .section-inner {
        position: relative;
        z-index: 2;
      }

      .section-title {
        text-align: center;
        margin-bottom: 70px;
        position: relative;
      }

      .section-title h2 {
        font-size: 3rem;
        margin-bottom: 20px;
        color: var(--olive-green);
        position: relative;
        display: inline-block;
      }

      .section-title .subtitle {
        font-family: "Montserrat", sans-serif;
        font-size: 1.1rem;
        color: var(--text-medium);
        max-width: 700px;
        margin: 0 auto 30px;
        font-weight: 400;
      }

      .section-title h2::after {
        content: "";
        position: absolute;
        bottom: -10px;
        left: 0;
        width: 100%;
        height: 2px;
        background: linear-gradient(
          90deg,
          transparent,
          var(--gold),
          transparent
        );
      }

      .section-title .icon-divider {
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 25px 0;
      }

      .section-title .icon-divider::before,
      .section-title .icon-divider::after {
        content: "";
        height: 1px;
        width: 60px;
        background: var(--gold);
        opacity: 0.5;
      }

      .section-title .icon-divider i {
        color: var(--gold);
        font-size: 1.2rem;
        margin: 0 15px;
      }

      /* Spacing utilities */
      .mt-1 {
        margin-top: 0.5rem;
      }
      .mt-2 {
        margin-top: 1rem;
      }
      .mt-3 {
        margin-top: 1.5rem;
      }
      .mt-4 {
        margin-top: 2rem;
      }
      .mt-5 {
        margin-top: 3rem;
      }

      .mb-1 {
        margin-bottom: 0.5rem;
      }
      .mb-2 {
        margin-bottom: 1rem;
      }
      .mb-3 {
        margin-bottom: 1.5rem;
      }
      .mb-4 {
        margin-bottom: 2rem;
      }
      .mb-5 {
        margin-bottom: 3rem;
      }

      .mx-auto {
        margin-left: auto;
        margin-right: auto;
      }

      /* Shadows and depth */
      .shadow-sm {
        box-shadow: 0 4px 10px var(--shadow-color);
      }

      .shadow-md {
        box-shadow: 0 8px 20px var(--shadow-color);
      }

      .shadow-lg {
        box-shadow: 0 15px 30px var(--shadow-color);
      }

      .depth-1 {
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05), 0 4px 8px rgba(0, 0, 0, 0.05);
      }

      .depth-2 {
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05),
          0 8px 16px rgba(0, 0, 0, 0.05);
      }

      .depth-3 {
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.05),
          0 16px 32px rgba(0, 0, 0, 0.05);
      }

      .btn {
        display: inline-block;
        padding: 14px 32px;
        border-radius: 50px;
        text-decoration: none;
        font-weight: 600;
        transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
        text-align: center;
        position: relative;
        overflow: hidden;
        z-index: 1;
        letter-spacing: 0.03em;
      }

      .btn::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(255, 255, 255, 0.1);
        transform: scaleX(0);
        transform-origin: right;
        transition: transform 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
        z-index: -1;
      }

      .btn:hover::after {
        transform: scaleX(1);
        transform-origin: left;
      }

      .btn-primary {
        background-color: var(--gold);
        color: white;
        box-shadow: 0 4px 15px rgba(201, 168, 63, 0.3);
        border: 2px solid var(--gold);
      }

      .btn-primary:hover {
        background-color: var(--gold);
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(201, 168, 63, 0.4);
      }

      .btn-outline {
        background-color: transparent;
        color: var(--olive-green);
        border: 2px solid var(--olive-green);
      }

      .btn-outline:hover {
        background-color: var(--olive-green);
        color: white;
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(58, 83, 17, 0.3);
      }

      .btn-large {
        padding: 16px 42px;
        font-size: 1.1rem;
      }

      .btn-with-icon {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
      }

      .btn-with-icon i {
        font-size: 1.2em;
        transition: transform 0.3s ease;
      }

      .btn-with-icon:hover i {
        transform: translateX(5px);
      }

      .hero .btn {
        transform: translateY(30px);
        opacity: 0;
        animation: fadeInUp 0.8s 0.8s forwards;
      }

      /* Hero Section */
      .hero {
        position: relative;
        height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        color: white;
        overflow: hidden;
      }

      .hero::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(
            rgba(44, 63, 14, 0.6),
            rgba(58, 83, 17, 0.3)
          ),
          url("https://www.dogalyasam.net/image/catalog/icons/new/olivePhoto00023.jpg")
            center/cover no-repeat;
        z-index: 1;
        animation: hero-zoom 20s infinite alternate ease-in-out;
      }

      @keyframes hero-zoom {
        0% {
          transform: scale(1);
        }
        100% {
          transform: scale(1.1);
        }
      }

      .hero::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 150px;
        background: linear-gradient(to top, var(--light-cream), transparent);
        z-index: 2;
      }

      .hero-content {
        max-width: 900px;
        padding: 2rem;
        z-index: 3;
        position: relative;
      }

      .hero-title {
        font-size: 4.5rem;
        margin-bottom: 1.5rem;
        font-weight: 700;
        text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        position: relative;
        transform: translateY(30px);
        opacity: 0;
        animation: fadeInUp 0.8s 0.4s forwards;
        color: #fffbf2;
      }

      .hero-title::after {
        content: "";
        position: absolute;
        bottom: -15px;
        left: 50%;
        transform: translateX(-50%);
        width: 100px;
        height: 3px;
        background: var(--gold);
        box-shadow: 0 2px 5px rgba(201, 168, 63, 0.3);
      }

      .hero-subtitle {
        font-size: 1.8rem;
        margin-bottom: 2.5rem;
        font-weight: 400;
        text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        transform: translateY(30px);
        opacity: 0;
        animation: fadeInUp 0.8s 0.6s forwards;
      }

      @keyframes fadeInUp {
        to {
          transform: translateY(0);
          opacity: 1;
        }
      }

      .scroll-indicator {
        position: absolute;
        bottom: 50px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 3;
        color: white;
        font-size: 2rem;
        animation: bounce 2s infinite;
        cursor: pointer;
        opacity: 0.8;
        transition: opacity 0.3s;
      }

      .scroll-indicator:hover {
        opacity: 1;
      }

      @keyframes bounce {
        0%,
        20%,
        50%,
        80%,
        100% {
          transform: translateY(0) translateX(-50%);
        }
        40% {
          transform: translateY(-20px) translateX(-50%);
        }
        60% {
          transform: translateY(-10px) translateX(-50%);
        }
      }

      /* Product Description Section */
      .product-description {
        background-color: white;
        padding: 100px 0;
        position: relative;
      }

      .product-description::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%233a5311' fill-opacity='0.03'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        opacity: 0.5;
        z-index: 0;
      }

      .description-text {
        max-width: 800px;
        margin: 0 auto 60px;
        text-align: center;
        font-size: 1.25rem;
        line-height: 1.9;
        color: var(--text-dark);
        position: relative;
        z-index: 1;
      }

      .description-text p {
        margin-bottom: 1.5rem;
      }

      .description-text p:first-of-type::first-letter {
        font-size: 3.5rem;
        font-family: "Cormorant Garamond", serif;
        font-weight: 700;
        float: left;
        line-height: 0.8;
        margin-right: 10px;
        color: var(--olive-green);
      }

      .description-text strong {
        color: var(--olive-green);
        font-weight: 600;
      }

      .highlights {
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
        gap: 30px;
        margin-top: 70px;
        position: relative;
        z-index: 1;
      }

      .highlight-box {
        flex: 1;
        min-width: 250px;
        max-width: 350px;
        padding: 40px 30px;
        background-color: var(--light-cream);
        border-radius: 15px;
        box-shadow: 0 10px 30px var(--shadow-color);
        display: flex;
        align-items: center;
        transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
        position: relative;
        overflow: hidden;
        border: 1px solid rgba(201, 168, 63, 0.1);
      }

      .highlight-box::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 5px;
        background: linear-gradient(90deg, var(--gold), var(--copper));
        transform: scaleX(0);
        transform-origin: left;
        transition: transform 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
      }

      .highlight-box:hover {
        transform: translateY(-15px);
        box-shadow: 0 20px 40px var(--shadow-color);
      }

      .highlight-box:hover::before {
        transform: scaleX(1);
      }

      .highlight-icon {
        width: 100px;
        margin-right: 25px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        transition: transform 0.3s ease;
      }

      .highlight-box:hover .highlight-icon {
        transform: scale(1.1);
      }

      .highlight-icon img {
        width: 100px;
        object-fit: contain;
      }

      .highlight-text h3 {
        font-size: 1.3rem;
        margin-bottom: 8px;
        color: var(--olive-green);
        font-weight: 600;
      }

      .highlight-text p {
        font-size: 1rem;
        color: var(--text-medium);
        margin-bottom: 0;
      }

      /* Benefits Section */
      .benefits {
        background-color: var(--cream);
        padding: 30px 0;
        position: relative;
        overflow: hidden;
      }

      .benefits::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%233a5311' fill-opacity='0.03' fill-rule='evenodd'/%3E%3C/svg%3E");
        opacity: 0.5;
      }

      .benefits .section-title::after {
        background: linear-gradient(
          90deg,
          transparent,
          var(--gold),
          transparent
        );
      }

      .benefits-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
        gap: 40px;
        position: relative;
        z-index: 1;
      }

      .benefit-card {
        background: white;
        border-radius: 20px;
        padding: 50px 30px;
        box-shadow: 0 15px 35px var(--shadow-color);
        text-align: center;
        transition: all 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
        position: relative;
        overflow: hidden;
        border: 1px solid rgba(201, 168, 63, 0.1);
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
      }

      .benefit-card::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          135deg,
          rgba(201, 168, 63, 0.05) 0%,
          rgba(58, 83, 17, 0.05) 100%
        );
        opacity: 0;
        transition: opacity 0.5s ease;
      }

      .benefit-card:hover {
        transform: translateY(-20px) scale(1.03);
        box-shadow: 0 25px 50px var(--shadow-color);
      }

      .benefit-card:hover::before {
        opacity: 1;
      }

      .benefit-icon {
        width: 100px;
        height: 100px;
        margin: 0 auto 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: var(--light-cream);
        border-radius: 50%;
        padding: 25px;
        box-shadow: 0 10px 25px var(--shadow-color);
        transition: all 0.5s ease;
        position: relative;
        z-index: 1;
      }

      .benefit-icon::after {
        content: "";
        position: absolute;
        top: -8px;
        left: -8px;
        right: -8px;
        bottom: -8px;
        border-radius: 50%;
        border: 2px dashed var(--gold);
        opacity: 0.3;
        transition: all 0.5s ease;
      }

      .benefit-card:hover .benefit-icon {
        transform: scale(1.1);
        background-color: rgba(201, 168, 63, 0.1);
      }

      .benefit-card:hover .benefit-icon::after {
        opacity: 0.7;
        transform: scale(1.1) rotate(30deg);
      }

      .benefit-icon img {
        width: 100px;
        object-fit: contain;
        transition: transform 0.5s ease;
      }

      .benefit-card:hover .benefit-icon img {
        transform: scale(1.1);
      }

      .benefit-card h3 {
        font-size: 1.8rem;
        margin-bottom: 20px;
        color: var(--olive-green);
        position: relative;
        padding-bottom: 15px;
        font-weight: 600;
      }

      .benefit-card h3::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 50px;
        height: 2px;
        background: var(--gold);
        transition: width 0.3s ease;
      }

      .benefit-card:hover h3::after {
        width: 80px;
      }

      .benefit-card p {
        font-size: 1.1rem;
        color: var(--text-medium);
        line-height: 1.7;
        margin-bottom: 0;
      }

      /* Timeline Section */
      .timeline-section {
        background-color: white;
        padding: 30px 0;
        position: relative;
        overflow: hidden;
      }

      .timeline-section::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: url("data:image/svg+xml,%3Csvg width='84' height='48' viewBox='0 0 84 48' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0 0h12v6H0V0zm28 8h12v6H28V8zm14-8h12v6H42V0zm14 0h12v6H56V0zm0 8h12v6H56V8zM42 8h12v6H42V8zm0 16h12v6H42v-6zm14-8h12v6H56v-6zm14 0h12v6H70v-6zm0-16h12v6H70V0zM28 32h12v6H28v-6zM14 16h12v6H14v-6zM0 24h12v6H0v-6zm0 8h12v6H0v-6zm14 0h12v6H14v-6zm14 8h12v6H28v-6zm-14 0h12v6H14v-6zm28 0h12v6H42v-6zm14-8h12v6H56v-6zm0-8h12v6H56v-6zm14 8h12v6H70v-6zm0 8h12v6H70v-6zM14 24h12v6H14v-6zm14-8h12v6H28v-6zM14 8h12v6H14V8zM0 8h12v6H0V8z' fill='%233a5311' fill-opacity='0.02' fill-rule='evenodd'/%3E%3C/svg%3E");
      }

      .timeline-section .section-title::after {
        background: linear-gradient(
          90deg,
          transparent,
          var(--olive-green),
          transparent
        );
      }

      /* Interactive Timeline */
      .interactive-timeline {
        max-width: 1000px;
        margin: 60px auto 0;
        position: relative;
      }

      /* Timeline Navigation */
      .timeline-navigation {
        display: flex;
        justify-content: space-between;
        margin-bottom: 40px;
        position: relative;
        z-index: 2;
      }

      .timeline-nav-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        cursor: pointer;
        position: relative;
        flex: 1;
        transition: all 0.3s ease;
        padding: 0 10px;
      }

      .timeline-nav-item .nav-dot {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        background-color: white;
        border: 3px solid var(--olive-green);
        margin-bottom: 15px;
        position: relative;
        z-index: 2;
        transition: all 0.3s ease;
      }

      .timeline-nav-item.active .nav-dot {
        background-color: var(--gold);
        border-color: var(--gold);
        transform: scale(1.2);
        box-shadow: 0 0 0 6px rgba(201, 168, 63, 0.2);
      }

      .timeline-nav-item span {
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--text-medium);
        transition: all 0.3s ease;
      }

      .timeline-nav-item.active span {
        color: var(--olive-green);
        font-weight: 700;
      }

      .timeline-nav-item:hover .nav-dot {
        transform: scale(1.2);
      }

      .timeline-nav-item:hover span {
        color: var(--olive-green);
      }

      /* Timeline Progress Bar */
      .timeline-progress-container {
        position: absolute;
        top: 12px;
        left: 0;
        width: 100%;
        z-index: 1;
      }

      .timeline-branch {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 4px;
        background-color: rgba(58, 83, 17, 0.15);
        border-radius: 2px;
        display: flex;
        justify-content: space-between;
        padding: 0 1px;
      }

      /* Add this after the timeline-branch is rendered via JavaScript */
      .timeline-segment {
        flex: 1;
        height: 100%;
        background-color: rgba(58, 83, 17, 0.2);
        border-radius: 2px;
        margin: 0 2px;
      }

      .timeline-progress {
        position: absolute;
        top: 0;
        left: 0;
        width: 0;
        height: 4px;
        background: linear-gradient(to right, var(--gold), var(--olive-green));
        transition: width 4s linear;
        box-shadow: 0 0 8px rgba(201, 168, 63, 0.5);
        border-radius: 0 2px 2px 0;
      }

      /* Add a pulsing effect at the end of the progress bar */
      .timeline-progress::after {
        content: "";
        position: absolute;
        top: -3px;
        right: -3px;
        width: 10px;
        height: 10px;
        background-color: var(--gold);
        border-radius: 50%;
        box-shadow: 0 0 10px var(--gold);
        animation: pulse 1s infinite;
      }

      @keyframes pulse {
        0% {
          transform: scale(0.8);
          opacity: 0.8;
        }
        50% {
          transform: scale(1.2);
          opacity: 1;
        }
        100% {
          transform: scale(0.8);
          opacity: 0.8;
        }
      }

      /* Timeline Content */
      .timeline-content-wrapper {
        position: relative;
        overflow: hidden;
        min-height: 500px;
      }

      .timeline-step {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        opacity: 0;
        visibility: hidden;
        transform: translateX(50px);
        transition: all 0.5s ease;
      }

      .timeline-step.active {
        opacity: 1;
        visibility: visible;
        transform: translateX(0);
      }

      .timeline-step-content {
        display: flex;
        flex-wrap: wrap;
        gap: 40px;
        background-color: white;
        border-radius: 20px;
        box-shadow: 0 20px 50px var(--shadow-color);
        overflow: hidden;
      }

      .step-image-container {
        flex: 1;
        min-width: 300px;
        position: relative;
        overflow: hidden;
      }

      .step-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.7s ease;
      }

      .timeline-step-content:hover .step-image {
        transform: scale(1.05);
      }

      .step-image-overlay {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        padding: 20px;
        background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
      }

      .step-date {
        display: inline-block;
        padding: 8px 16px;
        background-color: var(--gold);
        color: white;
        font-weight: 600;
        border-radius: 30px;
        font-size: 0.9rem;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
      }

      .step-details {
        flex: 1;
        min-width: 300px;
        padding: 40px;
      }

      .step-title {
        font-size: 2rem;
        color: var(--olive-green);
        margin-bottom: 20px;
        position: relative;
        padding-bottom: 15px;
      }

      .step-title::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 0;
        width: 60px;
        height: 3px;
        background: var(--gold);
      }

      .step-description {
        font-size: 1.1rem;
        line-height: 1.7;
        color: var(--text-medium);
        margin-bottom: 30px;
      }

      .step-facts {
        margin-bottom: 30px;
      }

      .fact {
        display: flex;
        align-items: flex-start;
        margin-bottom: 15px;
      }

      .fact i {
        color: var(--gold);
        margin-right: 15px;
        margin-top: 3px;
      }

      .fact span {
        font-size: 1rem;
        color: var(--text-medium);
      }

      .step-more-btn {
        display: inline-block;
        padding: 12px 25px;
        background-color: var(--olive-green);
        color: white;
        border: none;
        border-radius: 30px;
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 5px 15px rgba(58, 83, 17, 0.3);
      }

      .step-more-btn:hover {
        background-color: var(--gold);
        transform: translateY(-3px);
        box-shadow: 0 8px 20px rgba(201, 168, 63, 0.4);
      }

      /* Modal Popups */
      .timeline-modal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.8);
        z-index: 9999;
        overflow-y: auto;
        padding: 50px 0;
      }

      .modal-content {
        position: relative;
        max-width: 900px;
        margin: 0 auto;
        background-color: white;
        border-radius: 20px;
        overflow: hidden;
        box-shadow: 0 20px 50px rgba(0, 0, 0, 0.3);
        animation: modalFadeIn 0.5s ease;
      }

      @keyframes modalFadeIn {
        from {
          opacity: 0;
          transform: translateY(50px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .modal-close {
        position: absolute;
        top: 20px;
        right: 20px;
        font-size: 2rem;
        color: white;
        cursor: pointer;
        z-index: 2;
        transition: all 0.3s ease;
      }

      .modal-close:hover {
        transform: rotate(90deg);
      }

      .modal-header {
        padding: 25px 40px;
        background-color: var(--olive-green);
        color: white;
      }

      .modal-header h3 {
        font-size: 1.8rem;
        margin: 0;
      }

      .modal-body {
        display: flex;
        flex-wrap: wrap;
      }

      .modal-image {
        flex: 1;
        min-width: 300px;
        max-width: 400px;
      }

      .modal-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .modal-text {
        flex: 1;
        min-width: 300px;
        padding: 40px;
      }

      .modal-text p {
        font-size: 1.1rem;
        line-height: 1.7;
        color: var(--text-medium);
        margin-bottom: 20px;
      }

      .modal-text h4 {
        font-size: 1.4rem;
        color: var(--olive-green);
        margin: 30px 0 15px;
      }

      .modal-text ul {
        padding-left: 20px;
      }

      .modal-text li {
        font-size: 1.05rem;
        color: var(--text-medium);
        margin-bottom: 10px;
        line-height: 1.5;
      }

      /* Responsive Styles */
      @media (max-width: 768px) {
        .timeline-step-content {
          flex-direction: column;
        }

        .step-image-container {
          height: 250px;
        }

        .step-details {
          padding: 30px;
        }

        .step-title {
          font-size: 1.6rem;
        }

        .modal-body {
          flex-direction: column;
        }

        .modal-image {
          max-width: 100%;
          height: 250px;
        }

        .modal-text {
          padding: 30px;
        }
      }

      /* Usage Section */
      .usage-section {
        background-color: var(--cream);
        padding: 30px 0;
        position: relative;
        overflow: hidden;
      }

      .usage-section::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%233a5311' fill-opacity='0.03' fill-rule='evenodd'%3E%3Ccircle cx='3' cy='3' r='3'/%3E%3Ccircle cx='13' cy='13' r='3'/%3E%3C/g%3E%3C/svg%3E");
      }

      .usage-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 40px;
        position: relative;
        z-index: 1;
      }

      .usage-card {
        background: white;
        border-radius: 20px;
        padding: 0;
        box-shadow: 0 15px 35px var(--shadow-color);
        text-align: center;
        transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
        position: relative;
        overflow: hidden;
        height: 100%;
        display: flex;
        flex-direction: column;
      }

      .usage-card::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          135deg,
          rgba(201, 168, 63, 0.05) 0%,
          rgba(58, 83, 17, 0.05) 100%
        );
        opacity: 0;
        transition: opacity 0.4s ease;
        z-index: 0;
      }

      .usage-card:hover {
        transform: translateY(-15px) scale(1.02);
        box-shadow: 0 25px 50px var(--shadow-color);
      }

      .usage-card:hover::after {
        opacity: 1;
      }

      .usage-card-header {
        background-color: var(--olive-green);
        padding: 10px 20px;
        border-radius: 20px 20px 0 0;
        position: relative;
        z-index: 1;
      }

      .usage-card:nth-child(2) .usage-card-header {
        background-color: var(--gold);
      }

      .usage-card:nth-child(3) .usage-card-header {
        background-color: var(--copper);
      }

      .usage-card:nth-child(4) .usage-card-header {
        background-color: var(--earth-brown);
      }

      .usage-icon {
        width: 100px;
        margin: 0 auto;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        padding: 5px;
        transition: all 0.4s ease;
        position: relative;
        z-index: 2;
      }

      .usage-card:hover .usage-icon {
        transform: scale(1.1) rotate(5deg);
      }

      .usage-icon img {
        width: 100%;
        height: 100%;
        object-fit: contain;
        transition: transform 0.4s ease;
      }

      .usage-card:hover .usage-icon img {
        transform: scale(1.1);
      }

      .usage-card-body {
        padding: 30px;
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        position: relative;
        z-index: 1;
      }

      .usage-card h3 {
        font-size: 1.5rem;
        margin-bottom: 15px;
        color: var(--olive-green);
        font-weight: 600;
      }

      .usage-card:nth-child(2) h3 {
        color: var(--gold);
      }

      .usage-card:nth-child(3) h3 {
        color: var(--copper);
      }

      .usage-card:nth-child(4) h3 {
        color: var(--earth-brown);
      }

      .usage-card p {
        font-size: 1.05rem;
        color: var(--text-medium);
        line-height: 1.6;
        margin-bottom: 20px;
      }

      .usage-examples {
        font-size: 0.9rem;
        color: var(--text-light);
        font-style: italic;
        margin-top: auto;
      }

      .usage-card-footer {
        padding: 15px;
        border-top: 1px solid rgba(0, 0, 0, 0.05);
        font-size: 0.9rem;
        color: var(--text-light);
        background-color: rgba(245, 245, 245, 0.5);
      }

      /* Storage Section */
      .storage-section {
        background-color: white;
        padding: 30px 0;
        position: relative;
        overflow: hidden;
      }

      .storage-section::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: url("data:image/svg+xml,%3Csvg width='52' height='26' viewBox='0 0 52 26' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%233a5311' fill-opacity='0.02'%3E%3Cpath d='M10 10c0-2.21-1.79-4-4-4-3.314 0-6-2.686-6-6h2c0 2.21 1.79 4 4 4 3.314 0 6 2.686 6 6 0 2.21 1.79 4 4 4 3.314 0 6 2.686 6 6 0 2.21 1.79 4 4 4v2c-3.314 0-6-2.686-6-6 0-2.21-1.79-4-4-4-3.314 0-6-2.686-6-6zm25.464-1.95l8.486 8.486-1.414 1.414-8.486-8.486 1.414-1.414z' /%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
      }

      .storage-container {
        display: flex;
        flex-wrap: wrap;
        gap: 40px;
        align-items: center;
        justify-content: center;
        position: relative;
        z-index: 1;
      }

      .storage-image {
        flex: 1;
        min-width: 300px;
        max-width: 500px;
        position: relative;
      }

      .storage-image img {
        width: 100%;
        height: auto;
        border-radius: 20px;
        box-shadow: 0 20px 40px var(--shadow-color);
        transition: transform 0.5s ease;
      }

      .storage-image:hover img {
        transform: scale(1.03);
      }

      .storage-image::after {
        content: "";
        position: absolute;
        top: 20px;
        left: 20px;
        right: 20px;
        bottom: 20px;
        border: 2px dashed var(--gold);
        border-radius: 20px;
        opacity: 0.3;
        z-index: -1;
        transition: all 0.5s ease;
      }

      .storage-image:hover::after {
        top: 10px;
        left: 10px;
        right: 10px;
        bottom: 10px;
        opacity: 0.6;
      }

      .storage-content {
        flex: 1;
        min-width: 300px;
        max-width: 600px;
      }

      .storage-card {
        background-color: var(--light-cream);
        border-radius: 20px;
        padding: 50px;
        box-shadow: 0 20px 40px var(--shadow-color);
        position: relative;
        overflow: hidden;
        border: 1px solid rgba(201, 168, 63, 0.1);
      }

      .storage-card::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 5px;
        height: 100%;
        background: linear-gradient(to bottom, var(--gold), var(--olive-green));
      }

      .storage-icon {
        width: 100px;
        height: 100px;
        margin: 0 auto 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: white;
        border-radius: 50%;
        padding: 20px;
        box-shadow: 0 10px 25px var(--shadow-color);
        position: relative;
      }

      .storage-icon::after {
        content: "";
        position: absolute;
        top: -8px;
        left: -8px;
        right: -8px;
        bottom: -8px;
        border-radius: 50%;
        border: 2px dashed var(--gold);
        opacity: 0.3;
        animation: rotate 20s linear infinite;
      }

      @keyframes rotate {
        from {
          transform: rotate(0deg);
        }
        to {
          transform: rotate(360deg);
        }
      }

      .storage-icon img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }

      .storage-card h3 {
        font-size: 2rem;
        margin-bottom: 25px;
        color: var(--olive-green);
        text-align: center;
        position: relative;
        padding-bottom: 15px;
      }

      .storage-card h3::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 60px;
        height: 2px;
        background: var(--gold);
      }

      .storage-card p {
        font-size: 1.2rem;
        margin-bottom: 20px;
        color: var(--text-dark);
        line-height: 1.7;
      }

      .storage-tips {
        margin-top: 30px;
      }

      .storage-tip {
        display: flex;
        align-items: flex-start;
        margin-bottom: 15px;
        padding-bottom: 15px;
        border-bottom: 1px dashed rgba(0, 0, 0, 0.1);
      }

      .storage-tip:last-child {
        margin-bottom: 0;
        padding-bottom: 0;
        border-bottom: none;
      }

      .storage-tip i {
        color: var(--gold);
        font-size: 1.2rem;
        margin-right: 15px;
        margin-top: 3px;
      }

      .storage-tip p {
        margin-bottom: 0;
        font-size: 1.1rem;
      }

      /* Gallery Section */
      .gallery-section {
        background-color: var(--cream);
        padding: 30px 0;
        position: relative;
        overflow: hidden;
      }

      .gallery-section::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: url("data:image/svg+xml,%3Csvg width='44' height='12' viewBox='0 0 44 12' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M20 12v-2L0 0v10l4 2h16zm18 0l4-2V0L22 10v2h16zM20 0v8L4 0h16zm18 0L22 8V0h16z' fill='%233a5311' fill-opacity='0.02' fill-rule='evenodd'/%3E%3C/svg%3E");
      }

      .gallery-grid {
        display: grid;
        grid-template-columns: repeat(12, 1fr);
        grid-auto-rows: 200px;
        gap: 20px;
        position: relative;
        z-index: 1;
      }

      .gallery-item {
        position: relative;
        overflow: hidden;
        border-radius: 20px;
        box-shadow: 0 15px 35px var(--shadow-color);
        transition: all 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
        cursor: pointer;
      }

      .gallery-item:nth-child(1) {
        grid-column: span 6;
        grid-row: span 2;
      }

      .gallery-item:nth-child(2) {
        grid-column: span 6;
        grid-row: span 1;
      }

      .gallery-item:nth-child(3) {
        grid-column: span 3;
        grid-row: span 1;
      }

      .gallery-item:nth-child(4) {
        grid-column: span 3;
        grid-row: span 1;
      }

      .gallery-item:hover {
        transform: translateY(-10px);
        box-shadow: 0 25px 50px var(--shadow-color);
        z-index: 2;
      }

      .gallery-item img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.7s ease;
      }

      .gallery-item:hover img {
        transform: scale(1.1);
      }

      .gallery-item::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          to top,
          rgba(0, 0, 0, 0.5),
          transparent 50%
        );
        opacity: 0;
        transition: opacity 0.5s ease;
      }

      .gallery-item:hover::after {
        opacity: 1;
      }

      .gallery-caption {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        padding: 20px;
        color: white;
        z-index: 2;
        transform: translateY(100%);
        transition: transform 0.5s ease;
      }

      .gallery-item:hover .gallery-caption {
        transform: translateY(0);
      }

      .gallery-caption h4 {
        font-size: 1.2rem;
        margin-bottom: 5px;
        color: white;
      }

      .gallery-caption p {
        font-size: 0.9rem;
        margin-bottom: 0;
        opacity: 0.9;
      }

      /* Lightbox */
      .lightbox {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
      }

      .lightbox.active {
        opacity: 1;
        visibility: visible;
      }

      .lightbox-content {
        max-width: 90%;
        max-height: 90%;
        position: relative;
      }

      .lightbox-content img {
        max-width: 100%;
        max-height: 90vh;
        border: 5px solid white;
        box-shadow: 0 0 30px rgba(0, 0, 0, 0.5);
      }

      .lightbox-close {
        position: absolute;
        top: 20px;
        right: 20px;
        color: white;
        font-size: 2rem;
        cursor: pointer;
        z-index: 10000;
      }

      @media (max-width: 768px) {
        .gallery-grid {
          grid-template-columns: repeat(6, 1fr);
          grid-auto-rows: 150px;
        }

        .gallery-item:nth-child(1) {
          grid-column: span 6;
          grid-row: span 2;
        }

        .gallery-item:nth-child(2) {
          grid-column: span 3;
          grid-row: span 1;
        }

        .gallery-item:nth-child(3) {
          grid-column: span 3;
          grid-row: span 1;
        }

        .gallery-item:nth-child(4) {
          grid-column: span 6;
          grid-row: span 1;
        }
      }

      /* CTA Section */
      .cta-section {
        background: linear-gradient(
          135deg,
          var(--olive-green),
          var(--dark-olive)
        );
        padding: 30px 0;
        text-align: center;
        color: white;
        position: relative;
        overflow: hidden;
      }

      .cta-section::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image: url("https://www.dogalyasam.net/image/catalog/icons/new/olivePhoto0006.jpg");
        background-size: cover;
        background-position: center;
        opacity: 0.1;
        z-index: 1;
      }

      .cta-section::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: radial-gradient(
          circle at center,
          transparent 0%,
          var(--olive-green) 70%
        );
        z-index: 2;
      }

      .cta-content {
        max-width: 900px;
        margin: 0 auto;
        position: relative;
        z-index: 3;
      }

      .cta-title {
        font-size: 3.5rem;
        margin-bottom: 30px;
        color: white;
        text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        position: relative;
        display: inline-block;
      }

      .cta-title::after {
        content: "";
        position: absolute;
        bottom: -15px;
        left: 50%;
        transform: translateX(-50%);
        width: 100px;
        height: 3px;
        background: var(--gold);
        box-shadow: 0 0 10px var(--gold);
      }

      .cta-text {
        font-size: 1.4rem;
        margin-bottom: 50px;
        line-height: 1.7;
        max-width: 700px;
        margin-left: auto;
        margin-right: auto;
      }

      .btn-cta {
        background-color: var(--gold);
        color: white;
        font-size: 1.3rem;
        padding: 18px 50px;
        border: 2px solid var(--gold);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        position: relative;
        overflow: hidden;
        z-index: 1;
      }

      .btn-cta::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: white;
        transform: scaleX(0);
        transform-origin: right;
        transition: transform 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
        z-index: -1;
      }

      .btn-cta:hover {
        color: var(--gold);
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
      }

      .btn-cta:hover::before {
        transform: scaleX(1);
        transform-origin: left;
      }

      .price-info {
        margin-top: 30px;
        font-size: 1.1rem;
        opacity: 0.9;
        background-color: rgba(255, 255, 255, 0.1);
        display: inline-block;
        padding: 10px 25px;
        border-radius: 50px;
        backdrop-filter: blur(5px);
        -webkit-backdrop-filter: blur(5px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
      }

      .price-info strong {
        color: var(--gold);
        font-weight: 700;
      }

      .cta-badge {
        position: absolute;
        top: 30px;
        right: 30px;
        background-color: var(--gold);
        color: white;
        padding: 10px 20px;
        border-radius: 50px;
        font-weight: 600;
        font-size: 0.9rem;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        transform: rotate(5deg);
        z-index: 3;
      }

      /* Kategori Kartları */
      .category-cards {
        display: flex;
        justify-content: center;
        gap: 2.5rem;
        margin: 3rem auto 0;
        max-width: 900px;
      }

      .category-card {
        width: 350px;
        background-color: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border-radius: 20px;
        overflow: hidden;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
        transition: all 0.4s ease;
        text-decoration: none;
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.2);
      }

      .category-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        border-color: var(--gold);
      }

      .category-card-image {
        height: 200px;
        overflow: hidden;
        position: relative;
      }

      .category-card-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.6s ease;
      }

      .category-card:hover .category-card-image img {
        transform: scale(1.1);
      }

      .category-card-content {
        padding: 1.5rem;
        text-align: center;
      }

      .category-card-title {
        font-size: 1.4rem;
        font-weight: 600;
        margin-bottom: 0.8rem;
        color: white;
      }

      .category-card-description {
        font-size: 1rem;
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 1.2rem;
        line-height: 1.5;
      }

      .category-card-button {
        display: inline-block;
        padding: 0.6rem 1.5rem;
        background-color: var(--gold);
        color: #333;
        border-radius: 30px;
        font-weight: 600;
        font-size: 0.9rem;
        transition: all 0.3s ease;
      }

      .category-card:hover .category-card-button {
        background-color: white;
        color: var(--olive-green);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
      }

      @media (max-width: 768px) {
        .category-cards {
          flex-direction: column;
          align-items: center;
          gap: 2rem;
        }

        .category-card {
          width: 100%;
          max-width: 350px;
        }
      }

      /* Media Queries */
      @media (max-width: 992px) {
        .section {
          padding: 80px 0;
        }

        .hero-title {
          font-size: 3.5rem;
        }

        .cta-title {
          font-size: 3rem;
        }

        .storage-container {
          flex-direction: column;
          align-items: center;
        }

        .storage-image {
          margin-bottom: 40px;
        }
      }

      @media (max-width: 768px) {
        .hero-title {
          font-size: 2.8rem;
        }

        .hero-subtitle {
          font-size: 1.4rem;
        }

        .section-title h2 {
          font-size: 2.2rem;
        }

        .cta-title {
          font-size: 2.5rem;
        }

        .cta-text {
          font-size: 1.2rem;
        }

        .btn-cta {
          padding: 15px 40px;
          font-size: 1.1rem;
        }

        .timeline::after {
          left: 31px;
        }

        .timeline-item {
          width: 100%;
          padding-left: 70px;
          padding-right: 25px;
        }

        .timeline-item.right {
          left: 0;
        }

        .timeline-item::after {
          left: 18px;
        }

        .timeline-item.right::after {
          left: 18px;
        }

        .timeline-date {
          left: 70px;
          top: 15px;
        }

        .timeline-item.left .timeline-date {
          right: auto;
          left: 70px;
        }

        .highlights {
          flex-direction: column;
          align-items: center;
        }

        .highlight-box {
          width: 100%;
          max-width: 100%;
        }

        .benefit-card {
          padding: 40px 20px;
        }

        .cta-badge {
          top: 20px;
          right: 20px;
          font-size: 0.8rem;
          padding: 8px 15px;
        }
      }

      /* Discount Alert */
      .discount-alert {
        position: fixed;
        bottom: 30px;
        right: 30px;
        background-color: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
        z-index: 9999;
        transform: translateY(150%);
        opacity: 0;
        transition: all 0.5s cubic-bezier(0.68, -0.55, 0.27, 1.55);
        border-left: 4px solid var(--gold);
        overflow: hidden;
      }

      .discount-alert.show {
        transform: translateY(0);
        opacity: 1;
      }

      .discount-alert-content {
        display: flex;
        align-items: center;
        padding: 15px 20px;
      }

      .discount-alert-icon {
        background-color: var(--gold);
        color: white;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        font-size: 1.2rem;
      }

      .discount-alert-text {
        font-size: 1rem;
        color: var(--text-dark);
        margin-right: 20px;
      }

      .discount-alert-close {
        color: var(--text-light);
        cursor: pointer;
        font-size: 1rem;
        transition: all 0.3s ease;
      }

      .discount-alert-close:hover {
        color: var(--text-dark);
        transform: scale(1.1);
      }

      @media (max-width: 576px) {
        .discount-alert {
          left: 20px;
          right: 20px;
          bottom: 20px;
        }

        .hero-title {
          font-size: 2.2rem;
        }

        .hero-subtitle {
          font-size: 1.2rem;
        }

        .section-title h2 {
          font-size: 1.8rem;
        }

        .section-title .subtitle {
          font-size: 1rem;
        }

        .description-text {
          font-size: 1.1rem;
        }

        .highlight-box {
          padding: 30px 20px;
          flex-direction: column;
          text-align: center;
        }

        .highlight-icon {
          margin-right: 0;
          margin-bottom: 20px;
        }

        .benefit-icon {
          width: 80px;
          height: 80px;
        }

        .timeline-content h3 {
          font-size: 1.3rem;
        }

        .usage-card-header {
          padding: 10px 15px;
        }

        .usage-card-body {
          padding: 20px 15px;
        }

        .usage-card h3 {
          font-size: 1.3rem;
        }

        .storage-card {
          padding: 30px 20px;
        }

        .storage-card h3 {
          font-size: 1.6rem;
        }

        .storage-tip p {
          font-size: 1rem;
        }

        .cta-title {
          font-size: 2rem;
        }

        .cta-text {
          font-size: 1.1rem;
        }

        .btn-cta {
          padding: 12px 30px;
          font-size: 1rem;
        }

        .price-info {
          font-size: 0.9rem;
          padding: 8px 20px;
        }
      }
    </style>
  </head>
  <body>
    <!-- Discount Alert -->
    <div class="discount-alert">
      <div class="discount-alert-content">
        <div class="discount-alert-icon">
          <i class="fas fa-percentage"></i>
        </div>
        <div class="discount-alert-text">
          <strong>%20 İndirim!</strong> Tüm zeytinyağlarında sepette geçerli.
        </div>
        <div class="discount-alert-close">
          <i class="fas fa-times"></i>
        </div>
      </div>
    </div>

    <!-- Hero Section -->
    <section class="hero">
      <div class="hero-content">
        <h1 class="hero-title">Erken Hasat Naturel Sızma Zeytinyağı</h1>
        <p class="hero-subtitle">
          Yeşil zeytinin en taze hâli sofralarınıza geliyor.
        </p>
        <a
          href="https://www.dogalyasam.net/erken-hasat-naturel-sizma-zeytinyagi"
          class="btn btn-primary btn-large btn-with-icon"
        >
          Hemen Satın Al <i class="fas fa-arrow-right"></i>
        </a>
      </div>
      <a href="#product-description" class="scroll-indicator">
        <i class="fas fa-chevron-down"></i>
      </a>
    </section>

    <!-- Product Description Section -->
    <section id="product-description" class="product-description section">
      <div class="container">
        <div class="section-title">
          <h2 data-aos="fade-up">Erken Hasat Naturel Sızma Zeytinyağı</h2>
          <div class="icon-divider" data-aos="fade-up" data-aos-delay="100">
            <i class="fas fa-leaf"></i>
          </div>
          <p class="subtitle" data-aos="fade-up" data-aos-delay="200">
            Doğanın en saf hali, zeytinin en değerli özü
          </p>
        </div>
        <div class="description-text" data-aos="fade-up" data-aos-delay="300">
          <p>
            Erken hasat naturel sızma zeytinyağı, zeytinin olgunlaşmadan hemen
            önceki <strong>"yeşil" döneminde</strong> hasat edilip işlenmesiyle
            ortaya çıkar. Bizim bahçelerimizde ekim ayı başında elle toplanan
            tek tip erkence zeytinler, maksimum tazelik ve besin değeri için
            <strong>4 saat içinde sıkılır</strong>. Hiçbir şekilde ısıl işleme
            ya da kimyasal katkıya maruz kalmadan üretilen Naturel Sızma
            Zeytinyağımız, Akdeniz mutfağının vazgeçilmez lezzet ve sağlık
            kaynağıdır.
          </p>
        </div>
        <div class="highlights">
          <div class="highlight-box" data-aos="fade-up" data-aos-delay="400">
            <div class="highlight-icon">
              <img
                src="https://www.dogalyasam.net/image/catalog/icons/new/oilTime.png"
                alt="4 Saat İçinde Soğuk Sıkım"
              />
            </div>
            <div class="highlight-text">
              <h3>4 Saat İçinde Soğuk Sıkım</h3>
              <p>Hasattan hemen sonra işleme alınır</p>
            </div>
          </div>
          <div class="highlight-box" data-aos="fade-up" data-aos-delay="500">
            <div class="highlight-icon">
              <img
                src="https://www.dogalyasam.net/image/catalog/icons/new/medicin-icon-2.png"
                alt="Hiçbir Kimyasal Yok"
              />
            </div>
            <div class="highlight-text">
              <h3>Hiçbir Kimyasal Yok</h3>
              <p>Tamamen doğal üretim süreci</p>
            </div>
          </div>
          <div class="highlight-box" data-aos="fade-up" data-aos-delay="600">
            <div class="highlight-icon">
              <img
                src="https://www.dogalyasam.net/image/catalog/icons/new/stomic-icon-2.png"
                alt="Elle Hasat, Elle Seçim"
              />
            </div>
            <div class="highlight-text">
              <h3>Mide Dostu</h3>
              <p>Mideye hiç rahatsız etmez meyve suyu gibi içilebilir</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Benefits Section -->
    <section class="benefits section">
      <div class="container">
        <div class="section-title">
          <h2 data-aos="fade-up">
            Neden Erken Hasat Naturel Sızma Zeytinyağı?
          </h2>
          <div class="icon-divider" data-aos="fade-up" data-aos-delay="100">
            <i class="fas fa-olive-branch"></i>
          </div>
          <p class="subtitle" data-aos="fade-up" data-aos-delay="200">
            Erken hasadın eşsiz avantajları
          </p>
        </div>
        <div class="benefits-grid">
          <div class="benefit-card" data-aos="fade-up" data-aos-delay="300">
            <div class="benefit-icon">
              <img
                src="https://www.dogalyasam.net/image/catalog/icons/new/olive-icon-4.png"
                alt="Antioksidan"
              />
            </div>
            <h3>Yoğun Antioksidan</h3>
            <p>
              Klorofil, polifenol ve yağ asidi profili olgun yağlara kıyasla çok
              daha yüksek. Bu sayede vücudunuzu serbest radikallere karşı daha
              güçlü korur.
            </p>
          </div>
          <div class="benefit-card" data-aos="fade-up" data-aos-delay="400">
            <div class="benefit-icon">
              <img
                src="https://www.dogalyasam.net/image/catalog/icons/new/olive-icon-5.png"
                alt="Aroma"
              />
            </div>
            <h3>Keskin & Meyvemsi Aroma</h3>
            <p>
              Yeşil zeytinin canlı kokusu ve hafif biber ısırımı barındırır. Bu
              karakteristik tat, yemeklerinize benzersiz bir derinlik katar.
            </p>
          </div>
          <div class="benefit-card" data-aos="fade-up" data-aos-delay="500">
            <div class="benefit-icon">
              <img
                src="https://www.dogalyasam.net/image/catalog/icons/new/olive-icon-2.png"
                alt="Tazelik"
              />
            </div>
            <h3>Saf Tazelik</h3>
            <p>
              4 saat içinde sıkım ve düşük sıcaklıkta işlem, doğal enzim ve
              vitaminleri korur. Bu hızlı süreç, zeytinin tüm besin değerlerini
              şişeye taşır.
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- Timeline Section -->
    <section class="timeline-section section">
      <div class="container">
        <div class="section-title">
          <h2 data-aos="fade-up">Üretim Sürecimiz – Zaman Çizelgesi</h2>
          <div class="icon-divider" data-aos="fade-up" data-aos-delay="100">
            <i class="fas fa-seedling"></i>
          </div>
          <p class="subtitle" data-aos="fade-up" data-aos-delay="200">
            Zeytinden şişeye uzanan yolculuk
          </p>
        </div>

        <div
          class="interactive-timeline"
          data-aos="fade-up"
          data-aos-delay="300"
        >
          <!-- Timeline Navigation -->
          <div class="timeline-navigation">
            <div class="timeline-nav-item active" data-step="1">
              <div class="nav-dot"></div>
              <span>Hasat</span>
            </div>
            <div class="timeline-nav-item" data-step="2">
              <div class="nav-dot"></div>
              <span>Soğuk Pres</span>
            </div>
            <div class="timeline-nav-item" data-step="3">
              <div class="nav-dot"></div>
              <span>Şişeleme</span>
            </div>
          </div>

          <!-- Timeline Progress Bar -->
          <div class="timeline-progress-container">
            <div class="timeline-branch"></div>
            <div class="timeline-progress"></div>
          </div>

          <!-- Timeline Content -->
          <div class="timeline-content-wrapper">
            <!-- Step 1 -->
            <div class="timeline-step active" id="step-1">
              <div class="timeline-step-content">
                <div class="step-image-container">
                  <img
                    src="https://www.dogalyasam.net/image/catalog/icons/new/OlivePhoto00000016.jpg"
                    alt="Elle Hasat"
                    class="step-image"
                  />
                  <div class="step-image-overlay">
                    <span class="step-date">1. Gün</span>
                  </div>
                </div>
                <div class="step-details">
                  <h3 class="step-title">Ekim Başı: Elle Hasat</h3>
                  <p class="step-description">
                    Bahçelerimizden tek tek toplanan erkence zeytinler özenle
                    seçilir. Sadece en kaliteli taneler, erken hasat
                    zeytinyağımız için ayrılır.
                  </p>
                  <div class="step-facts">
                    <div class="fact">
                      <i class="fas fa-check-circle"></i>
                      <span>Mekanik hasat yöntemleri yerine elle toplama</span>
                    </div>
                    <div class="fact">
                      <i class="fas fa-check-circle"></i>
                      <span>Zeytinlerin zarar görmeden işleme alınması</span>
                    </div>
                    <div class="fact">
                      <i class="fas fa-check-circle"></i>
                      <span>Ekim ayının ilk 10 günü içinde hasat</span>
                    </div>
                  </div>
                  <button class="step-more-btn" data-modal="modal-1">
                    Daha Fazla Bilgi
                  </button>
                </div>
              </div>
            </div>

            <!-- Step 2 -->
            <div class="timeline-step" id="step-2">
              <div class="timeline-step-content">
                <div class="step-image-container">
                  <img
                    src="https://www.dogalyasam.net/image/catalog/icons/new/OlivePhoto00000010.jpg"
                    alt="Soğuk Pres"
                    class="step-image"
                  />
                  <div class="step-image-overlay">
                    <span class="step-date">Aynı Gün</span>
                  </div>
                </div>
                <div class="step-details">
                  <h3 class="step-title">Soğuk Pres (4 saat içinde)</h3>
                  <p class="step-description">
                    Bekletme süresi olmadan, soğuk pres tekniğiyle zeytinler
                    sıkılır. Sıcaklık 27°C'nin altında tutularak, zeytinin tüm
                    besin değerleri korunur.
                  </p>
                  <div class="step-facts">
                    <div class="fact">
                      <i class="fas fa-check-circle"></i>
                      <span>Hasattan sonra 4 saat içinde işleme</span>
                    </div>
                    <div class="fact">
                      <i class="fas fa-check-circle"></i>
                      <span>27°C'nin altında sıkım</span>
                    </div>
                    <div class="fact">
                      <i class="fas fa-check-circle"></i>
                      <span>Oksidasyon ve fermentasyonu önleme</span>
                    </div>
                  </div>
                  <button class="step-more-btn" data-modal="modal-2">
                    Daha Fazla Bilgi
                  </button>
                </div>
              </div>
            </div>

            <!-- Step 3 -->
            <div class="timeline-step" id="step-3">
              <div class="timeline-step-content">
                <div class="step-image-container">
                  <img
                    src="https://www.dogalyasam.net/image/catalog/icons/new/coldBatle.png"
                    alt="Şişeleme"
                    class="step-image"
                  />
                  <div class="step-image-overlay">
                    <span class="step-date">48 Saat Sonra</span>
                  </div>
                </div>
                <div class="step-details">
                  <h3 class="step-title">Koyu Cam Şişe Dolum & Paketleme</h3>
                  <p class="step-description">
                    Işık ve ısıdan korumak için özel koyu cam şişelere dolum
                    yapılır. Koyu renkli cam şişeler, zeytinyağını ışığın
                    zararlı etkilerinden korur.
                  </p>
                  <div class="step-facts">
                    <div class="fact">
                      <i class="fas fa-check-circle"></i>
                      <span>Koyu cam şişeler ile UV koruması</span>
                    </div>
                    <div class="fact">
                      <i class="fas fa-check-circle"></i>
                      <span>Özel kapak ile hava temasının önlenmesi</span>
                    </div>
                    <div class="fact">
                      <i class="fas fa-check-circle"></i>
                      <span>Uzun raf ömrü sağlayan paketleme</span>
                    </div>
                  </div>
                  <button class="step-more-btn" data-modal="modal-3">
                    Daha Fazla Bilgi
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Modal Popups -->
        <div class="timeline-modal" id="modal-1">
          <div class="modal-content">
            <span class="modal-close">&times;</span>
            <div class="modal-header">
              <h3>Elle Hasat Süreci</h3>
            </div>
            <div class="modal-body">
              <div class="modal-image">
                <img
                  src="https://www.dogalyasam.net/image/catalog/icons/new/OlivePhoto00000021.jpg"
                  alt="Elle Hasat Detay"
                />
              </div>
              <div class="modal-text">
                <p>
                  Erken hasat zeytinyağımız için, Ekim ayının ilk 10 günü içinde
                  zeytinler elle tek tek toplanır. Bu dönemde zeytinler henüz
                  tam olgunlaşmamıştır ve yeşilden mora dönüşmeye başlamıştır.
                </p>
                <p>
                  Elle toplama yöntemi, zeytinlerin ezilmeden ve zarar görmeden
                  toplanmasını sağlar. Bu sayede oksidasyon süreci başlamadan
                  zeytinler işleme alınabilir.
                </p>
                <p>
                  Toplanan zeytinler havalandırmalı kasalara yerleştirilir ve
                  aynı gün içinde sıkım tesisine ulaştırılır. Bu hızlı süreç,
                  zeytinyağının kalitesini ve tazeliğini korumak için çok
                  önemlidir.
                </p>
                <h4>Neden Elle Hasat?</h4>
                <ul>
                  <li>Zeytinlerin zarar görmesini önler</li>
                  <li>Sadece olgun zeytinlerin seçilmesini sağlar</li>
                  <li>Yüksek kaliteli yağ elde etmek için gereklidir</li>
                  <li>Geleneksel yöntemlere bağlılığımızı gösterir</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <div class="timeline-modal" id="modal-2">
          <div class="modal-content">
            <span class="modal-close">&times;</span>
            <div class="modal-header">
              <h3>Soğuk Pres Tekniği</h3>
            </div>
            <div class="modal-body">
              <div class="modal-image">
                <img
                  src="https://www.dogalyasam.net/image/catalog/icons/new/olivePhoto00010.png"
                  alt="Soğuk Pres Detay"
                />
              </div>
              <div class="modal-text">
                <p>
                  Soğuk pres tekniği, zeytinyağı üretiminde en kaliteli
                  yöntemdir. Zeytinler, hasattan sonra en geç 4 saat içinde
                  işleme alınır ve sıcaklık 27°C'nin altında tutulur.
                </p>
                <p>
                  Bu düşük sıcaklıkta sıkım, zeytinyağının içerdiği
                  polifenoller, antioksidanlar ve aromatik bileşenlerin
                  korunmasını sağlar. Yüksek sıcaklıkta sıkım yapıldığında bu
                  değerli bileşenler kaybolabilir.
                </p>
                <p>
                  Soğuk pres işlemi sırasında zeytinler önce yıkanır, sonra
                  ezilir ve hamur haline getirilir. Bu hamur, düşük sıcaklıkta
                  ve basınçta sıkılarak yağ elde edilir.
                </p>
                <h4>Soğuk Presin Avantajları</h4>
                <ul>
                  <li>Daha yüksek antioksidan içeriği</li>
                  <li>Daha zengin aroma ve tat</li>
                  <li>Daha düşük asit oranı</li>
                  <li>Daha uzun raf ömrü</li>
                  <li>Daha yüksek besin değeri</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <div class="timeline-modal" id="modal-3">
          <div class="modal-content">
            <span class="modal-close">&times;</span>
            <div class="modal-header">
              <h3>Şişeleme ve Saklama</h3>
            </div>
            <div class="modal-body">
              <div class="modal-image">
                <img
                  src="https://www.dogalyasam.net/image/catalog/icons/new/coldBatle.png"
                  alt="Şişeleme Detay"
                />
              </div>
              <div class="modal-text">
                <p>
                  Zeytinyağımız, sıkımdan 48 saat sonra özel koyu cam şişelere
                  doldurulur. Koyu renkli cam şişeler, zeytinyağını güneş
                  ışığından korur ve oksidasyonu önler.
                </p>
                <p>
                  Şişeleme işlemi sırasında, yağın hava ile temasını en aza
                  indirmek için özel dolum teknikleri kullanılır. Her şişe,
                  içindeki değerli içeriği en iyi şekilde muhafaza etmek için
                  özenle kapatılır ve etiketlenir.
                </p>
                <p>
                  Zeytinyağımızın en iyi şekilde saklanması için, 14-18°C
                  arasında, doğrudan güneş ışığından uzak bir yerde muhafaza
                  edilmesi önerilir.
                </p>
                <h4>Doğru Saklama Koşulları</h4>
                <ul>
                  <li>Serin ve karanlık bir yerde saklayın</li>
                  <li>Sıcaklık 14-18°C arasında olmalı</li>
                  <li>Kullandıktan sonra kapağını sıkıca kapatın</li>
                  <li>Plastik kaplara aktarmayın</li>
                  <li>Ocak veya fırın yanında saklamayın</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Usage Section -->
    <section class="usage-section section">
      <div class="container">
        <div class="section-title">
          <h2 data-aos="fade-up">Kullanım Önerileri</h2>
          <div class="icon-divider" data-aos="fade-up" data-aos-delay="100">
            <i class="fas fa-utensils"></i>
          </div>
          <p class="subtitle" data-aos="fade-up" data-aos-delay="200">
            Erken hasat zeytinyağının mutfakta kullanım rehberi
          </p>
        </div>
        <div class="usage-grid">
          <div class="usage-card" data-aos="fade-up" data-aos-delay="300">
            <div class="usage-card-header">
              <div class="usage-icon">
                <img
                  src="https://www.dogalyasam.net/image/catalog/icons/new/salata-icon.png"
                  alt="Salata & Meze"
                />
              </div>
            </div>
            <div class="usage-card-body">
              <h3>Salata & Meze</h3>
              <p>
                Taze yeşilliklerin üzerine gezdirerek salatalarınıza
                karakteristik bir aroma katın.
              </p>
              <div class="usage-examples">
                Roka salatası, Akdeniz salatası, humus, cacık
              </div>
            </div>
            <div class="usage-card-footer">
              Önerilen miktar: 1-2 yemek kaşığı
            </div>
          </div>
          <div class="usage-card" data-aos="fade-up" data-aos-delay="400">
            <div class="usage-card-header">
              <div class="usage-icon">
                <img
                  src="https://www.dogalyasam.net/image/catalog/icons/new/hart-icon-2.png"
                  alt="Soğuk Yemekler"
                />
              </div>
            </div>
            <div class="usage-card-body">
              <h3>Soğuk Yemekler</h3>
              <p>
                Pişirme gerektirmeyen yemeklerde zeytinyağının tüm aromalarını
                hissedin.
              </p>
              <div class="usage-examples">
                Bruschetta, carpaccio, sushi, soğuk mezeler
              </div>
            </div>
            <div class="usage-card-footer">Önerilen miktar: 1 yemek kaşığı</div>
          </div>
          <div class="usage-card" data-aos="fade-up" data-aos-delay="500">
            <div class="usage-card-header">
              <div class="usage-icon">
                <img
                  src="https://www.dogalyasam.net/image/catalog/icons/new/bread-icon.png"
                  alt="Hamur İşi & Fırın"
                />
              </div>
            </div>
            <div class="usage-card-body">
              <h3>Hamur İşi & Fırın</h3>
              <p>
                Hamur işlerinize zengin bir doku ve aroma katarak lezzetini
                artırın.
              </p>
              <div class="usage-examples">
                Ev yapımı ekmek, pizza hamuru, focaccia
              </div>
            </div>
            <div class="usage-card-footer">
              Önerilen miktar: Tarife göre 2-3 yemek kaşığı
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Storage Section -->
    <section class="storage-section section">
      <div class="container">
        <div class="section-title">
          <h2 data-aos="fade-up">Depolama ve Saklama</h2>
          <div class="icon-divider" data-aos="fade-up" data-aos-delay="100">
            <i class="fas fa-temperature-low"></i>
          </div>
          <p class="subtitle" data-aos="fade-up" data-aos-delay="200">
            Zeytinyağınızın tazeliğini ve lezzetini korumak için
          </p>
        </div>

        <div class="storage-container">
          <div class="storage-image" data-aos="fade-right" data-aos-delay="300">
            <img
              src="https://www.dogalyasam.net/image/catalog/icons/new/coldBatle.png"
              alt="Koyu cam şişede zeytinyağı"
            />
          </div>

          <div
            class="storage-content"
            data-aos="fade-left"
            data-aos-delay="400"
          >
            <div class="storage-card">
              <div class="storage-icon">
                <img
                  src="https://www.dogalyasam.net/image/catalog/icons/new/oil-battle-icon.png"
                  alt="Saklama"
                />
              </div>
              <h3>Doğru Saklama Koşulları</h3>
              <p>
                Erken hasat zeytinyağının kalitesini ve besin değerlerini
                korumak için doğru saklama koşulları çok önemlidir.
                Zeytinyağınızı en iyi şekilde muhafaza etmek için aşağıdaki
                önerileri dikkate alın:
              </p>

              <div class="storage-tips">
                <div class="storage-tip">
                  <i class="fas fa-temperature-low"></i>
                  <p>
                    <strong>Sıcaklık:</strong> +14–18 °C arasında, serin bir
                    ortamda saklayın. Aşırı sıcak veya soğuk, yağın yapısını
                    bozabilir.
                  </p>
                </div>

                <div class="storage-tip">
                  <i class="fas fa-lightbulb"></i>
                  <p>
                    <strong>Işık:</strong> Doğrudan güneş ışığından ve güçlü
                    yapay ışıklardan uzak tutun. Işık, zeytinyağının
                    oksidasyonunu hızlandırır.
                  </p>
                </div>

                <div class="storage-tip">
                  <i class="fas fa-wine-bottle"></i>
                  <p>
                    <strong>Kap:</strong> Koyu renkli cam şişede muhafaza edin.
                    Plastik kaplar zeytinyağının kalitesini düşürebilir.
                  </p>
                </div>

                <div class="storage-tip">
                  <i class="fas fa-wind"></i>
                  <p>
                    <strong>Hava:</strong> Kullandıktan sonra şişenin kapağını
                    sıkıca kapatın. Hava ile temas, oksidasyon sürecini
                    başlatır.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Gallery Section -->
    <section class="gallery-section section">
      <div class="container">
        <div class="section-title">
          <h2 data-aos="fade-up">Görsel Galeri</h2>
          <div class="icon-divider" data-aos="fade-up" data-aos-delay="100">
            <i class="fas fa-images"></i>
          </div>
          <p class="subtitle" data-aos="fade-up" data-aos-delay="200">
            Erken hasat zeytinyağı üretim sürecimizden kareler
          </p>
        </div>
        <div class="gallery-grid">
          <div class="gallery-item" data-aos="fade-up" data-aos-delay="300">
            <img
              src="https://www.dogalyasam.net/image/catalog/icons/new/OlivePhoto00000021.jpg"
              alt="Hasat anı fotoğrafı"
            />
            <div class="gallery-caption">
              <h4>Hasat Anı</h4>
              <p>Ekim ayı başında elle toplanan erkence zeytinler</p>
            </div>
          </div>
          <div class="gallery-item" data-aos="fade-up" data-aos-delay="400">
            <img
              src="https://www.dogalyasam.net/image/catalog/icons/new/OlivePhoto00000014.jpg"
              alt="Soğuk pres makinası görüntüsü"
            />
            <div class="gallery-caption">
              <h4>Soğuk Pres İşlemi</h4>
              <p>Zeytinlerin 27°C'nin altında sıkım süreci</p>
            </div>
          </div>
          <div class="gallery-item" data-aos="fade-up" data-aos-delay="500">
            <img
              src="https://www.dogalyasam.net/image/catalog/icons/new/OlivePhoto00000018.jpg"
              alt="Koyu cam şişe yerleşimi"
            />
            <div class="gallery-caption">
              <h4>Koyu Cam Şişeleme</h4>
              <p>Işık ve ısıdan koruma için özel şişeleme</p>
            </div>
          </div>
          <div class="gallery-item" data-aos="fade-up" data-aos-delay="600">
            <img
              src="https://www.dogalyasam.net/image/catalog/icons/new/OlivePhoto00000013.jpg"
              alt="Ürünün sofrada kullanımı"
            />
            <div class="gallery-caption">
              <h4>Sofrada Kullanım</h4>
              <p>Taze salatalarda eşsiz lezzet</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Lightbox -->
      <div class="lightbox">
        <div class="lightbox-content">
          <img src="" alt="Lightbox Image" />
        </div>
        <div class="lightbox-close">
          <i class="fas fa-times"></i>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section">
      <div class="container">
        <div class="cta-badge">Sınırlı Üretim</div>
        <div class="cta-content">
          <h2 class="cta-title" data-aos="fade-up">
            Erken Hasatın Gerçek Tazeliğini Keşfet!
          </h2>
          <p class="cta-text" data-aos="fade-up" data-aos-delay="100">
            Amanos Dağları'nın eteklerinden gelen eşsiz lezzet ve sağlık kaynağı
            olan erken hasat zeytinyağımız, sınırlı miktarda üretiliyor. Doğanın
            bu nadide armağanını sofranıza taşımak için acele edin.
          </p>

          <div class="category-cards" data-aos="fade-up" data-aos-delay="400">
            <!-- Yemeklik Zeytinyağı Kartı -->
            <a
              href="https://www.dogalyasam.net/zeytinyagi/naturel-birinci-zeytinyagi-yemeklik-zeytinyagi/"
              class="category-card"
            >
              <div class="category-card-image">
                <img
                  src="https://www.dogalyasam.net/image/catalog/icons/new/olivePhoto0004.jpg"
                  alt="Yemeklik Zeytinyağı"
                />
              </div>
              <div class="category-card-content">
                <h3 class="category-card-title">Birinci Zeytinyağı</h3>
                <p class="category-card-description">
                  Günlük kullanım için ideal, yemeklerinize lezzet katacak doğal
                  zeytinyağı.
                </p>
                <div class="category-card-button">İncele</div>
              </div>
            </a>

            <!-- Erken Hasat Zeytinyağı Kartı -->
            <a
              href="https://www.dogalyasam.net/zeytinyagi/soguk-sikim-naturel-sizma-zeytinyagi/"
              class="category-card"
            >
              <div class="category-card-image">
                <img
                  src="https://www.dogalyasam.net/image/catalog/icons/new/OlivePhoto00000021.jpg"
                  alt="Soğuk Sıkım Zeytinyağı"
                />
              </div>
              <div class="category-card-content">
                <h3 class="category-card-title">Soğuk Sıkım Zeytinyağı</h3>
                <p class="category-card-description">
                  Düşük sıcaklıkta sıkılmış, yüksek antioksidan içerikli premium
                  zeytinyağı.
                </p>
                <div class="category-card-button">İncele</div>
              </div>
            </a>
          </div>
        </div>
      </div>
    </section>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.js"></script>
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        // Initialize AOS (Animate On Scroll)
        AOS.init({
          duration: 800,
          easing: "ease-in-out",
          once: true,
          offset: 100,
          delay: 100,
        });

        // Discount alert functionality
        const discountAlert = document.querySelector(".discount-alert");
        let hasClosedAlert = false;
        let hasShownNotification = false;

        if (discountAlert) {
          // Close button functionality
          const closeButton = discountAlert.querySelector(
            ".discount-alert-close"
          );
          if (closeButton) {
            closeButton.addEventListener("click", function () {
              discountAlert.classList.remove("show");
              hasClosedAlert = true;
            });
          }

          // Show notification when user scrolls to benefits section
          const benefitsSection = document.querySelector(".benefits");

          if (!hasClosedAlert && benefitsSection) {
            const observer = new IntersectionObserver(
              (entries) => {
                entries.forEach((entry) => {
                  // If benefits section is visible and notification hasn't been shown yet
                  if (entry.isIntersecting && !hasShownNotification) {
                    hasShownNotification = true;

                    // Show notification with a slight delay
                    setTimeout(() => {
                      discountAlert.classList.add("show");

                      // Auto hide after 7 seconds
                      setTimeout(() => {
                        discountAlert.classList.remove("show");
                      }, 7000);
                    }, 1000);

                    // Stop observing once shown
                    observer.unobserve(benefitsSection);
                  }
                });
              },
              { threshold: 0.3 }
            ); // Trigger when 30% of the element is visible

            // Start observing
            observer.observe(benefitsSection);
          }
        }

        // Interactive Timeline
        const timelineNavItems =
          document.querySelectorAll(".timeline-nav-item");
        const timelineSteps = document.querySelectorAll(".timeline-step");
        const timelineProgress = document.querySelector(".timeline-progress");
        const timelineModals = document.querySelectorAll(".timeline-modal");
        const modalCloseButtons = document.querySelectorAll(".modal-close");
        const stepMoreButtons = document.querySelectorAll(".step-more-btn");

        // Initialize timeline
        let currentStep = 0;

        // Variable to track if animation is in progress
        let animationInProgress = false;

        function updateTimeline(stepIndex, isAutoAdvance = false) {
          // If animation is already in progress, don't start another one
          if (animationInProgress && !isAutoAdvance) return;

          // Calculate segment width based on number of steps
          const segmentWidth = 100 / (timelineNavItems.length - 1);

          // Calculate start and end positions for the progress bar
          const startPosition = currentStep * segmentWidth;
          const endPosition = stepIndex * segmentWidth;

          // If we're moving to a new step (not initialization)
          if (currentStep !== stepIndex) {
            animationInProgress = true;

            // Instagram-story style: Reset progress bar to start of current segment
            timelineProgress.style.transition = "none";

            if (isAutoAdvance) {
              // For auto-advance, start from 0 width in the current segment
              timelineProgress.style.width = `${startPosition}%`;
            } else {
              // For manual navigation, jump to the start of the target segment
              timelineProgress.style.width = `${startPosition}%`;
            }

            // Force a reflow to make sure the reset happens before the animation starts
            timelineProgress.offsetHeight;

            // Now restore the transition and animate to the end position
            timelineProgress.style.transition = "width 4s linear";
            timelineProgress.style.width = `${endPosition}%`;

            // Update navigation dots immediately
            timelineNavItems.forEach((item, index) => {
              if (index === stepIndex) {
                item.classList.add("active");
              } else {
                item.classList.remove("active");
              }
            });

            // Update content immediately
            timelineSteps.forEach((step, index) => {
              if (index === stepIndex) {
                step.classList.add("active");
              } else {
                step.classList.remove("active");
              }
            });

            // Update segment styling for Instagram-story effect
            const segments = document.querySelectorAll(".timeline-segment");
            if (segments.length > 0) {
              segments.forEach((segment, index) => {
                // Mark previous segments as completed
                if (index < currentStep) {
                  segment.style.backgroundColor = "var(--gold)";
                  segment.style.opacity = "0.7";
                }
                // Mark current segment as active
                else if (index === currentStep) {
                  segment.style.backgroundColor = "var(--gold)";
                  segment.style.opacity = "0.3";
                }
                // Reset future segments
                else {
                  segment.style.backgroundColor = "rgba(58, 83, 17, 0.2)";
                  segment.style.opacity = "1";
                }
              });
            }

            // Update current step
            currentStep = stepIndex;

            // Reset animation flag after transition completes
            setTimeout(() => {
              animationInProgress = false;
            }, 4000); // Match this to the CSS transition time
          } else {
            // For initialization, update everything immediately without animation
            timelineProgress.style.transition = "none";
            timelineProgress.style.width = `${startPosition}%`;

            // Update navigation dots
            timelineNavItems.forEach((item, index) => {
              if (index === stepIndex) {
                item.classList.add("active");
              } else {
                item.classList.remove("active");
              }
            });

            // Update content
            timelineSteps.forEach((step, index) => {
              if (index === stepIndex) {
                step.classList.add("active");
              } else {
                step.classList.remove("active");
              }
            });

            // Initialize segment styling for Instagram-story effect
            const segments = document.querySelectorAll(".timeline-segment");
            if (segments.length > 0) {
              segments.forEach((segment, index) => {
                if (index < stepIndex) {
                  segment.style.backgroundColor = "var(--gold)";
                  segment.style.opacity = "0.7";
                } else {
                  segment.style.backgroundColor = "rgba(58, 83, 17, 0.2)";
                  segment.style.opacity = "1";
                }
              });
            }

            // Set current step
            currentStep = stepIndex;

            // Restore transition for future animations
            setTimeout(() => {
              timelineProgress.style.transition = "width 4s linear";

              // Start the animation for the first segment
              if (stepIndex < timelineNavItems.length - 1) {
                timelineProgress.style.width = `${endPosition}%`;
              }
            }, 50);
          }
        }

        // Add click event to navigation items
        timelineNavItems.forEach((item, index) => {
          item.addEventListener("click", () => {
            // Clear any existing interval
            clearInterval(timelineInterval);

            // Update timeline with manual navigation
            updateTimeline(index, false);

            // Restart the interval after manual navigation
            timelineInterval = setInterval(() => {
              const nextStep = (currentStep + 1) % timelineNavItems.length;
              updateTimeline(nextStep, true);
            }, 4000);
          });
        });

        // Modal functionality
        stepMoreButtons.forEach((button) => {
          button.addEventListener("click", () => {
            const modalId = button.getAttribute("data-modal");
            const modal = document.getElementById(modalId);
            if (modal) {
              modal.style.display = "block";
              document.body.style.overflow = "hidden";
            }
          });
        });

        modalCloseButtons.forEach((button) => {
          button.addEventListener("click", () => {
            const modal = button.closest(".timeline-modal");
            if (modal) {
              modal.style.display = "none";
              document.body.style.overflow = "auto";
            }
          });
        });

        // Close modal when clicking outside
        timelineModals.forEach((modal) => {
          modal.addEventListener("click", (e) => {
            if (e.target === modal) {
              modal.style.display = "none";
              document.body.style.overflow = "auto";
            }
          });
        });

        // Close modal with Escape key
        document.addEventListener("keydown", (e) => {
          if (e.key === "Escape") {
            timelineModals.forEach((modal) => {
              modal.style.display = "none";
            });
            document.body.style.overflow = "auto";
          }
        });

        // Auto advance timeline every 4 seconds (Instagram-story style)
        let timelineInterval = setInterval(() => {
          const nextStep = (currentStep + 1) % timelineNavItems.length;
          updateTimeline(nextStep, true); // Pass true to indicate this is an auto-advance
        }, 4000);

        // Pause auto advance on hover
        const timelineContainer = document.querySelector(
          ".interactive-timeline"
        );
        if (timelineContainer) {
          timelineContainer.addEventListener("mouseenter", () => {
            clearInterval(timelineInterval);

            // Pause the progress animation by removing the transition
            if (timelineProgress) {
              const currentWidth = timelineProgress.offsetWidth;
              const containerWidth = timelineProgress.parentElement.offsetWidth;
              const percentWidth = (currentWidth / containerWidth) * 100;

              timelineProgress.style.transition = "none";
              timelineProgress.style.width = `${percentWidth}%`;
            }
          });

          timelineContainer.addEventListener("mouseleave", () => {
            // Resume the progress animation
            if (timelineProgress) {
              const segmentWidth = 100 / (timelineNavItems.length - 1);
              const endPosition =
                ((currentStep + 1) % timelineNavItems.length) * segmentWidth;

              // Force reflow
              timelineProgress.offsetHeight;

              // Resume animation to next step
              timelineProgress.style.transition = "width 4s linear";
              timelineProgress.style.width = `${endPosition}%`;
            }

            // Restart the interval
            timelineInterval = setInterval(() => {
              const nextStep = (currentStep + 1) % timelineNavItems.length;
              updateTimeline(nextStep, true); // Pass true to indicate this is an auto-advance
            }, 4000);
          });
        }

        // Timeline initialization is handled above

        // Gallery Lightbox
        const galleryItems = document.querySelectorAll(".gallery-item");
        const lightbox = document.querySelector(".lightbox");

        if (lightbox && galleryItems.length > 0) {
          const lightboxImg = lightbox.querySelector("img");
          const lightboxClose = document.querySelector(".lightbox-close");

          if (lightboxImg) {
            galleryItems.forEach((item) => {
              item.addEventListener("click", () => {
                const imgSrc = item.querySelector("img").getAttribute("src");
                const imgAlt = item.querySelector("img").getAttribute("alt");

                lightboxImg.setAttribute("src", imgSrc);
                lightboxImg.setAttribute("alt", imgAlt);

                lightbox.classList.add("active");
                document.body.style.overflow = "hidden";
              });
            });
          }

          if (lightboxClose) {
            lightboxClose.addEventListener("click", () => {
              lightbox.classList.remove("active");
              document.body.style.overflow = "auto";
            });
          }

          // Close lightbox when clicking outside the image
          lightbox.addEventListener("click", (e) => {
            if (e.target === lightbox) {
              lightbox.classList.remove("active");
              document.body.style.overflow = "auto";
            }
          });

          // Close lightbox with Escape key
          document.addEventListener("keydown", (e) => {
            if (e.key === "Escape" && lightbox.classList.contains("active")) {
              lightbox.classList.remove("active");
              document.body.style.overflow = "auto";
            }
          });
        }

        // Add smooth scroll for anchor links
        document.querySelectorAll('a[href^="#"]').forEach((anchor) => {
          anchor.addEventListener("click", function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute("href"));
            if (target) {
              target.scrollIntoView({
                behavior: "smooth",
                block: "start",
              });
            }
          });
        });

        // Add scroll indicator functionality
        const scrollIndicator = document.querySelector(".scroll-indicator");
        if (scrollIndicator) {
          scrollIndicator.addEventListener("click", () => {
            const productDescription = document.querySelector(
              "#product-description"
            );
            if (productDescription) {
              productDescription.scrollIntoView({
                behavior: "smooth",
                block: "start",
              });
            }
          });
        }

        // Create timeline segments for Instagram-story-like effect
        function createTimelineSegments() {
          const timelineBranch = document.querySelector(".timeline-branch");
          if (timelineBranch && timelineNavItems.length > 1) {
            // Clear any existing segments
            timelineBranch.innerHTML = "";

            // Create segments based on number of timeline steps
            for (let i = 0; i < timelineNavItems.length - 1; i++) {
              const segment = document.createElement("div");
              segment.className = "timeline-segment";
              segment.setAttribute("data-segment", i);
              timelineBranch.appendChild(segment);
            }
          }
        }

        // Initialize timeline on page load
        if (timelineNavItems.length > 0 && timelineSteps.length > 0) {
          // Create the segments first
          createTimelineSegments();

          // Then initialize the timeline
          updateTimeline(0);
        }
      });
    </script>
  </body>
</html>
