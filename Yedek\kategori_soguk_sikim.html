<!DOCTYPE html>
<html lang="tr">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>So<PERSON>uk Sıkım Zeytinyağları - Doğal Yaşam</title>
    <meta
      name="description"
      content="Amanos <PERSON>ı'nın eteklerinden gelen %100 doğal soğuk sıkım zeytinyağları. Erken hasat, düşük asit, katkısız ve doğal."
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.css"
    />
    <link
      href="https://fonts.googleapis.com/css2?family=SF+Pro+Display:wght@300;400;500;600;700&family=SF+Pro+Text:wght@400;500;600&display=swap"
      rel="stylesheet"
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <style>
      :root {
        --main-green: #556b2f; /* Zeytin yeşili */
        --secondary-green: #4a5d23; /* Daha koyu yeşil */
        --light-green: #d4edd4; /* Açık yeşil */
        --accent-brown: #8b4513; /* Toprak tonu */
        --text-color: #2d3748; /* Koyu gri */
        --bg-cream: #f9f7f2; /* Krem rengi arka plan */
        --gold: #d4af37; /* Altın rengi */
        --light-gray: #fbf7e9; /* Açık gri */
        --medium-gray: #e5e5e5; /* Orta gri */
        --dark-gray: #666666; /* Koyu gri */
        --discount-red: #e74c3c; /* İndirim kırmızısı */
      }

      * {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
      }

      body {
        font-family: "Inter", "Segoe UI", sans-serif;
        line-height: 1.6;
        background: var(--bg-cream);
        color: var(--text-color);
        overflow-x: hidden;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        width: 100%;
        max-width: 100%;
        margin: 0;
        padding: 0;
      }

      h1,
      h2,
      h3,
      h4,
      h5,
      h6 {
        font-family: "Inter", "Segoe UI", sans-serif;
        color: var(--main-green);
        font-weight: 600;
        letter-spacing: -0.01em;
      }

      /* Hero Section */
      .category-hero {
        position: relative;
        height: 85vh;
        background: linear-gradient(
            rgba(255, 255, 255, 0.2),
            rgba(200, 255, 200, 0.2)
          ),
          url("https://www.dogalyasam.net/image/catalog/icons/new/coldPure-1.jpg")
            center/cover no-repeat;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        color: white;
      }

      .hero-content {
        max-width: 800px;
        padding: 3rem;
        background-color: rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(8px);
        -webkit-backdrop-filter: blur(8px);
        border-radius: 20px;
        border: 2px solid rgba(255, 255, 255, 0.7);
      }

      .hero-title {
        font-size: 4rem;
        line-height: 1.1;
        margin-bottom: 1.5rem;
        animation: fadeInUp 1s ease;
        color: #fff;
        font-weight: 700;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      }

      .hero-subtitle {
        font-size: 1.6rem;
        font-weight: 400;
        margin-bottom: 2.5rem;
        animation: fadeInUp 1.2s ease;
        color: rgba(255, 255, 255, 0.95);
        max-width: 650px;
        margin-left: auto;
        margin-right: auto;
      }

      /* Feature Grid - Apple Style */
      .features-section {
        padding: 120px 2rem;
        background-color: var(--light-gray);
      }

      .feature-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 2rem;
        max-width: 1200px;
        margin: 0 auto;
      }

      .feature-card {
        background: #ffffff;
        padding: 2.5rem;
        border-radius: 20px;
        transition: all 0.4s ease;
        text-align: center;
        position: relative;
        overflow: hidden;
      }

      .feature-card::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--gold);
        opacity: 0;
        transition: opacity 0.4s ease;
      }

      .feature-card:hover::before {
        opacity: 1;
      }

      .feature-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
      }

      .feature-icon {
        margin-bottom: 1.5rem;
        height: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .feature-icon img {
        width: 64px;
        height: 64px;
        object-fit: contain;
      }

      .feature-card h3 {
        margin-bottom: 1rem;
        font-size: 1.4rem;
        font-weight: 600;
        letter-spacing: -0.01em;
      }

      .feature-card p {
        color: var(--dark-gray);
        font-size: 1rem;
        line-height: 1.6;
      }

      /* Stepper Process */
      .process-stepper {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        max-width: 1000px;
        margin: 4rem auto 0;
        position: relative;
        padding: 0 20px;
      }

      .process-stepper::before {
        content: "";
        position: absolute;
        top: 50px;
        left: 80px;
        right: 80px;
        height: 4px;
        background: linear-gradient(
          to right,
          rgba(85, 107, 47, 0.3) 0%,
          var(--main-green) 50%,
          rgba(85, 107, 47, 0.3) 100%
        );
        z-index: 1;
        border-radius: 2px;
        transform: scaleX(0);
        transform-origin: left;
        transition: transform 3s ease-in-out;
      }

      .process-stepper.animate::before {
        transform: scaleX(1);
      }

      .step {
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        z-index: 2;
        width: 33.33%;
        opacity: 0;
        transform: translateY(20px);
        transition: opacity 0.5s ease, transform 0.5s ease;
      }

      .step.animate {
        opacity: 1;
        transform: translateY(0);
      }

      .step:nth-child(1) {
        transition-delay: 0.3s;
      }

      .step:nth-child(2) {
        transition-delay: 1.2s;
      }

      .step:nth-child(3) {
        transition-delay: 2.1s;
      }

      .step-icon {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 1.5rem;
        transition: all 0.3s ease;
      }

      .step-icon img {
        width: 120px;
        height: 120px;
        object-fit: contain;
      }

      .step-content {
        text-align: center;
        max-width: 250px;
      }

      .step-title {
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: var(--main-green);
      }

      .step-description {
        font-size: 1rem;
        color: var(--dark-gray);
        line-height: 1.5;
      }

      .step:hover .step-icon {
        transform: scale(1.05);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        border-color: var(--gold);
      }

      @media (max-width: 768px) {
        .process-stepper {
          flex-direction: column;
          gap: 3rem;
        }

        .process-stepper::before {
          top: 0;
          bottom: 0;
          left: 65px;
          right: auto;
          width: 4px;
          height: auto;
          background: linear-gradient(
            to bottom,
            rgba(85, 107, 47, 0.3) 0%,
            var(--main-green) 50%,
            rgba(85, 107, 47, 0.3) 100%
          );
          transform: scaleY(0);
          transform-origin: top;
          transition: transform 3s ease-in-out;
        }

        .process-stepper.animate::before {
          transform: scaleY(1);
        }

        .step {
          width: 100%;
          flex-direction: row;
          text-align: left;
          gap: 2rem;
          transform: translateX(20px);
        }

        .step.animate {
          transform: translateX(0);
        }

        .step-icon {
          margin-bottom: 0;
        }

        .step-content {
          text-align: left;
        }
      }

      /* Analysis Section */
      .analysis-section {
        padding: 4rem 0;
        position: relative;
        overflow: hidden;
      }

      .analysis-section::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-size: 300px;
        opacity: 0.05;
        z-index: 0;
      }

      .title-container {
        text-align: center;
        margin-bottom: 0.5rem;
      }

      /* Forbes Style Leaderboard */
      .analysis-boards {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
        max-width: 1200px;
        margin: 3rem auto 0;
        padding: 0 1rem;
      }

      .analysis-board {
        max-width: 100%;
        border-radius: 12px;
        box-shadow: 0 0 20px -10px rgba(0, 0, 0, 0.4);
        overflow: hidden;
      }
      .analysis-board svg {
        fill: #fff;
        opacity: 0.65;
        width: 50px;
        position: absolute;
        top: 50%;
        left: var(--start);
        transform: translate(-50%, -50%);
      }
      .board-header {
        --start: 15%;
        height: 130px;
        position: relative;
        color: #fff;
        border-radius: 12px 12px 0 0;
        overflow: hidden;
      }

      .board-header--nutrition {
        background-image: repeating-radial-gradient(
            circle at var(--start),
            #00000021 0%,
            #00000014 10%,
            rgba(85, 107, 47, 0.33) 10%,
            rgba(85, 107, 47, 0.33) 17%
          ),
          linear-gradient(to right, var(--main-green), var(--secondary-green));
      }

      .board-header--analysis {
        background-image: repeating-radial-gradient(
            circle at var(--start),
            #00000021 0%,
            #00000014 10%,
            rgba(212, 175, 55, 0.33) 10%,
            rgba(212, 175, 55, 0.33) 17%
          ),
          linear-gradient(to right, var(--gold), #c9a22e);
      }

      .board-icon {
        fill: #fff;
        opacity: 0.65;
        width: 50px;
        position: absolute;
        top: 50%;
        left: var(--start);
        transform: translate(-50%, -50%);
      }

      .board-title {
        position: absolute;
        z-index: 2;
        top: 50%;
        right: calc(var(--start) * 0.75);
        transform: translateY(-50%);
        text-transform: uppercase;
        margin: 0;
        color: #fff;
      }

      .board-title span {
        display: block;
      }

      .board-title--top {
        font-size: 24px;
        font-weight: 700;
        letter-spacing: 2px;
      }

      .board-title--bottom {
        font-size: 13px;
        font-weight: 500;
        letter-spacing: 1px;
        opacity: 0.65;
        transform: translateY(-2px);
      }

      .board-profiles {
        background-color: rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(8px);
        -webkit-backdrop-filter: blur(8px);
        border-radius: 0 0 12px 12px;
        padding: 15px 15px 20px;
        display: grid;
        row-gap: 8px;
      }

      .board-profile {
        display: grid;
        grid-template-columns: 60px 1fr auto;
        align-items: center;
        padding: 10px 30px 10px 10px;
        overflow: hidden;
        border-radius: 10px;
        box-shadow: 0 5px 7px -1px rgba(51, 51, 51, 0.23);
        cursor: pointer;
        transition: transform 0.25s cubic-bezier(0.7, 0.98, 0.86, 0.98),
          box-shadow 0.25s cubic-bezier(0.7, 0.98, 0.86, 0.98);
        background-color: rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(8px);
        -webkit-backdrop-filter: blur(8px);
        position: relative;
      }

      .board-profile:hover {
        transform: scale(1.05);
        box-shadow: 0 9px 47px 11px rgba(51, 51, 51, 0.18);
      }

      .board-profile-icon {
        max-width: 100%;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 1.2rem;
        color: white;
      }

      .nutrition-icon {
        background: var(--main-green);
        box-shadow: 0 0 0 5px rgba(85, 107, 47, 0.1),
          0 0 0 10px rgba(85, 107, 47, 0.05);
      }

      .analysis-icon {
        background: var(--gold);
        box-shadow: 0 0 0 5px rgba(212, 175, 55, 0.1),
          0 0 0 10px rgba(212, 175, 55, 0.05);
      }

      .board-profile-name {
        color: var(--text-color);
        font-weight: 500;
        font-size: 1.1rem;
        margin-left: 12px;
      }

      .board-profile-value {
        color: var(--main-green);
        font-weight: 700;
        font-size: 1.3rem;
        text-align: right;
      }

      .board-profile-value span {
        opacity: 0.8;
        font-weight: 400;
        font-size: 0.9rem;
        margin-left: 3px;
      }

      .analysis-value {
        color: var(--gold);
      }

      .board-tooltip {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: #c9a22e;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 1px;
        border-radius: 10px;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        font-size: 0.9rem;
        text-align: center;
        z-index: 10;
      }

      .board-profile:hover .board-tooltip {
        opacity: 1;
        visibility: visible;
      }

      @media (max-width: 768px) {
        .analysis-boards {
          grid-template-columns: 1fr;
        }
      }

      /* Usage Section - Apple Style */
      .usage-section {
        padding: 120px 2rem;
        background-color: var(--light-gray);
      }

      .usage-content {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 6rem;
        align-items: center;
      }

      .usage-text h3 {
        font-size: 2rem;
        margin-bottom: 1.5rem;
        margin-top: 3rem;
        color: var(--text-color);
        font-weight: 600;
        letter-spacing: -0.02em;
        line-height: 1.2;
      }

      .usage-text h3:first-child {
        margin-top: 0;
      }

      .usage-text p {
        margin-bottom: 1.5rem;
        font-size: 1.1rem;
        line-height: 1.6;
        color: var(--dark-gray);
      }

      .usage-list {
        list-style: none;
        padding: 0;
        margin: 1.5rem 0;
      }

      .usage-list li {
        margin-bottom: 1.2rem;
        padding-left: 3rem;
        position: relative;
        text-align: left;
        font-size: 1.1rem;
        line-height: 1.6;
        color: var(--dark-gray);
      }

      .usage-icon {
        position: absolute;
        left: 0;
        font-size: 1.5rem;
      }

      .usage-image {
        position: relative;
      }

      .usage-image img {
        width: 100%;
        border-radius: 20px;
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.1);
      }

      .usage-image::after {
        content: "";
        position: absolute;
        top: 20px;
        left: 20px;
        right: 20px;
        bottom: 20px;
        border-radius: 20px;
        box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15);
        z-index: -1;
      }

      /* CTA Section */
      .cta-section {
        background: var(--main-green);
        color: white;
        text-align: center;
        padding: 120px 2rem;
        position: relative;
        overflow: hidden;
        width: 100%;
        max-width: 100%;
        box-sizing: border-box;
      }

      .cta-background {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: url("https://www.dogalyasam.net/image/catalog/icons/new/olivePhoto00014.png")
          center/cover no-repeat;
        opacity: 0.15;
        filter: blur(8px);
        max-width: 100%;
      }

      .cta-content {
        position: relative;
        z-index: 2;
        max-width: 1000px;
        margin: 0 auto;
      }

      .cta-section h2 {
        color: #fff;
        margin-bottom: 1.5rem;
        font-size: 3.2rem;
        font-weight: 700;
        letter-spacing: -0.03em;
        line-height: 1.1;
      }

      .cta-section p {
        max-width: 800px;
        margin: 0 auto 3rem;
        font-size: 1.3rem;
        line-height: 1.6;
        color: rgba(255, 255, 255, 0.9);
      }

      .cta-buttons {
        display: flex;
        justify-content: center;
        gap: 1.5rem;
        flex-wrap: wrap;
      }

      .cta-button {
        display: inline-block;
        padding: 1rem 2.5rem;
        background: var(--gold);
        color: white;
        text-decoration: none;
        border-radius: 50px;
        transition: all 0.3s ease;
        font-weight: 500;
        font-size: 1.1rem;
        letter-spacing: -0.01em;
      }

      .cta-button:hover {
        background: #c9a22e;
        transform: translateY(-3px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
      }

      .cta-button.secondary {
        background: transparent;
        border: 1px solid rgba(255, 255, 255, 0.5);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
      }

      .cta-button.secondary:hover {
        background: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.8);
      }

      /* Parallax Section - Apple Style */
      .parallax-section {
        position: relative;
        min-height: 80vh;
        background-attachment: fixed;
        background-position: center;
        background-repeat: no-repeat;
        background-size: cover;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        text-align: center;
        padding: 0;
        overflow: hidden;
      }

      .parallax-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          to right,
          rgba(0, 0, 0, 0.7),
          rgba(0, 0, 0, 0.3)
        );
        z-index: 1;
      }

      .parallax-section .content-wrapper {
        position: relative;
        z-index: 2;
        padding: 0;
        max-width: 800px;
        margin: 0 2rem;
      }

      .parallax-section h2 {
        font-size: 3.2rem;
        line-height: 1.1;
        margin-bottom: 1.5rem;
        color: #fff;
        font-weight: 700;
        letter-spacing: -0.03em;
      }

      .parallax-section p {
        font-size: 1.4rem;
        color: rgba(255, 255, 255, 0.9);
        line-height: 1.6;
        font-weight: 400;
        margin-bottom: 1.5rem;
      }

      .parallax-section p:last-child {
        margin-bottom: 0;
      }

      /* Process Timeline - Apple Style */
      .process-section {
        padding: 120px 2rem;
        background-color: white;
      }

      .process-timeline {
        position: relative;
        max-width: 1000px;
        margin: 0 auto;
        padding: 0;
      }

      .process-timeline h2 {
        text-align: center;
        margin-bottom: 4rem;
        font-size: 2.8rem;
        font-weight: 700;
        letter-spacing: -0.03em;
        line-height: 1.1;
      }

      .timeline-container {
        position: relative;
        padding-left: 2rem;
      }

      .timeline-line {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        width: 1px;
        background: var(--medium-gray);
      }

      .timeline-item {
        position: relative;
        margin-bottom: 4rem;
        padding-left: 2.5rem;
      }

      .timeline-item:last-child {
        margin-bottom: 0;
      }

      .timeline-item::before {
        content: "";
        position: absolute;
        left: -8px;
        top: 8px;
        width: 16px;
        height: 16px;
        background: white;
        border: 2px solid var(--main-green);
        border-radius: 50%;
        z-index: 1;
      }

      .timeline-item h3 {
        font-size: 1.6rem;
        color: var(--text-color);
        margin-bottom: 1rem;
        font-weight: 600;
        letter-spacing: -0.01em;
      }

      .timeline-item p {
        color: var(--dark-gray);
        font-size: 1.1rem;
        line-height: 1.6;
      }

      /* Benefits Section - Apple Style */
      .benefits-section {
        padding: 120px 0;
        background: var(--light-gray);
      }

      .benefits-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 2rem;
        padding: 0;
        max-width: 1200px;
        margin: 0 auto;
      }

      .benefit-card {
        background: white;
        padding: 2.5rem;
        border-radius: 20px;
        text-align: center;
        transition: all 0.4s ease;
        position: relative;
        overflow: hidden;
        height: 100%;
        display: flex;
        flex-direction: column;
        border-top: 3px solid transparent;
      }

      .benefit-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
        border-top: 3px solid var(--gold);
      }

      .benefit-icon {
        margin-bottom: 1.5rem;
        height: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .benefit-icon img {
        width: 64px;
        height: 64px;
        object-fit: contain;
      }

      .benefit-card h3 {
        margin-bottom: 1rem;
        font-size: 1.4rem;
        color: var(--text-color);
        font-weight: 600;
        letter-spacing: -0.01em;
      }

      .benefit-card p {
        font-size: 1.05rem;
        line-height: 1.6;
        color: var(--dark-gray);
        flex-grow: 1;
      }

      .benefits-footer {
        text-align: center;
        margin-top: 3rem;
      }

      .benefits-note {
        font-size: 1.1rem;
        color: var(--dark-gray);
        max-width: 700px;
        margin: 0 auto;
        font-style: italic;
      }

      /* Quality Section - Apple Style */
      .quality-section {
        padding: 120px 2rem;
        background: #ffffff;
      }

      .quality-container {
        max-width: 1200px;
        margin: 0 auto;
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 6rem;
        align-items: center;
      }

      .quality-image {
        position: relative;
        overflow: hidden;
        border-radius: 20px;
      }

      .quality-image img {
        width: 100%;
        height: auto;
        display: block;
        transition: transform 0.5s ease;
        border-radius: 20px;
      }

      .quality-image::after {
        content: "";
        position: absolute;
        top: 20px;
        left: 20px;
        right: 20px;
        bottom: 20px;
        border-radius: 20px;
        box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15);
        z-index: -1;
      }

      .quality-content h2 {
        margin-bottom: 1.5rem;
        font-size: 2.8rem;
        font-weight: 700;
        letter-spacing: -0.03em;
        line-height: 1.1;
      }

      .quality-content p {
        font-size: 1.1rem;
        line-height: 1.6;
        color: var(--dark-gray);
        margin-bottom: 1.5rem;
      }

      .quality-list {
        list-style: none;
        padding: 0;
        margin: 2rem 0;
      }

      .quality-list li {
        margin-bottom: 1.2rem;
        padding-left: 2.2rem;
        position: relative;
        text-align: left;
        font-size: 1.1rem;
        line-height: 1.6;
        color: var(--dark-gray);
      }

      .quality-check {
        position: absolute;
        left: 0;
        color: var(--main-green);
        font-weight: bold;
        font-size: 1.2rem;
      }

      .quality-quote {
        font-style: italic;
        color: var(--text-color);
        font-size: 1.3rem;
        margin-top: 2.5rem;
        position: relative;
        padding: 1.5rem;
        background-color: var(--light-gray);
        border-radius: 12px;
        font-weight: 500;
        line-height: 1.5;
      }

      /* Animations */
      @keyframes fadeInUp {
        from {
          opacity: 0;
          transform: translateY(20px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      /* Hero Badges */
      .hero-badges {
        display: flex;
        justify-content: center;
        gap: 1rem;
        margin-top: 2rem;
      }

      .badge {
        background-color: rgba(0, 50, 0, 0.2);
        backdrop-filter: blur(9px);
        -webkit-backdrop-filter: blur(9px);
        padding: 0.6rem 1.4rem;
        border-radius: 50px;
        font-weight: 500;
        font-size: 0.95rem;
        color: white;
        transition: all 0.3s ease;
      }

      .badge:hover {
        background-color: var(--gold);
        transform: translateY(-3px);
      }

      /* Scroll Indicator */
      .hero-scroll-indicator {
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        width: 30px;
        height: 50px;
        animation: fadeInUp 1.5s ease infinite alternate;
      }

      .scroll-arrow {
        position: relative;
        width: 20px;
        height: 20px;
        border-right: 2px solid rgba(255, 255, 255, 0.8);
        border-bottom: 2px solid rgba(255, 255, 255, 0.8);
        transform: rotate(45deg);
        margin: 0 auto;
        animation: scrollArrow 1.5s infinite;
      }

      @keyframes scrollArrow {
        0% {
          transform: rotate(45deg) translate(-5px, -5px);
          opacity: 0;
        }
        50% {
          opacity: 1;
        }
        100% {
          transform: rotate(45deg) translate(5px, 5px);
          opacity: 0;
        }
      }

      /* Intro Section - Apple Style */
      .intro-section {
        padding: 120px 2rem;
        background-color: white;
      }

      .container {
        overflow: hidden;
        max-width: 1200px;
        margin: 0 auto;
        width: 100%;
        box-sizing: border-box;
        padding: 0 1rem;
        overflow-x: hidden;
      }

      .section-title {
        text-align: center;
        font-size: 2.8rem;
        margin-bottom: 1.5rem;
        font-weight: 700;
        letter-spacing: -0.01em;
        line-height: 1.1;
        color: var(--main-green);
        position: relative;
        z-index: 1;
        display: inline-block;
      }

      .section-title::after {
        content: "";
        position: absolute;
        left: 50%;
        bottom: -15px;
        width: 350px;
        height: 4px;
        background: linear-gradient(
          to right,
          rgba(212, 175, 55, 0) 0%,
          var(--gold) 50%,
          rgba(212, 175, 55, 0) 100%
        );
        transform: translateX(-50%);
        border-radius: 2px;
      }

      .section-subtitle {
        text-align: center;
        font-size: 1.2rem;
        margin-bottom: 3rem;
        color: var(--dark-gray);
        max-width: 700px;
        margin-left: auto;
        margin-right: auto;
        position: relative;
        z-index: 1;
      }

      .section-subtitle {
        text-align: center;
        font-size: 1.3rem;
        color: var(--dark-gray);
        margin-bottom: 4rem;
        max-width: 700px;
        margin-left: auto;
        margin-right: auto;
        font-weight: 400;
      }

      .intro-content {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 5rem;
        align-items: center;
      }

      .intro-text h3 {
        font-size: 2rem;
        margin-bottom: 1.5rem;
        color: var(--text-color);
        font-weight: 600;
        letter-spacing: -0.02em;
        line-height: 1.2;
      }

      .intro-text p {
        margin-bottom: 1.8rem;
        font-size: 1.1rem;
        line-height: 1.6;
        color: var(--dark-gray);
      }

      .intro-image {
        position: relative;
      }

      .intro-image img {
        width: 100%;
        border-radius: 12px;
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.1);
      }

      .intro-image::after {
        content: "";
        position: absolute;
        top: 20px;
        left: 20px;
        right: 20px;
        bottom: 20px;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        z-index: -1;
      }

      /* Discount Alert */
      /* iOS Style Notification */
      .ios-notification {
        position: fixed;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        width: 90%;
        max-width: 400px;
        background-color: rgba(40, 40, 40, 0.85);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border-radius: 13px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        padding: 12px 15px;
        z-index: 1000;
        display: flex;
        align-items: center;
        transition: all 0.3s ease-in-out;
        opacity: 0;
        visibility: hidden;
        transform: translateX(-50%) translateY(-20px);
      }

      .ios-notification.show {
        opacity: 1;
        visibility: visible;
        transform: translateX(-50%) translateY(0);
      }

      .ios-notification-icon {
        width: 40px;
        height: 40px;
        min-width: 40px;
        background-color: var(--discount-red);
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
      }

      .ios-notification-icon svg {
        width: 24px;
        height: 24px;
        fill: white;
      }

      .ios-notification-content {
        flex: 1;
      }

      .ios-notification-app {
        font-size: 0.75rem;
        font-weight: 500;
        color: rgba(255, 255, 255, 0.7);
        margin-bottom: 2px;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .ios-notification-time {
        font-size: 0.75rem;
        color: rgba(255, 255, 255, 0.5);
      }

      .ios-notification-title {
        font-size: 0.95rem;
        font-weight: 600;
        color: white;
        margin-bottom: 3px;
      }

      .ios-notification-message {
        font-size: 0.85rem;
        color: rgba(255, 255, 255, 0.9);
        line-height: 1.3;
      }

      .ios-notification-code {
        font-weight: 600;
        color: var(--gold);
      }

      /* Turkey Map Section */
      .map-section {
        padding: 60px 2rem 0 2rem;
        background-color: white;
        overflow: hidden;
      }

      .map-container {
        position: relative;
        max-width: 900px;
        height: 600px;
        margin: 0 auto;
        overflow: visible; /* Pin'in dışarı taşmasına izin ver */
        border-radius: 20px;
        width: 100%;
        box-sizing: border-box;
      }

      .turkey-map {
        width: 100%;
        height: 100%;
        transform-origin: center;
        transition: transform 8s cubic-bezier(0.26, 0.86, 0.44, 0.985);
        max-width: 100%;
        overflow: hidden;
        position: relative;
      }

      /* Mobil cihazlar için harita ayarları */
      @media (max-width: 768px) {
        .map-container {
          height: 350px;
        }

        .map-pin {
          /* Pin başlangıçta gizli olsun */
          opacity: 0;
          visibility: hidden;
        }

        .map-pin.visible {
          opacity: 1 !important;
          visibility: visible !important;
          transition: opacity 0.5s ease;
          z-index: 999 !important; /* En üstte görünmesi için */
        }

        .pin-icon {
          width: 10px !important; /* Daha büyük pin */
          height: 10px !important;
          background-color: #e74c3c !important; /* Daha parlak renk */
          display: block !important;
          position: relative !important;
          z-index: 1000 !important;
        }

        .pin-pulse {
          width: 60px !important; /* Daha büyük pulse efekti */
          height: 60px !important;
          background-color: rgba(
            231,
            76,
            60,
            0.8
          ) !important; /* Daha belirgin pulse */
          display: block !important;
          position: absolute !important;
          z-index: 999 !important;
        }

        .pin-label {
          font-size: 16px !important;
          padding: 6px 12px !important;
          background-color: rgba(
            0,
            0,
            0,
            0.9
          ) !important; /* Daha koyu arka plan */
          color: white !important;
          font-weight: bold !important;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5) !important; /* Daha belirgin gölge */
          border: 2px solid rgba(255, 255, 255, 0.5) !important; /* Daha belirgin beyaz kenarlık */
          display: block !important;
          position: absolute !important;
          top: 40px !important; /* Pin'in altında görünmesi için */
          white-space: nowrap !important;
          z-index: 1000 !important;
        }
      }

      .turkey-map svg {
        width: 100%;
        height: 100%;
      }

      .turkey-map path {
        fill: #f5f5f5;
        stroke: #ddd;
        stroke-width: 0.5;
        transition: all 0.3s ease;
      }

      .turkey-map path:hover {
        fill: #c30c0c;
      }

      /* Highlight Osmaniye province */
      .turkey-map #TR80,
      .turkey-map path.province-osmaniye {
        fill: var(--main-green) !important;
        stroke: var(--secondary-green);
        stroke-width: 1.5;
        filter: drop-shadow(0 0 5px rgba(139, 195, 74, 0.7));
      }

      .map-pin {
        position: absolute;
        /* Pin'in konumu JavaScript'te dinamik olarak ayarlanacak */
        transform: translate(-50%, -50%);
        z-index: 10;
        opacity: 0;
        visibility: hidden;
        transition: opacity 1s ease;
        pointer-events: none; /* Pin'in altındaki haritaya tıklamayı engellememesi için */
      }

      .map-pin.visible {
        opacity: 1;
        /* Pin'i kalıcı olarak görünür yap */
        animation: none;
        visibility: visible !important;
      }

      .pin-icon {
        width: 20px;
        height: 20px;
        background-color: var(--gold);
        border-radius: 50% 50% 50% 0;
        transform: rotate(-45deg);
        margin: 0 auto;
        position: relative;
        box-shadow: 0 0 0 rgba(212, 175, 55, 0.4);
      }

      .pin-pulse {
        position: absolute;
        width: 40px;
        height: 40px;
        background-color: rgba(212, 175, 55, 0.4);
        border-radius: 50%;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        animation: pulse 1.5s infinite;
      }

      .pin-label {
        position: absolute;
        top: 30px;
        left: 50%;
        transform: translateX(-50%);
        background-color: rgba(255, 255, 255, 0.9);
        padding: 5px 10px;
        border-radius: 4px;
        font-weight: 600;
        font-size: 14px;
        color: var(--main-green);
        white-space: nowrap;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      }

      @keyframes bounce {
        from {
          transform: rotate(-45deg) translateY(0);
        }
        to {
          transform: rotate(-45deg) translateY(-10px);
        }
      }

      @keyframes pulse {
        0% {
          transform: translate(-50%, -50%) scale(0.5);
          opacity: 1;
        }
        100% {
          transform: translate(-50%, -50%) scale(1.5);
          opacity: 0;
        }
      }

      /* Media Queries */
      @media (max-width: 768px) {
        html,
        body {
          overflow-x: hidden;
          width: 100%;
          max-width: 100%;
          position: relative;
        }

        .hero-title {
          font-size: 2.5rem;
        }

        .hero-subtitle {
          font-size: 1.4rem;
        }

        .quality-container {
          grid-template-columns: 1fr;
        }

        .parallax-section h2 {
          font-size: 2.2rem;
        }

        .intro-content {
          grid-template-columns: 1fr;
        }

        .usage-content {
          grid-template-columns: 1fr;
        }

        .hero-badges {
          flex-wrap: wrap;
        }

        .section-title {
          font-size: 2rem;
        }

        .cta-section {
          width: 100%;
          max-width: 100%;
          box-sizing: border-box;
          padding: 80px 1rem;
        }

        .cta-section h2 {
          font-size: 2rem;
        }

        .cta-section p {
          font-size: 1.1rem;
        }

        .quality-quote {
          font-size: 1.1rem;
        }

        .parallax-section .content-wrapper {
          padding: 1.5rem;
        }

        .parallax-section p {
          font-size: 1.1rem;
        }

        .map-container {
          height: 400px;
          width: 100%;
          max-width: 100%;
          overflow: hidden;
        }

        .container {
          padding: 0 1rem;
          width: 100%;
          max-width: 100%;
          overflow-x: hidden;
        }

        .analysis-boards {
          grid-template-columns: 1fr;
          gap: 1.5rem;
        }
      }

      @media (max-width: 480px) {
        .hero-title {
          font-size: 2rem;
        }

        .hero-subtitle {
          font-size: 1.2rem;
        }

        .feature-grid {
          grid-template-columns: 1fr;
        }

        .benefits-grid {
          grid-template-columns: 1fr;
        }

        .cta-buttons {
          flex-direction: column;
          gap: 1rem;
        }

        .cta-button {
          width: 100%;
        }

        .process-timeline {
          padding: 2rem 1rem;
        }

        .timeline-item {
          padding-left: 2rem;
        }
      }
    </style>
  </head>
  <body>
    <div class="category-hero">
      <div class="hero-content">
        <h1 class="hero-title" data-aos="fade-up">Soğuk Sıkım Zeytinyağları</h1>
        <p class="hero-subtitle" data-aos="fade-up" data-aos-delay="200">
          Amanos Dağları'nın eteklerinden gelen %100 doğal soğuk sıkım
          zeytinyağları
        </p>
        <div class="hero-badges" data-aos="fade-up" data-aos-delay="300">
          <div class="badge">
            <span>Erken Hasat</span>
          </div>
          <div class="badge">
            <span>Düşük Asit</span>
          </div>
          <div class="badge">
            <span>Katkısız</span>
          </div>
          <div class="badge">
            <span>Doğal</span>
          </div>
        </div>
        <div class="hero-scroll-indicator">
          <div class="scroll-arrow"></div>
        </div>
      </div>
    </div>

    <div class="intro-section">
      <div class="container">
        <div class="title-container">
          <h2 class="section-title" data-aos="fade-up">
            Doğal Üretimle Elde Edilen Katkısız Zeytinyağları
          </h2>
        </div>
        <p class="section-subtitle" data-aos="fade-up" data-aos-delay="100">
          Amanos Dağları'nın eteklerinden gelen %100 doğal, erken hasat, soğuk
          sıkım zeytinyağları
        </p>
        <div class="intro-content">
          <div class="intro-text" data-aos="fade-right" data-aos-delay="200">
            <h3>Soğuk Sıkım Zeytinyağı Nedir?</h3>
            <p>
              Soğuk sıkım zeytinyağı, zeytinlerin maksimum 27°C sıcaklıkta,
              hiçbir ısı uygulanmadan işlendiği özel bir üretim yöntemidir. Bu
              yöntem sayesinde zeytinin doğasında bulunan vitaminler, mineraller
              ve antioksidanlar bozulmadan korunur.
            </p>
            <p>
              Doğal, aromatik ve besin değeri yüksek bir zeytinyağı arıyorsanız,
              soğuk sıkım naturel sızma zeytinyağı sizin için en doğru
              tercihtir.
            </p>
            <p>
              Amanos Dağları'nın eşsiz mikroklima koşullarında yetişen
              zeytinlerimiz, kendine özgü aroması ve yüksek besin değerleri ile
              fark yaratır.
            </p>
          </div>
          <div class="intro-image" data-aos="fade-left" data-aos-delay="300">
            <img
              src="https://www.dogalyasam.net/image/catalog/icons/new/deliceOile.jpg"
              alt="Soğuk Sıkım Zeytinyağı"
            />
          </div>
        </div>
      </div>
    </div>

    <div class="features-section">
      <div class="container">
        <div class="title-container">
          <h2 class="section-title" data-aos="fade-up">
            Özel Üretim Sürecimiz
          </h2>
        </div>
        <p class="section-subtitle" data-aos="fade-up" data-aos-delay="100">
          Zeytinyağımızı özel kılan üretim sürecimizin detayları
        </p>
        <div class="process-stepper" id="processStepperAnimation">
          <!-- Adım 1: Soğuk Sıkım -->
          <div class="step">
            <div class="step-icon">
              <img
                src="https://www.dogalyasam.net/image/catalog/icons/new/cold-pres-icon.png"
                alt="Soğuk Sıkım"
              />
            </div>
            <div class="step-content">
              <h3 class="step-title">Soğuk Sıkım</h3>
              <p class="step-description">
                Zeytinler 27°C'nin altında sıkılarak, besin değerleri ve
                antioksidanlar bozulmadan korunur.
              </p>
            </div>
          </div>

          <!-- Adım 2: Düşük Asit -->
          <div class="step">
            <div class="step-icon">
              <img
                src="https://www.dogalyasam.net/image/catalog/icons/new/asit-icon-2.png"
                alt="Düşük Asit"
              />
            </div>
            <div class="step-content">
              <h3 class="step-title">Düşük Asit</h3>
              <p class="step-description">
                %0.3-0.6 asit oranı ile uluslararası standartlara göre "Natürel
                Sızma Zeytinyağı" kategorisinde yer alır.
              </p>
            </div>
          </div>

          <!-- Adım 3: Erken Hasat -->
          <div class="step">
            <div class="step-icon">
              <img
                src="https://www.dogalyasam.net/image/catalog/icons/new/erly-harvest-icon.png"
                alt="Erken Hasat"
              />
            </div>
            <div class="step-content">
              <h3 class="step-title">Erken Hasat</h3>
              <p class="step-description">
                Zeytinler tam olgunlaşmadan toplanarak daha yoğun aroma ve daha
                fazla polifenol elde edilir.
              </p>
            </div>
          </div>
        </div>

        <script>
          // Stepper animasyonu için IntersectionObserver kullanımı
          document.addEventListener("DOMContentLoaded", function () {
            const stepperObserver = new IntersectionObserver(
              (entries) => {
                entries.forEach((entry) => {
                  if (entry.isIntersecting) {
                    const stepper = entry.target;
                    stepper.classList.add("animate");

                    // Adımları sırayla göster
                    const steps = stepper.querySelectorAll(".step");
                    steps.forEach((step) => {
                      step.classList.add("animate");
                    });

                    // Gözlemlemeyi durdur
                    stepperObserver.unobserve(stepper);
                  }
                });
              },
              {
                threshold: 0.3,
              }
            );

            const stepper = document.getElementById("processStepperAnimation");
            if (stepper) {
              stepperObserver.observe(stepper);
            }
          });
        </script>
      </div>
    </div>

    <!-- Türkiye Haritası Bölümü -->
    <div class="map-section">
      <div class="container">
        <div class="title-container">
          <h2 class="section-title" data-aos="fade-up">
            Bahçelerimizin Konumu
          </h2>
        </div>
        <p class="section-subtitle" data-aos="fade-up" data-aos-delay="100">
          Zeytinliklerimiz Osmaniye'nin Akyar bölgesinde, Amanos Dağları'nın
          eteklerinde yer almaktadır
        </p>

        <div class="map-container" data-aos="fade-up" data-aos-delay="200">
          <div id="turkeyMap" class="turkey-map">
            <!-- SVG haritası buraya eklenecek -->
            <svg
              xmlns="http://www.w3.org/2000/svg"
              baseprofile="tiny"
              fill="#6f9c76"
              height="422"
              stroke="#ffffff"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width=".5"
              version="1.2"
              viewbox="0 0 1000 422"
              width="1000"
            >
              <g id="features">
                <path
                  d="M889.7 82.2l-3.1 1.1-4.8 2.7-7.7 3.3-7.8 3.3-5.7 3-1.1 4.8-0.7 7.5-0.9 4.5-5.7 0.6-5.5 1.1-1.4-2.6-1.9-1.9-1.4-2.1-1-2.4-1.3-1.7-1.8-1.2-2.4-3.7-1-4.9 3-5.9 4.1-4.6 4-2 1.2-4.2-1.3-4.4-0.9-2.3-0.4-0.7-0.5-1.5-0.3-2.7-0.7-3.6 0.5 0 1-0.7 1.4-2.2 1.1-1 0.5-1.1 0.4-2.6 0.5-1 0.9-0.8 0.1 0 0.2 0 0.9-0.4 2.4-0.2 6.7 0.6 0.9 0.4-0.4 0.6-0.1 0.7-0.1 0.6-0.2 0.6-0.4 0.4-1 0.8-0.4 0.7 1.3 0.7 0.9 1 0.9 0.3 1.1-1.4 0.7-0.4 0.3 0.4 0.3 0.8 0.4 0.7 0.3 0.3 1.4 0.4 0.3 0.3 0.2 0.3 0.2 0.2 0.5 0.2 0.3 0.4 1.4 2.7 0.7 0.8 3.5 2.4 2.3 2 1.5 0.7 1.4 0.2 0 0.3-0.6 0.9-0.7 0.6-2.6 1.3 1.3 0.4 1.1-0.1 0.9 0.1 1 1.1 1.1 2.8 0.7 0.5 4.4-1.2 1.3-0.1 1.5 0.4 1.3 0.8 1.3 1.2 0.7 1.5-0.5 1.6z"
                  id="TR75"
                  name="Ardahan"
                ></path>
                <path
                  d="M842.7 61.7l0.7 3.6 0.3 2.7 0.5 1.5 0.4 0.7 0.9 2.3 1.3 4.4-1.2 4.2-4 2-4.1 4.6-3 5.9-2.8 0.3-2.8 0.6-3.3-0.3-3.2 0.3-1.6 0.8-1.5 1.3-0.6 2.5 0.3 2.7-1.2 2.5-1.9 1.9-0.9 2.5-0.1 2.8-2.2 3.3-4-2.3-3.8-1.1-3.7 1.1-4 2.6-4.4 1.7-3.2-2.9 0.6-5.3 1.3-2.2-0.4-2.1-1.7-0.6-5.9-0.9-3.7-2.1 0.7-2.5 4.5-5.3 1.1-0.6 1-0.9 0.4-1.3 0.7-1.3 2.6-1.7 1.3-2.9-1.4-1.6-1.9-0.9-0.5-1.1-0.6-1.1-0.9-0.6-0.9-0.9-0.9-2.1-0.4-2.5-1.3-3.3-0.1-0.2 4.1-3.3 1.2-0.4 1-0.6 0.5-0.2 1.7-0.2 0.6-0.2 0.9-0.7 0.7-1.1 0.5-1.4 0.2-1.4 0.5-0.3 2.7-2.5 0.7-1.2 5 2.2 0.7 0.1 1.4-0.1 1.2-0.6 0.3 0 0.1 0.3-0.1 0.5 0 0.5 0.2 0.4 0.6 0.2 1.4 0.2 0.6 0.2 1.9 1.7 0.6 0.3 0.4-0.3 1.9-1.6 1.5-2.1 0.7-0.5 1.9-0.8 3.4 1.3 1.7-0.5 0.6-0.4 0.7-0.1 0.7 0.1 0.7 0.4 1.4-0.5 0.8 0 0.6 0.5 0.8 0.7 1.2 0.1 2.3-0.1 8.3 3.2 0.7 0z"
                  id="TR08"
                  name="Artvin"
                ></path>
                <path
                  d="M886 315.3l-1.3-0.9-0.6-0.1-0.8 0.1-0.5 0.2-0.4 0.4-0.4 0.1-0.4 0-0.4-0.2-6.2-3.3-0.8-0.3-1.5 0.2-1.9 0.5-1.8 0.8-1.2 0.9-2.1 0.7-1.9-0.3-3.9-1.3-0.4-0.3-0.2-1-0.4-0.4-0.6 0-0.4 0-2.4 1-0.3 0.2-0.3 0.5-0.1 0.4 0 0.4-0.2 0.5-6 8.7-0.5 1.6-0.2 0.3-0.8 0.4-4 0.7-2.8 0.9-2.1 0.2 0-1.7-0.1-0.8-0.4-0.4-0.5-0.7 0.8-3.4-0.3-0.7-0.3-0.2-1.6-1.3-0.2-0.3-0.3-0.6-0.2-0.3-0.3-0.1-0.3 0.2-0.3 0.4-0.3 0.1-1.4-0.7-0.7-0.1 0-0.3 0.1-0.6-0.1-0.7-0.5-0.6-0.9 0.7-0.7 0.4-2.4 3.2-3.5 3.3-2.1 1.3-5.4 1.3-0.9-3.8-3-2.4-5.5-1.7-0.8-2.5 2-2.2 4.5-2.5 3.5-1.7 0.6-4-1.3-5.2-2.8-7.5 6.7 3.3 9.4 0.7 3.7-3.7 6.3 0.3 9.6 2.5 10 1.4 8.1 0.8 2.9-3 0.5-3.4 3.8-2 4.1 0.5 3.6 1.5 1.2 0.3 2.3-0.1 2.3-0.3-0.3 5.5-1 3.5 0.8 3.2 1.6 3.9-0.4 5.2-1.2 3.2-0.6 1.2z"
                  id="TR73"
                  name="Sirnak"
                ></path>
                <path
                  d="M886 315.3l0.6-1.2 1.2-3.2 0.4-5.2-1.6-3.9-0.8-3.2 1-3.5 0.3-5.5 6.4 0 6.3-1.1 1.3-0.7 1.4-0.5 2 0.3 2 0.8 3 0 3.1-0.6 2.6-1.6 1.2-3.3 1.9-3.4 3.5-0.7 4.1 0.5 0.1 0-0.2 0.5 0.9 1.3 1.6 0.3 3.1-0.4 0.6 0.2 1.7 0.8 0.5 0.4 0.3 0.5 0.1 0.7 0.3 0.8 0.5 0.6 0.4 0.2 0.3-0.1 0.3-0.1 0.4 0.3 0 0.3-0.2 0.9 0 0.4 0.3 0.7 0.7 0 0.8-0.2 0.6-0.4 1.3-0.1 1.3 0.6 1.2 1 1.7 2.2 0.1 0.5-0.4 0.8-1.7 1.3-0.4 0.6-0.2 1.8 1 1.1 1 0.8 0.2 1-0.4 1.2-0.3 1.5-0.1 1.5 0.2 1.2 0.2 1-0.6 1.8 0.3 1 0.6 0.3 1.8 0.2 0.7 0.6 0.7 1.7 0.4 0.5 1.8-0.3 0.4 0.5 0.3 0.9 0.4 0.8 2.7 1.8 1.1 1.2-0.2 2-0.5 0.7-0.7 0.4-0.6 0.5-0.3 1 0.2 1.2 0.4 1 0.3 1.1-0.3 1.1-0.6-1-0.9-0.5-5-0.7-0.9 0-1.5 0.7-1.9 1.4-1.7 1.6-1.1 1.5-1.2 0.9-1.2 0.7-4.1 1.3-0.4 0.7-0.1 0.9-0.8 1.3-0.4 1-0.5 0.4-0.6 0.1-1.9-0.5-0.5-0.4-0.3-0.6-1.9-4.3-0.3-1.3 0-1.2 0.4-1.2 0.8-1.1 0.6-0.3 0.6-0.1 0.4-0.2 0.4-0.7 0.1-0.6-0.1-0.8-0.2-1.3-0.4-1.3-0.6-1.1-0.8-0.8-1-0.7-4.6-1.9-0.9-0.1-1.6-0.3-2.1 0.3-1.8 1.5-1.4 2.1-1.4 1.7-2.6 0.4-0.8 0.9-0.6 0.2-0.4-0.3-1-1-0.5-0.3-1.1-0.3-1.2-0.1-4.9 0.3-1.1-0.1-1.2-0.5-0.9-0.7-0.4-0.2-0.6-0.1-0.6 0.1-1.2 0.5-0.6 0.1-0.8-0.4-2.2-1.8-1.9-1-0.6-0.5z"
                  id="TR30"
                  name="Hakkari"
                ></path>
                <path
                  d="M938 173.5l-0.8-0.6-1.8-0.5-8.5 2-3.7-0.5-3.5-1.1-4.7 0.6-4.4-0.7-0.3-0.9-0.3-0.9-3-0.7-4.3-2-1.4-1.9-1.1-2.3-1.5-1.1-1 0-1.6-0.7-0.6-0.5-1.5-0.3-3.2 1.3-3.1 2.1-1.6-0.6-0.7-0.7-0.7-2.3 0-1.5 0.2-2.9-1-3.5 1.8-2.1 0-2.4 2.5-1.2 5.9-0.8 6.2-2.3 2.2 1.2 4.4 1.3 5.2 2.9 5.7 0.9 0.7-0.2 1.7-1.2 1-0.3 0.5 0.2 0.6 0.6 0.4 0.1 0.5-0.1 0.9-0.3 1.4-0.1 2.8-0.8 1.6 0 1.8 0.5 2.6 1.6 4 2.3 2.7 2.4 1.2 1.4 0.6 0.9 0.2 0.9 0.2 0.5 0.4 0.4 0.7 0.6 0.1 0.6 0.1 0.7 0.1 0.3 0.7-0.4 1.7 2 0.9 0.6 0.2 0 0.5-0.1 0.3 0.1 0.2 0.2 0.3 0.5 2.9 3.8 0.7 1.3 0.5 0.7 0.5 2.4 0.5 0.7-3.8-2.8-0.9-0.8-2.9-1.3-0.6-0.7-1.3-2-0.7-0.5-1.2 0.4-5.1 4.1-0.2 0.7 0.3 2-0.1 0.8z"
                  id="TR76"
                  name="Iğdir"
                ></path>
                <path
                  d="M938 173.5l0 0.2-0.3 0.8-1.6 3.2-0.2 0.8-0.1 1 0.1 0.9 0.1 0.8 0 0.7-0.5 0.7-0.2 1.4 0.2 1.2-0.1 1.1-0.8 1.2-0.9 0.7-2.4 1.1-1.1 0.3-1.2-0.1-3.3-1.7-1.1-0.1-2.6 0.7-1.1 0.1-1-0.1-0.8 0-0.7 0.2-0.9 0.7-0.6 0.7 0.3 0.7 1.3 1.5-6.7 1.8-1.8-0.3-0.8-0.3-2.8-0.3-2.4-0.7-1.8-0.3-1.8 0.2-1.3 1-1.2 1.3-1.7-0.1-4.1-0.8-2.1-1.3-2.8-2.9-3.2-1.5-3.7-0.2-2.1 1.6-1.7 2.3-1.6 0.5-2.9 0.6-1.1 0.4-0.4 1.9 1.8 1 1.9 0.8 0.3 1.9-0.3 2.1-0.1 2.1-1.2 1.3-5.5 2.3-8.8 1.9-1.6 1.3-1.6 1-2.3 0.7-2 1.3-0.2-3.2 0.4-3 0.5-2.4-0.1-0.9-0.2-0.8 0.2-4-1.7-2.5-1.2-1.2-0.8-1.7-1.5-1.7-1.5-1.4-1.9-3.4-1.4-3.8 1.5-2.5 0.5-2.9-0.1-8.3 0.5-1.7 0.6-0.4-6.7-2.8-3.9-4.7 1.7-0.7 3.6 0.6 1.7-0.6 1-1.5 1.2-0.5 0.6-0.1 1.2 0.1 1.6-0.9 1.3-1.5 9.2 1.8 14.6-4.2 5.2 0.1 2.2 0.6 2.1-0.5 2.6 1.1 1 3.5-0.2 2.9 0 1.5 0.7 2.3 0.7 0.7 1.6 0.6 3.1-2.1 3.2-1.3 1.5 0.3 0.6 0.5 1.6 0.7 1 0 1.5 1.1 1.1 2.3 1.4 1.9 4.3 2 3 0.7 0.3 0.9 0.3 0.9 4.4 0.7 4.7-0.6 3.5 1.1 3.7 0.5 8.5-2 1.8 0.5 0.8 0.6z"
                  id="TR04"
                  name="Agri"
                ></path>
                <path
                  d="M887.1 289.6l-2.3 0.3-2.3 0.1-1.2-0.3-3.6-1.5-4.1-0.5-3.8 2-1.9-4.5 0.5-1.6 0.7-1.5 0.2-3.5 0.6-2.4 0.2-2.6-0.3-2.4 0.2-2.5-0.5-4.3-3.4-1.3-3.8-0.7-3.7 0.3-2.5-0.9-0.9-3.1 0.5-2.1-0.1-1.6-0.2-0.7-1.7-3.9-0.3-1.3 0.5-2.5 0.8-0.9 5.1-2.9 3-2.1 1-1.7 0.8-1.9 4.4-6.5 2.6-2.8 3.4-2.6 1.4-4.5-1.9-3-5.6-3.4-1.4-3.1-0.1-4.6 5.5-2.3 1.2-1.3 0.1-2.1 0.3-2.1-0.3-1.9-1.9-0.8-1.8-1 0.4-1.9 1.1-0.4 2.9-0.6 1.6-0.5 1.7-2.3 2.1-1.6 3.7 0.2 3.2 1.5 2.8 2.9 2.1 1.3 4.1 0.8 1.7 0.1 1.2-1.3 1.3-1 1.8-0.2 1.8 0.3 2.4 0.7 2.8 0.3 0.8 0.3 1.8 0.3 6.7-1.8 0.2 0.6 0.3 1.3 0.5 1.2 0 0.5-0.1 0.4 0 0.6 0.2 0.5 0.5 1.2 0.1 0.5-0.3 1.1 0 0.6 1.2 0.9 2.2 1.1 1.8 1.3 0.4 1.5-1 1.9-0.2 1 0.4 2.4-0.4 0.7-0.8 0.6-0.7 1.2 0.3 0.4 0.1 0.4 0.2 1 0.3 0.4 0.5 0.3 0.3 0.4 0.1 0.6-0.1 0.5 0.2 0.5 0.2 0.4 0.4 1.4 0.7 0.9 0.8 0.8 2.5 1.2 0.2 1.8-1.3 4.4-0.2 1.2 0.1 0.7 0.3 0.8 0.2 1.1-0.2 0.4-0.4 0.4-0.2 0.4 0.6 0.3 0.9 0.3 0.3 0.2 0.4 0.4 0.3 1 0 1.3-0.2 2.4 0.4 3.1 0 1-0.6 2.5 0 0.7 0.1 1.5 0 0.6-0.2 1 0.2 0.5 0.4 0.3 0.7 0.3 2.4 0.5 1.5-0.9 0.6-0.2 0.8 0.3 0.7 0.6 0.5 0.8 0.4 1 0 0.9-1 1.6-1.8 1.3-1.3 1.5 0.2 2.9-0.2 1.2-0.2 1.2-0.4 0.9-0.3 0.5-0.9 0.3-0.3 0.4-0.1 0.5 0.4 1.1-0.3 0.4-0.9 0.4-0.6 0.5-0.5 0.8-0.4 1.3-0.4 0.9-1.1 1.3-0.5 0.9-0.4 0.7-0.3 0.8 0 0.7 0.2 0.9 0 0.8-0.4 0.6-0.5 0.6 0 0.2-0.1 0-4.1-0.5-3.5 0.7-1.9 3.4-1.2 3.3-2.6 1.6-3.1 0.6-3 0-2-0.8-2-0.3-1.4 0.5-1.3 0.7-6.3 1.1-6.4 0z"
                  id="TR65"
                  name="Van"
                ></path>
                <path
                  d="M166.1 54.8l-2.2 3-0.1 0-8.3 0.9-8.2 1.1-5.2 1.2-4.6 1.4-2.3 3.4-1.7 3.5-2.7 2.1-3.1 0.8-2.6-1.7-2.1-2.8-2.7-1.8-5.5 0-7.7 0.6-7.2 1.6-1.4-2.9 2.3-4.8 0.7-4.6 1.9-2.9 1.1-6.2-0.6-6.1-1.5-6.1-1.2-7.3 0-0.1 0.6-0.5 0.5-0.3 0.5 0 0.5 0.1 0.4 0 0.5-0.5 0.5-0.2 0.9 0.2 0.4-0.1 0.6-0.7 0.3-0.8 0.5-0.6 0.6-0.3 1-0.6 1-2 1.1-0.6 0.1 0 0.8 0 0.9 0.3 0.8 0.5 0.7 0.6 0.6-0.1 1 0.1 1.2 0.3 0.2-0.1 0-0.2 0.2-0.3 1-1.4 0.6-0.5 1-0.1 1.7 0.3 1.5 1 1.3 1.2 2 3.2 1.1 1.2 1.1 1 3.5 2 0.7 0.6 1.2 1.6 0.6 0.4 0.8-0.3-0.7-0.3 0.1-0.1 0.3-0.2 0.2-0.1-0.2-0.1-0.1-0.1-0.1-0.1-0.1-0.1 1-0.4 0.5-0.3 0.4-0.5 0.3 0.2 0.3 0 0.2-0.3 0.1-0.6 3.7-1 1.7 0.1 2.5 1.4 0.6 0.2 0.7-0.1 0.6-0.2 0.1-0.4-0.7-0.4 0.1-0.7 0-0.8 0.1-0.7 0.2 0 0.2-0.1 0.2 0.1 0.2 0 0.9-0.1 0.4 0 1.2 0.3 1.2 0.6 0.7 0.2 2.3-0.2 0.7 0.2 1.7 0.4 0.7 3 0 0.7 0.2 0.2 0.3 0.4 0.2 0.5 0 0.4-0.5 0.3-0.5-0.1-0.8-0.4-0.8 0.1-0.6 0.3-0.4 0.9-0.2 1.6 0.3 1.7 0.7 1.1 0.9 1 0.7 1.2 0.9 2.8 0.6 0.9 0.8 0.8 0.4 0.4 0.3 0.7 0.1 0.1 0.1 0.2 0.1 0.4-0.1 0.2-0.3 0-0.2 0-0.1 0.4 0.4 1 0.6 1.2 0.8 1 1.4 0.8 0.7 0.9 1 1.6 1.2 1.2z"
                  id="TR39"
                  name="Kirklareli"
                ></path>
                <path
                  d="M101.2 27.1l0 0.1 1.2 7.3 1.5 6.1 0.6 6.1-1.1 6.2-1.9 2.9-0.7 4.6-2.3 4.8 1.4 2.9 0.6 1.1 0.3 8.8-0.7 3.8-2.3 3-1.5 3.8-0.8 4.4 0.3 5.1 1.8 3.9 1.2 4.1 0 0.2-0.3 3.7 0 0.3-3.1 0.5-1.2 0.5-0.6 0.2-3.2 0-1.3 0.4-3.6 2.2-1.4-0.9-0.8-0.3-0.7-0.1-0.7 0.3-0.7 0.4-0.8 0.3-1.2-0.7-0.8 0.1-2.6 0.7-8.8 0-1.2-0.2-0.7-0.4-0.4-1-0.3-1.1-0.4-1 0.3-0.9-0.1-1.4-0.5-1.4-0.7-0.9 0.4-0.1 0.1-0.2 1.8 0.2 1.5-0.7 1-1.3 0.4-1.6 0.8-1.2 2.5-2.2-0.3-0.7 0-0.4 0.2-0.2 0.4-0.2-0.1-0.1-0.2 0 0-0.1 0-0.3 0.4-0.1 0.2-0.2 0.1-0.3 0-0.6 0.2 0.5 0.1 0.3 0.3 0 0.3-0.4 0.3-0.5 0.4-1.3 0.3 0 0.2 0.4 0.2 0.3 0.3 0.2 0.4 0 0.7-0.2-0.1-0.4-0.2-0.4-0.2-0.3 0.6-0.6 1.4-0.6 0.6-0.5 0.5-0.7-0.1-0.2-0.5-0.2-0.5-0.6-0.1-0.6 0.1-0.4 0.4-0.2 0.7-0.1 0.4-0.4 0.4-0.7-0.1-0.7-1.2-0.7-0.5-0.6-0.6-1.4-0.1-1 0.8-1.6-0.4-0.3-0.7-0.2 0-0.5 0.9-1.4-0.6-1.1-0.1-1.1 0.2-1.2 0.5-1.4-0.7-1.1 0.5-0.5 3-0.3 0.8-0.4 1.3-1.2 1.3-0.8 0.3-0.3 0.1-0.3 0.2-0.2 0.5-0.1 2.4-2.6 0.5 0.7 0.9 0.5 1 0.2 0.9 0 0.6-0.7 0.4-1 0.3-1.4 0-1.3-0.2-1.3-0.8-3.1-0.2-2.3-0.3-1.1-0.5-0.9 0.3-0.7-0.2-1.6 0.2-0.8-0.1-0.3-0.1-0.5-0.1-0.4 0-0.5-0.9 1.4-0.5-0.5-2.1-1.2-2.7-2 0.4-0.8 0.2-0.9-0.8-0.2-0.8-0.4-1.9-0.4-1.2-1.2-0.9-0.3-1.2 0.1-0.5-0.7-0.3-1.2 0.1-1.4 0.7-1.6 2-1.7 4.9 0.2 2.2-0.7 0.7-0.8 0.4-1.2 0.2-1.2 0-0.4 0-0.4 0.1-0.4 0.2-0.5 0-0.4-0.1-0.2-0.6-0.4-0.1-0.2 0.2-0.4 0.3-0.3 0.4-0.3 1.3-1.5 0.8-0.5 0.9-0.1 4.4 0.7 0.9-0.1 0.5-0.3 1-1.1 0.6-0.1 0.5 0.2 0.8 0.8 0.6 0.1 0.4-0.2 0.4-0.4z"
                  id="TR22"
                  name="Edirne"
                ></path>
                <path
                  d="M846.7 114.1l5.5-1.1 5.7-0.6 0.9-4.5 0.7-7.5 1.1-4.8 5.7-3 7.8-3.3 7.7-3.3 4.8-2.7 3.1-1.1-0.2 1.5 0.3 2 0.8 1.8 0.9 0.8 2 0.3 2.1 1.1 1.9 1.5 1.4 1.9 0.6 1.2 0.2 1.2 0.1 1.3 0.3 1.4 0.4 1.3 2 3.1 0.5 1.2 0.1 1 0.3 4.4-0.2 0.5-1 1.8-0.2 0.3 0.3 1.5-2 2.3-0.6 1.3 0 1.5-0.3-0.2-0.2-0.1-0.2 0-0.3-0.1 0.2 0.2 0.1 0.2 0.1 0.5-1.1 0.1-1.1 0.7-1.6 1.9 0.2 0.9 1 1.1 1.9 1.6 0 0.5 0 0.1-0.7 2.4-0.4 0.7-0.3 1 0.4 1 1.3 1.2 2.1 2.8 0.7 1.5 0.1 0.2-0.9 0.6 0.5 1.1 1.5 1.9 0.3 0.7-0.5 0.8-2.1 0.4-0.7 0.6 0.6 1.8-6.2 2.3-5.9 0.8-2.5 1.2 0 2.4-1.8 2.1-2.6-1.1-2.1 0.5-2.2-0.6-5.2-0.1-14.6 4.2-9.2-1.8-1.4-2.3-1.1-2.5-1.2-1.9-1.9-0.8-1-0.3-0.8-0.6-0.6-1.3-0.8-1.1-1.4-1-1.5-0.5-4.1-0.7-1.1-1.3-1-1.7-1.7-2 1.5-3.3 6.9-2.4 3-1.7 1.5-1.2 1.6-1.1 2-0.9 1.9-1.2-1.1-6.2 0.7-2.2 0.3-2.1z"
                  id="TR36"
                  name="Kars"
                ></path>
                <path
                  d="M812.6 290.8l2.8 7.5 1.3 5.2-0.6 4-3.5 1.7-4.5 2.5-2 2.2 0.8 2.5 5.5 1.7 3 2.4 0.9 3.8-19.8 4.7-13.2 0.6-1-0.3-2.1-0.9-11.4-2.2-8.9 1.3-2.3 0.9-6.4 3.5-2.1 0.7-1.1 0-1.1 0.2-1 0.4-0.9 0.6-6.3 4.2-2.1-1.1-4.3-7.5 0.5-4.8-0.6-1.2-2.4-2.5-1.5-1.3-1.4-1.5-0.6-7 4.6-6 2.1-2 5.2-3.6 2.4-0.9 5.2-4.1 3.8-0.3 2.3-3 0.6-1.4 5.2-1.1 16.6 0.5 3-1.5 1.2 0 0.9 0.4 3.4 4.3 3.3 6.9 5.1 3.2 7.3-1.5 5.3-3.9 8.8-6.3z"
                  id="TR47"
                  name="Mardin"
                ></path>
                <path
                  d="M726.3 311.1l0.6 7 1.4 1.5 1.5 1.3 2.4 2.5 0.6 1.2-0.5 4.8 4.3 7.5 2.1 1.1-3.4 2.3-1.8 0.9-1.8 0.3-1.7 0.6-4.7 2.8-10.2 3.9-15.3 2.9-9.6 1.9-2.6 0.1-7.3-2.5-2.5 0.2-1 0.3-7.7 0.3-3.4-0.4-2.9-1.5-1.3-1.5-5.1-5.3-2.3-1.3-9-2.7-3.2-0.4-1.6 0.2-3.2 1.2-4.6 3 0-0.1-0.2-0.5-0.1-0.8 0.1-0.8 0.4-0.3 0.5-0.2-0.2-0.5-0.8-0.9-0.2-0.6-0.1-0.6 0-1.5-0.2-0.5-1.3-1.2-0.7-0.8-0.1-0.5 0-0.9 0.6-1.4 0.1-0.7-0.5-0.3-0.7 0.1-1.1 0.6-0.4 0.1-0.4-0.3-0.5-0.5-0.4-0.3-0.6 0.3-0.6-0.6-0.1-0.8 0.3-1.7 0.1-1.4-0.1-0.6-0.3-0.7-0.8-1.3-0.3-0.8-0.2-0.9 0.2-1 0.4-0.4 0.4 0.1 0.3 0.7 0.3 0 0-1.3-0.3-0.9-0.6-0.6-0.7-0.5 0.6-2.1 0.2-0.6 0.4-0.3 0.4-0.2 0.2-0.2 0.4-1.5 0.5-0.8 1.6-1.6 2.1-2.7 0.7-0.6 0.8 0 0.8 0.1 0.8-0.1 0.7 0.5 0.5 0 1.4-1 0.6 0.8 0.9 0.4 2.1 0.5 0.2 0.2 0.1 0.5 0.2 0.1 0.2-0.1 0.1-0.1 0.1-0.1 0.2-0.1 0.2-0.3-0.1-0.7 0.1-0.8 0.4-0.3 2.1 0.3 0.7-0.3 0.3-1.4 0.5 0 2.8-1 1.5 0.6 5.8-1 1-0.6 2.8-3.1 0.6-1 0.5-0.3 2.3-2.4 10.5-1.6-2.3-1.8-0.9-1 1-0.6 0.1 0.2 0.6 0.6 0.2 0.1 0.4-0.1 0.3-0.1 0.3-0.1 0.2-0.1 0.2-0.2 0.2-0.5 0.4-0.3 0.8 0.6 0.4 0 0.4-0.2 0.3-0.3-1.2-1.3-0.4-0.3 0.6-0.7 1-0.2 1.1-0.1 0.9-0.2-0.6-0.9 0.1-1.5-0.5-0.5-0.6 0.1-1.3 0.9-0.7 0.2 0.4-0.9 0.5-0.8 0.7-0.5 0.7-0.2 0.4 0 0.9 0.3 0.3 0.1 0.3-0.5 0.1-0.5-0.1-0.4-0.3-0.3 0.6-0.9 0.3-0.8-0.1-0.7-0.4-0.9 2.3-0.8 0.6-0.4 0.5-0.9 0.2-2 0.2-0.9 0.6-0.7 0.8-0.4 2.9-0.9 1.9-1.3 3.7 2 3.8 0.7 2.9 0.1 2.6 1.2 2.8 3 3.1 2.6 1.5 0.8 1.6 0.5 1.8 1.7 2.3 1 0.5-0.7 0.4-0.8 1.4 0.6 1.2 1.1 0.8 0.5 0.9 0.2 0.7 1.5-0.3 6.5 0.7 4.5 0.4 1.7 0.2 0.9 1.7 4.9 1.6 3.1 2.2 2.5z"
                  id="TR63"
                  name="Sanliurfa"
                ></path>
                <path
                  d="M608.6 352.8l-3.6 2.4-2-0.1-7.7-1.4-1.1 0.1-3 0.8-0.9-0.1-0.7-0.3-0.8 0-0.9 0.4-0.8 0.7-0.3 0.6-0.5 0.1-0.9-0.5-0.9-0.8-0.2-0.7 0.4-1.9 0-1.2-0.4-0.8-1.4-1.3-0.8-0.7-0.9-0.6-0.9-0.3-0.9-0.2-3-0.2-8.8-3.1 2.6-2.5 4-1.8 3.2-2.2 0.4-3.9 1.5-1.3 1.7 1 1.5 3.3 3.2 0.6 3.7 2.5 3.9 4 2 2.4 1.4-0.9 3.5 1.9 4.4 2.4 4 3.6z"
                  id="TR79"
                  name="Kilis"
                ></path>
                <path
                  d="M633.2 306.9l-0.8 0.1-0.8-0.1-0.8 0-0.7 0.6-2.1 2.7-1.6 1.6-0.5 0.8-0.4 1.5-0.2 0.2-0.4 0.2-0.4 0.3-0.2 0.6-0.6 2.1 0.7 0.5 0.6 0.6 0.3 0.9 0 1.3-0.3 0-0.3-0.7-0.4-0.1-0.4 0.4-0.2 1 0.2 0.9 0.3 0.8 0.8 1.3 0.3 0.7 0.1 0.6-0.1 1.4-0.3 1.7 0.1 0.8 0.6 0.6 0.6-0.3 0.4 0.3 0.5 0.5 0.4 0.3 0.4-0.1 1.1-0.6 0.7-0.1 0.5 0.3-0.1 0.7-0.6 1.4 0 0.9 0.1 0.5 0.7 0.8 1.3 1.2 0.2 0.5 0 1.5 0.1 0.6 0.2 0.6 0.8 0.9 0.2 0.5-0.5 0.2-0.4 0.3-0.1 0.8 0.1 0.8 0.2 0.5 0 0.1-0.8 0.5-1.4 0.4-7.6 3.5-7.8 1.7-2.9 1.2-3.4 2.2-4-3.6-4.4-2.4-3.5-1.9-1.4 0.9-2-2.4-3.9-4-3.7-2.5-3.2-0.6-1.5-3.3-1.7-1-1.5 1.3-0.4 3.9-3.2 2.2-4 1.8-2.6 2.5-0.3-0.5-0.8-0.1-0.6-0.5-0.4-1.2-0.8-0.8-3.8-1.7-3.1-3.1 1.1-2.2 1.8-1.7 3.3-4.2 2.6-5.1 1.8-2.8 2.2-2.5 5.7-2.6 2.3-3.1 2.3 1.5 1.3 5.3 3.7 1.2 4.3-4.6 2.6-1.3 6-1.1 6.3-2.2 3-0.4 3 0.1 2-1.4 0.6-2.7-0.3-1.7 0.1-1.8 5.2-1.1 5.2 0.1 5.6 2.1 3.7 3.3z"
                  id="TR27"
                  name="Gaziantep"
                ></path>
                <path
                  d="M557.8 335.8l3.1 3.1 3.8 1.7 0.8 0.8 0.4 1.2 0.6 0.5 0.8 0.1 0.3 0.5-0.5-0.1-0.4 0.1 0.2 1.4-0.6 1.2-0.7 1-0.6 1.2-0.2 1.3-0.1 1.1-0.1 1.2-0.6 1.4-0.6 2.5 0 2.6-0.2 2.6-1.4 2.2-0.3 0.9 0.2 1 0.9 3.2 0.2 0.5 0.4 0.3 0.7 0.3 0.2 0.2 0.3 0.4 0.1 0.4 0.1 0.4-0.1 0.5-0.3 0.9-0.2 0.5-0.2 0.1 0.4 0.4 1.6 0.2 0.8 0.3 0.5 0.6 0 0.1 0.5 0.8 0.3 1 0.2 0.9 0 1.3-0.2 0.5-3.4 0.6-0.9 0-3.6-0.6-0.9 0.2-0.5 0.2-0.2 0.3 0 0.5-0.3 0.3-0.4 0.2-0.2 0-0.3-0.1-1.3-0.2-0.7-0.2-0.6-0.4-0.6-0.7-0.5 0.5 0.7 0.8-0.1 0.5-0.4 0.4-0.3 0.5 0 0.8-0.2 8.5 0.1 1.7-1 0.3-1.9-0.7-0.9 0.4-0.4 2.1-0.8 0.1-1.1-0.3-1.5 0.6-1.1 1.7-0.3 4.1-0.5 1.7-0.1 0.1-0.8 0.1-2.8-2-1.4-0.7-1.5 0-0.7-0.2-0.5-0.5-0.1-0.7 0.1-0.7 0-0.8-0.6-0.7-0.9 0.2-1 0.3-0.9 0.2-0.5-0.1 0-0.1 0.6-0.8 2-3.6 0.5-0.4 0-1.1-1.3-4-3.4-5.6-0.6-0.8-0.6-1-0.5-1.8-0.7-0.8-0.8-0.7-0.5-0.7-0.6-1.4 0.1-0.9 1.3-1.9 0.4-0.5 0.5-0.3 0.5-0.2 0.5 0 0.3-0.2 0.5-1.5 0.4-0.4 0.8-0.4 0.5-0.4 0.2-0.5 0.3-1.3 0.5-0.3 0.3-0.3 0.3-0.3 1.9-0.7 0.4-0.2 2.2-2.8 0.4-0.6 0.5-0.1 5.1-3.2 0.7-0.2 0.5-0.5 0.6-1 0.5-1.2 0.2-0.9 0-0.8-0.2-0.6-0.3-0.5-0.2-0.6 0-0.4 0-3.7-0.1-1-0.5-1-2.8-4-1.6-1.6-1.8-1.3-1.8-0.5-1.7 0.7-1.6 1.4-0.9 1.1-0.7-1.2 0-2.5 3.9-4.5 0.9-0.5 1 0.2 1 0.4 2.1-0.4 2 0.6 1.9 0 2.3-0.6 2.1 0.7 1.6 1.7 1.8 1.1 2.4 0.1 2.4-0.3z"
                  id="TR31"
                  name="Hatay"
                ></path>
                <path
                  d="M163.8 57.8l0.1 0 2.2-3 1.4 1.3 7.2 4.6 10.3 4.4 14.6 7 4.7 1.1 0.2 0.1 0.1 0.1 0.2 0.2 0.3 0 0.3-0.1 0.3-0.6 0.4-0.1 1.1 0.1 1.2 0.5 0.7 0.7-0.4 1.6-0.7 1.1-0.8 1.1-0.8 0.5-0.6 0.2 0.2 0.4 0.4 0.5 0.2 0.5 0.3 0.4 0.1 0.1 0.3 0.2-0.1 0.4-0.2 0.3-0.1 0-0.2 1.2-0.3 1.2-0.5 0.9-0.5 0.8-0.4 0.3-0.8 0.3-0.4 0.3-0.4 0.6-0.1 0.3 0.1 0.3 0 0.2 0.2 0.3-0.1 0.3-0.2 0.4-0.2 0-1.1-0.1-0.4-0.3-0.2-0.1-0.3 0.2-0.4 0.6-0.2 0.1-0.5 0.1-0.9 0.6-1.9 0.4-0.4 0.2-0.3 0.2-0.2 0.3-0.3 0.2-0.5 0.1-1.2 0-0.6-0.2-0.2-0.4-0.5-0.5-1.2 0-5.7 0.7-1.4-0.6 0.4-2-0.6-0.6-0.3-0.2-0.4-0.1 0.3-1.4-0.5-1.3-1-0.8-1.1-0.3 0.2 0.9 1.1 1 0.3 1.1 0 0.6-0.1 0.9-0.3 0.9-0.6 0.6-1.3-0.2-2.5-2.5-1.2-0.7-1.6-0.3-3.3-1.2-1.6-0.2-1.6 0-0.4-0.2-0.5-0.6-0.4-0.1-1.8 0.4-0.9 0-0.3-0.2-0.5-3.5-0.7-4.8 0.7-3.8 1.4-5.2-0.5-4.2-1.3-4.5z m52.2 38.9l-0.6-0.4-1 0.2-0.9-0.9-2.7-1.3-1-0.8-1-1.2-0.9-0.8-2.7-1.5 0.2-0.1 0.1 0 0-0.1 0.1-0.2-0.6-0.3-0.2-0.6-0.2-0.9-0.3-0.8 0.8-0.5 1-1.1 0.8-1.3 0.6-2.4 0.6-0.9 0.1-0.8-1-0.7 1.1-1.7 1.8-2 1.9-1.4 1.4 0.3 0.3 0 0.5-0.7 0.7-0.1 8.9 1.7 9.5 2.2 12.6 1.9-1 0.5 0.5 1.9 0.2 2-1.4 3.2-2.5 1.4-3.1 1.2-2.7 1.9-0.5 1.5-0.8 1.5-1.7 0.2-1.4-1.2-1.4-3.1-2.3-1.9-2.5-0.2-2.1 1.5-2 2-1.1 0.6-2.5 2.1-1.6 2.1z"
                  id="TR34"
                  name="Istanbul"
                ></path>
                <path
                  d="M99.9 68.1l7.2-1.6 7.7-0.6 5.5 0 2.7 1.8 2.1 2.8 2.6 1.7 3.1-0.8 2.7-2.1 1.7-3.5 2.3-3.4 4.6-1.4 5.2-1.2 8.2-1.1 8.3-0.9 1.3 4.5 0.5 4.2-1.4 5.2-0.7 3.8 0.7 4.8 0.5 3.5-0.4-0.2-2.3 0.8-0.4 0.3-0.4 0-2.6 1-0.7 0.2-0.9 0.5-0.7 0.5-0.5 0.5-1.1 1.8-0.3 0.8 0.1 0.5-0.9 0.2-3.8-0.4-0.7-0.5-0.7-0.6-0.8-0.4-1.8-0.6-1.8-0.1-10.2 1.5-1.4 0.9-1.1 2.1-1.2 5-1 1.9-4.5 4.4-2 3.6-4.3 3.2-0.2 0.3-0.5 0.7-0.2 0.3-0.5 0.1-1.2 0.2-1.3 0.9-2.2-0.1-1 0.1-0.8 0.7-1.4 2-0.6 0.4-1.2 0.1-1.5-4.6 0-5-6.9-0.7-1.2-4.1-1.8-3.9-0.3-5.1 0.8-4.4 1.5-3.8 2.3-3 0.7-3.8-0.3-8.8-0.6-1.1z"
                  id="TR59"
                  name="Tekirdag"
                ></path>
                <path
                  d="M98.5 110.3l0-0.3 0.3-3.7 0-0.2 6.9 0.7 0 5 1.5 4.6-0.6 0.1-1.9 0.6-2.7 1.7-0.7 0.7-0.9 0.6-2.9 0.4-0.9 0.5-1.5 1.4-0.7 0.3-0.5 0.3-0.4 1.9-0.5 0.8-1.6 0.8-0.3 0.5-0.1 1-0.3 1-0.7 0.9-2.6 2.4-0.3 0.2-1.4 0.3-0.6 0.6-1 1-0.1 0.2-0.2 0.3-1.2 1.5-0.9 0.7-1.1 0.6-1.1 0.6-1 0.1-0.6 0.6 0.1 1.1 0.6 1.2 0.7 0.5-2.3 2.2-0.7 0.5-1.9 1.1-1.4 1.4-0.6 0.3-2.5 0.9-0.5 0.1-0.5-0.4 0.1-0.7 0.4-0.7 0.3-0.4 0.8-0.6 0.5-1.3 0.4-1.3 0.2-0.6 2.2-3 0.6-2.2-0.3-1.7-0.9-1.4-1.4-1.1 0.5-0.4 0.2-0.4-0.3-0.3-0.6-0.1-0.2-0.3 0.4-0.6 0.7-0.6 0.3-0.1 0.5-0.5 2.5-0.9 0.9 0 0.7-1.3 2.4-1.5 2.2-1.9 2.8-1 2.1-1.8 1.4-0.9 1.4-0.7 1.3-0.3 2.3 0.3 0.6-0.3 0.7-0.7 1-1.6 0.6-0.7 1.2 0.5 1.5-0.5 1.3-1.1 0.6-1.3-1.4-3.1-0.4-0.6-0.5 0.1z m-5.4 68.8l-10 1.9-1 0.4-0.5 0.5-0.5 0-2.1 1.2-0.4 0.3-0.5 0-2.5-0.5-0.5 0.1-0.7 0.3-0.5 0.1-0.4 0.5-0.1 0.2-0.1 0.3-0.3-0.2-0.5-0.4-3.8 1-1.9 0-1.8-1-0.2-1.3 1.2-3.2 0.7-3.1 1.8-1.7 0.4-1.4 0.3-3.4-0.2-0.7-0.9-1.7-0.2-0.8 0.1-0.6 0.2-0.2 0.2-0.1 0.2-0.4 0-1.4 0.2-1.7-0.2-0.5-0.7-0.2 0.3-0.7 0.7-3.1 0-0.3-0.3-1.3 0-0.5 0.1-0.6 0.4-0.8 0.1-0.5 0.2-0.6 1-1.7 0.4-0.5 2.6 0.9 0.7-0.5 1.6-0.7 0.5-0.3 2-2.1 0.4-0.7 0.2-0.5 0-0.8 0.2-0.4 0-0.2 0-0.3 0-0.2 0.1-0.1 0.6 0 0.2-0.1 0.2-0.1 0.3-0.8 0.2-1.2 0-1.1-0.3-1 0.3-0.9 0.2-0.4 0.3 0 0.7 0 0.6-0.1 1.2-0.6 1.6-0.5 0.9-0.7 2.1-2.8 0.4-0.3 0.5-0.1 0.6-0.4 0.6-0.5 5.1-6.2 1-0.8 1.9-0.1 4.1 0.3 1.7-0.4 0.5 0.2 1.2 0.7 2.7 0.2 1.1-0.2 1.3-1.1 1.2-1.5 1.4-1.2 2.2-0.1 1.6 0.4 1.9 0 1-0.2 1.6-1 0.8-0.1 0.7 0.7 0.5 0.9 0.7 1.9-0.6 0.4-0.3 0-0.3-0.3 0 0.1-0.4 0.2 2.1 2.3 2.6 1.7 2.8 1.3 3.4 0.8 1.3 0 0.5-0.1 0.5-0.3 0.2 0.5-0.5 2.8-2.3 1.3-3.3 3.5-0.8 0.3-0.5 0.4 0.9 1.9 1.9 0.8 0.8 0.1 0.2 0.4-0.3 1.2-0.6 2.1-0.2 1.6-0.5 1.4-0.4 0.5-0.5 1 0.9 1.4 0.8 0.6-0.3 2.5-0.8 2.6-1 2.3-1.9 1.4-2 0.9-1.9 1.3-2 0.8-3.5-1.1-3.5-0.6-1.3 0.7-1.5-0.2-2.2 0-2 1.6-3.5 1.5-1.7 1.5-2.1 0.6-4.3-0.5-1.9 0.4-1.5 4.9 0.1 4.9z m-28.8-17.7l0.8-0.4 0.3 0.6-0.1 2.2-0.3 0.9-0.7-0.1-1.6-1.1-1.4-0.6-0.6-0.5-0.3-0.8 0.4-0.5 0.8-0.1 1.7 0 0.3 0 0.3 0 0.2 0.1 0.2 0.3z m-2.3-20.4l0 1.3-1 0.6-1.1 0.4-3.2 0.5-2.1 0.9-4.3 0.6-0.9 0-1.2-0.4-0.6-0.3-0.2-0.4-1.9-0.7 0-0.3 1-1.8 1.6-1.6 1.9-1.3 3.2-0.6 4.1-1.5 0.6-0.1 0.6 0.1 0.3 0.3 0.3 0.4 0.3 0.4 0.7 0.1 0 0.5-0.1 0.5-0.1 0.8 0 0.8 0.2 0.4-0.4 1 0.5 0.3 0.8-0.3 0.7-0.6 0.3 0z"
                  id="TR17"
                  name="Çanakkale"
                ></path>
                <path
                  d="M783.2 69.9l0.1 0.2 1.3 3.3 0.4 2.5 0.9 2.1 0.9 0.9 0.9 0.6 0.6 1.1 0.5 1.1 1.9 0.9 1.4 1.6-1.3 2.9-2.6 1.7-0.7 1.3-0.4 1.3-1 0.9-1.1 0.6-4.5 5.3-0.7 2.5-3.6 2.1-5.1 4.4-2 0.7-2.1 0-1.7 0.7-1.4 3.6-2.9 0.8-1.9 0.2-1.9 0.6-7.7 3.8-0.7-3.9 0.6-4.3-3-7.6-0.5-4.4-1.2-4.2-1.7-3.9 5.3-3.6 1.7 0.8 1.4 0.1 1.3-0.4 1.5-0.9 1.8-1.6 0.6-0.1 1.5-0.2 0.7-0.3 0.6-0.4 2.8-3.8 3.4-2.5 0.5-0.2 0.7 0.2 2.1 0.7 1.2-0.2 1.8-1.3 0.7-0.3 1-0.1 1.1-0.4 1.1-0.5 0.9-0.7 1.8-2 1-0.7 1.2-0.3 1.3 0 0.8-0.3 0.4-0.4z"
                  id="TR53"
                  name="Rize"
                ></path>
                <path
                  d="M743 89.3l1.7 3.9 1.2 4.2 0.5 4.4 3 7.6-0.6 4.3 0.7 3.9-0.9 0.2-1.9 0.6-4.9 0-2.4 0.4-2.3 0.8-2.1 0-3.9-1.3-4.1-0.1-1.9-1.1-2.9-3.3-1.5-0.5-0.3 0.2-0.6 0.6 0.3 1.5-0.7 0.8-1 0.1-2.3-3.9-3.3-2.2-1.4 1.1-0.6 2.6-0.7 0.8-1 0.2-2.8-0.3-11.1-4.5-3.2-3.3-0.7-3.7-2-1.2-0.8 1.1-1 0.9-1-0.1-0.9-0.7-0.8-2.4-0.4-5.8 0.1-3.1 1.3-7.6 0.4-0.4 0.6-0.1 1.5 0.1 0.9 0.3 1.5 1.3 1.1 0.1 0.8-0.2 1.2-0.9 1-0.1 0.6-0.2 2.5-1.6 0.3-0.2 0.2-0.3 0.2-0.2 0.6-0.2 3 0.8 0.7 0.6 1.2 1.7 2.8 2.4 1.8 1.1 1.9 0.4 0.9-0.4 1-0.4 1-0.2 1.2 1 0.6 0 1 0 0.4 0.1 2.9 2.3 0.8 0.4 0.9 0.2 0.8-0.6 3-1 1.1-0.2 2.8 2.6 1.3 0.9 2 0.4 0.8-0.1 1.4-0.6 1.1-0.3 1.7-0.6 1.1-0.8 2.2-0.6 2.4-1.6z"
                  id="TR61"
                  name="Trabzon"
                ></path>
                <path
                  d="M685.8 84.4l-1.3 7.6-0.1 3.1 0.4 5.8 0.8 2.4-5.7 0.7-0.3 0.7-0.2 0.9-0.9 0.5-1.2 0.3-1.4 1.2-1.5 1-0.6 1.6 0.6 1.9-0.9 1.7-1.4 1.5-0.5 0.7-0.1 0.5-0.3 2.8 0.1 2.2 1.3 1.6 5.6 1.4 1.3 1.1 1.3 1.3 1.1 0.3-0.3 2.3-1.4 1.2-4.8 2.6-1 1.3-0.1 2.1 0.2 2.1-0.2 2.2 0.4 2.2 1.3 1.7-0.4 2-1.9 0.9-4.2 0.3-2-0.1-4.8-2.4-5-1.6-4.8 1.2-3-1.8-2.9-2.2-2.7-1.4-1.7-2.6 0.1-3.5-3.9-14.9-4.5-5.1 0.2-1.7 0.6-1.2 0.2-2.1-0.2-2.1 0.2-2.1-0.2-2.1-0.8-1.8 0-2 0.8-1.5 1-1.2 0.7-1.9-0.1-2.2 0-0.2 2.3 0.3 1.9 0.7 1 0 2.1-0.6 0.9 0.2 2.1 1.9 1 0 1.8-0.6 1.8 0.5 1.2 0.2 0.5-0.5 3.6-0.2 0.7-0.5 1.8-1.7 0.6-0.4 1.5-0.5 0.6 0.1 0.3 0.2 0.8 1.1 0.3 0.4 0.5-0.3 0.9 0 0.5-0.1 0.3-0.3 0.4-0.4 0.5-0.4 0.6-0.2 0.9-1.2 0.2-0.3 0.2-0.2 1.1-0.4 3.3-0.7 0.8-0.5 0.7-0.7 0.9-0.3 5.5 0 3.8-0.9 0.4-0.3 0.8-0.6z"
                  id="TR28"
                  name="Giresun"
                ></path>
                <path
                  d="M636.7 91.6l0 0.2 0.1 2.2-0.7 1.9-1 1.2-0.8 1.5 0 2 0.8 1.8 0.2 2.1-0.2 2.1 0.2 2.1-0.2 2.1-0.6 1.2-0.2 1.7 4.5 5.1-1.4 1.2-1.6 0.8-2.2 0.2-1.9 0.4-0.5 0.8-0.1 1.8 0.5 0.7-0.2 1.1-1 0.6-4-0.2-1.7 1.3-1.4 1.9-1.4 1.1-1.7 0.2-3.6-2.9-2.6-5.1-0.9-3.8-1.6-3-6.4-0.4-10.9-7.6-3.1-0.3-3.3 0.5-3.4-0.3-3.1-1.3-2.5-1.5-3.9-1.4-2.7-1.6-2.1-0.5-0.6-0.4-0.5-1.9 0.8-1.6 1-0.3 0.9-0.5 2.1-0.2 1.9-0.6 1.7-1 0.7-0.6 1.9-2.1 3.8-2.1 2-3.9 2.7-2.8 2.5-3.5 0-0.3 5.3-0.1 1.7 0.5 1.1 1 0.5 0.3 1 0.3 1.2 0.1 0.8 0.3 1.1 1.3 0.8 0.6 2 0.7 0.2 0.3 1.1 1.2 0.9 0.4 1.9-0.2 0.8 0.2 0.3-0.3 0.6-0.3 0.4-0.3 0.3-0.5 0.6-1.6 0.6-1.1 0.7-1 0.8-0.8 0.9-0.6 0.4 1 0.8 0.6 0.9 0 0.8-0.7 0.3 0.4 1.2-0.2 0.1 3.9 0.8 1.1 2.5 1.2 0.6 0.5 0.5 0.7 0.8 1.4 1.9-0.8 2.2 0.3 5.8 2 0.5 0.1z"
                  id="TR52"
                  name="Ordu"
                ></path>
                <path
                  d="M591 79.7l0 0.3-2.5 3.5-2.7 2.8-2 3.9-3.8 2.1-1.9 2.1-0.7 0.6-1.7 1-1.9 0.6-2.1 0.2-0.9 0.5-1 0.3-0.8-1.6-1.7-1.1-2.5-0.1-0.3-0.2-0.3 0.4-1.6 0.8-5.2 1.3-5.1-3.7-0.8-1.4-0.6-1.4-1.3-1.5-1.7-0.6-1.9 1.2-0.9 2.9-1.4 2.5-2.2 1.5-1.7 1.4-2 0.5-3.7-1.8-6.3-0.1-1.8-0.3-3.6 0.2-1.6-0.4-1.6-2.8-6.9-5.3-1.6-0.9-2.9 0.5-14.7-4.2-1.8-0.4-3.7 0.4-0.7-2.3-0.3-2.5-0.6-2.3-2.5-4.4 0.8-0.6 1.1-0.5 0.7-0.8-0.4-1.1 0.5-0.5 0.4-0.3 1.2-0.1 0.6-0.2 1.6-1.5 2.5-1 1.2-0.7 0.5-0.9 0.5 0.5 0.4 0.9 0.3 1.2 0.1 1.1 0.3 0.6 0.8 0.5 1.5 0.8-0.5 0.8 0.5 0.3 2.1 0.3 0.4 0.2 0.8 0.9 1.5 0.8 2.1 0.3 2-0.4 1.5-1 0.9-0.4 2.5 0.6 0.9-0.7-0.4-3.8-0.3-1.8-0.6-1.3-0.3-1.5 0.2-1.9-0.1-1.8-0.8-3.1 0.2-3.2 1.9-4.2 1.4 0.4 2.2 0.3 2.1-0.2 1-0.4 1.8-1.1 2.9-0.5 5-2.2 1.2-0.2 1.1-0.3 1.9-1.6 1.1-0.3 0 0.5-0.2 0.4-0.3 0.3-0.4 0.4-0.5 0.2 0.1 0.9-0.9 2 0.2 1.1 0.4 0 0.1-1.6 0.4-1.4 0.6-1.1 0.7-0.8 0.5-0.4 0.3-0.1 2.8 2.7 0-0.9 0.4 0 3.5 4.7 0.7 1.9 0 2.5-0.2 0.7-0.4 1 0 0.3-0.1 2.1 0.2 1 0.5 1.1 2.1 2.6 1.2 0.9 1.6 3 0.7 0.6 2.4 1.3 1.4 0.3 0.2 0.3 0.2 0.5 0 0.7 0.1 0.7 0.4 0.3 0.6 0.2 0.5 0.3 0.3 0.3 0.4 0.7 0.3 0.3 0.5 0.1 0.8 0.2 0.5 0.3 0.8 0.4 1.2-0.3 2-1.1 2.5-2.3 0.4-0.6 0.2-0.6 0.7-1.1 0.1-0.4 0.1-0.2 0.3-1.3-0.1-0.1 0.7-0.5 0.6 0 0.8 0.3 1 0.2 4.1 0 3.8 1 7 3.5 1.4 1.3 0.8 2 0.1 2.8 0.3 1.1 0.8 0.4 1 0.3 1.8 1.1 0.9 0.4 0.8 0z"
                  id="TR55"
                  name="Samsun"
                ></path>
                <path
                  d="M512.6 48.4l-1.9 4.2-0.2 3.2 0.8 3.1 0.1 1.8-0.2 1.9 0.3 1.5 0.6 1.3 0.3 1.8 0.4 3.8-0.9 0.7-2.5-0.6-0.9 0.4-1.5 1-2 0.4-2.1-0.3-1.5-0.8-0.8-0.9-0.4-0.2-2.1-0.3-0.5-0.3 0.5-0.8-1.5-0.8-0.8-0.5-0.3-0.6-0.1-1.1-0.3-1.2-0.4-0.9-0.5-0.5-0.5 0.9-1.2 0.7-2.5 1-1.6 1.5-0.6 0.2-1.2 0.1-0.4 0.3-0.5 0.5 0.4 1.1-0.7 0.8-1.1 0.5-0.8 0.6-0.1 0.4-0.1 1.1-0.1 0.5-0.2 0.3-0.9 0.8-1 1.2-5.4-1.7-0.8-0.6-0.5-0.9-1.2-0.9-1.4-0.3-1.6 0.4-1.6-0.3-0.8-0.9-1-0.2-1.8 0.1-1.2-1.6 0.3-8.3-0.5-1.2-0.3-1.2 0.5-2.4 0.9-2.2 1.4-1.4 1.5-1 1.7-4.3-2.7-3.5-3.1-0.9-5.8-0.3-2.9-0.8-2-0.3-1.7 0.1-3.1-1.2 0.1-2.1 2.4-2.9 1.3-3.7-0.2-3.6 5.5 0.7 5.4-1.6 1.8-0.1 0.7 0.2 1.4 0.7 1.6 0.6 6.3 0.4 2.5-0.3 2.5-0.8 0.8-0.4 1.7-1.4 1.8-1.1 1.4-1.8 1.1-2.1 0.3-1.9 0.6 0.2 1.4 0.3 0.6 0.4 0.1-0.4 0.2-0.1 0.3 0 0.4 0 0.3 0.8 0.5 0.5 0.4 0.4-0.3 0.5 0 0.4 1.8 1.4 1.1 0.5 1.4-0.1 0.5-0.3 0.8-0.8 0.5-0.2 0.7 0.1 0.4 0.3 1 0.9 0 0.4-2.5-0.3-1.2 0.2-1 0.8-0.5 0.9-0.4 1.4-0.2 1.5-0.1 1.2 0.3 1 1.3 2.4 1.4 1.9 2.6 2.1 0 0.5-0.7 0 0.3 0.8 0.5 1 0.6 0.9 1.4 0.7 1.4 1.7 0.7 0.6 2.8 1.1 2.1 0.4 0.8 0.5 1.5 1.4 1.7 1 0.7 0.3z"
                  id="TR57"
                  name="Sinop"
                ></path>
                <path
                  d="M451.6 28.1l0.2 3.6-1.3 3.7-2.4 2.9-0.1 2.1 3.1 1.2 1.7-0.1 2 0.3 2.9 0.8 5.8 0.3 3.1 0.9 2.7 3.5-1.7 4.3-1.5 1-1.4 1.4-0.9 2.2-0.5 2.4 0.3 1.2 0.5 1.2-0.3 8.3-2.5 0.6-1.7 2.4-1.3 3.4-1.1 1.3-2.2 2-1.7 4.2-1.6 2-0.5 1.2 0.9 2.4 1 0.4 1.6 1.7-1.2 3.1-6.7 2.8-4.2 0.9-1.9-0.1-2-0.4-1.6-1.2-1.5-1.4-2.8-1-1.7-3 0.5-1.9 1.2-1.4 1.5-2-0.5-2.5-0.7-0.4-1.6 0.6-7.7 4.1-3.7 0.7-5 3-1.8 0.6-2.2-0.7-1.8-2.1-4.3-2.9-6.1-0.6-0.7-0.6-1.1-1.4-0.4-0.8-1-1.5-2.5-1.1-2.7-0.4-0.9-6.8 0.4-2.1 2.2-3 2.5-2.5 4.6-0.9-1.3-3.1-1.3-5.6-3.5-2.6-3.5 1.2-2.9 0.4-1.6-1.8-0.4-4.5 3-2.2-1.7-4.7-1.3-2.8-0.5-3.8 1.4-0.4 0.8-0.5 0.5 0.3 3.4-0.8 1.7-0.8 2.1-1.5 7.5-2.5 7.6-4 1.7-0.1 3.5 0.6 5 0 1.1 0.2 2.4 0.9 10.1 1 0.9-0.3 1.3 0.5 6.2-0.5 10.1 1.5z"
                  id="TR37"
                  name="Kastamonu"
                ></path>
                <path
                  d="M384.3 34.5l0.5 3.8 1.3 2.8 1.7 4.7-3 2.2 0.4 4.5-4.5 4.3-2.7 0.8-2.1 4.6-5.7 4.8-5 1.3-5.2-0.2 1-5.4-1.8-4.4-0.9-3.3-2.9-1.3-2.7-2.7 1.4-0.8 0.7-1.6 3.1-3.5 0.3-0.3 0.7-1.4 1.6-0.5 2.6-0.1 0.1-0.4 0.4-0.2 0.5-0.3 0-0.4 0.2-0.3 0.2-0.1 0.3 0.1 0.4 0.6 0.2 0.1 0.8-0.4 1-1.2 0.7-0.6 3.3-1.7 0.6-0.4 0.6-0.3 0.7-0.1 0.7 0.3 0.6-0.9 1.1-0.8 1.2-0.3 1.2 0.5 0.7 0 2.9-1.1 0.7-0.4 0.7 0.1 1.4-0.1z"
                  id="TR74"
                  name="Bartın"
                ></path>
                <path
                  d="M352.7 51l2.7 2.7 2.9 1.3 0.9 3.3 1.8 4.4-1 5.4-2.5 3.5-1.7 2.1-0.8 3.3-1.5 5.1 0 5.6-10.2-0.3-3-0.8-3.5-0.3-11.5-1.1-8.2-4-1.8-1.8 1.6-1.5 0.6-0.9 0.4-0.8 0.2-1 0.1-1.4 0.1-0.5 0.4-1.3 0.1-0.8-0.2-0.3-0.9-0.6-0.2-0.5 0.5-0.8 1.1-0.4 2.2-0.2 0.2-0.2 0.8-1.2 0.2-0.3 0.5-0.1 7.2-2.8 10.1-6.8 5.8-2.8 1.7-1.4 0.4-0.3 4.5-1.5z"
                  id="TR67"
                  name="Zinguldak"
                ></path>
                <path
                  d="M315.3 79.4l1.8 1.8 8.2 4 11.5 1.1-0.8 5.5-2.3 2.7-4.4 1.1-6.1 1-2.3 1.8-0.2 3.3-1.8 4.5-3.6 2.8-4.6 0.5-5.4-2.3-7.7 0.3-3.9-1.4 1-2-0.9-1.4-0.5-1.7 0.7-1.7-0.2-1.9-0.8-1.7-0.3-1.9 1.6-3.7 2.8-2.6-0.1-1.3-0.3-1.9 0.1-0.6 6.4-0.1 1.2-0.7 0.4-0.2 0.6 0.1 1 0.3 0.5 0 2-0.4 0.3 0.1 0.4 0.3 0.3 0 0.3-0.1 0.1-0.2 0.1-0.3 0.3-0.2 2-0.8 1.5-1 1.1-1.1z"
                  id="TR81"
                  name="Düzce"
                ></path>
                <path
                  d="M296.8 83.7l-0.1 0.6 0.3 1.9 0.1 1.3-2.8 2.6-1.6 3.7 0.3 1.9 0.8 1.7 0.2 1.9-0.7 1.7 0.5 1.7 0.9 1.4-1 2-3 2.3 0.1 2.1 0.4 1.9-0.6 1.9-3.1 1.5-3.3-0.2-1.9 2.9-1.3 3.9-0.8 0.4-0.6 0.7 0.3 1.6 1 1.2 1.1 2.2-0.9 2.3-2.5-0.6-3.5 0.1-0.8-0.7-1.2-1.8-1.5-0.9-1.8 0-1.7-0.5-2.1 0.2-6.6 1.7-4.2-1.3-1.7-1.9-1.9-1.6-1.7-2.5-1.3-2.8-0.5-1.3-0.2-1.5 3.3-0.3 1.5-0.5 1.5-0.9 3.1-0.7 2.8-1.9 1.9-3.9 1.9-9.3 3.1-3.3-0.6-0.4-0.6-0.2-0.4-1.4 0.9-0.5 1.8 0.4 1.6 0.7 2.6-0.1 0.9-0.1 0.3-2.2 1.1-1.9-0.3-2.1-3.4-0.6-0.9-3.2 1.5-3.9 0.3-0.9 12.8 3.1 1.6 1.4 0.7 0.4 4.1 0.8 5.8-0.2z"
                  id="TR54"
                  name="Sakarya"
                ></path>
                <path
                  d="M216 96.7l1.6-2.1 2.5-2.1 1.1-0.6 2-2 2.1-1.5 2.5 0.2 2.3 1.9 1.4 3.1 1.4 1.2 1.7-0.2 0.8-1.5 0.5-1.5 2.7-1.9 3.1-1.2 2.5-1.4 1.4-3.2-0.2-2-0.5-1.9 1-0.5 12 0.4 1.3-0.2 0.7-0.3 1.2-0.8 0.6-0.2 0.4-0.4 0.4-0.7 0.3-0.5 0.4 0.3 0.5-0.5 0.6-1 0.5-0.3 0.6 0.5 2 0.5 1.6 1.2 2.8 0.7-0.3 0.9-1.5 3.9 0.9 3.2 3.4 0.6 0.3 2.1-1.1 1.9-0.3 2.2-0.9 0.1-2.6 0.1-1.6-0.7-1.8-0.4-0.9 0.5 0.4 1.4 0.6 0.2 0.6 0.4-3.1 3.3-1.9 9.3-1.9 3.9-2.8 1.9-3.1 0.7-1.5 0.9-1.5 0.5-3.3 0.3-6.5-2.3-3.3 0.6-8.8 3.3-2.7 0.2-2.7-0.9 0.4-2.4 0-5.6 0.4-0.3 0.6-0.2 0.5-0.3 0.2-0.5 0.2-0.4 0.5-0.4 0.5-0.2 0.5-0.1 0.5 0.1 0.8 0.4 0.7 0.7 0.3 1.2 0.5 0 9.2-2.1 5.4-0.3 1.7 0.8 1 0 0.7-1.1-0.5-1.5-1.8-0.3-3.9 0.5-0.3 0.1-0.3 0.1-0.4 0.2-0.6 0-0.5-0.3-1-0.8-0.3-0.2-0.7-0.2-1.7-0.6-1-0.1-11.2 0.5-0.9 0.3-0.8 0.6-0.8 0.3-0.9-0.4-0.4-0.5-0.2-0.6-0.3-0.4-0.5-0.2 0.2-0.1 0.1-0.2 0.2-0.1 0.2-0.1-0.2-0.6-0.3-0.4-0.3-0.4-0.5-0.2-0.2 0.3-0.5-0.3-0.4 0.3-0.5 0.2-1.3 0.3 0-0.4 0.5 0 0.3-0.3 0.1-0.4 0-0.6 0.5 0.3 0.2 0.1 0-0.2 0-0.2 0.1 0 0.2 0-0.5-0.9-1-1.1-0.4-0.4z"
                  id="TR41"
                  name="Kocaeli"
                ></path>
                <path
                  d="M224.3 108.3l0 5.6-0.4 2.4-3-0.6-3 3.2-3 0.6-7.3-3.2-1.4 0.2-3.3-0.8 0.1 6.8-0.5 0.2-2.5-0.6-4.2-2.7-2.6-0.6 0.9-1.9 1.6-1.1 3.4-1.3 1.4-0.9 2-2.1 0.8-0.3 14-1.3 2-0.7 1.9-1.1 1-0.8 0.3 0 0.4 0.3 0.3 0.6 0.5 0.4 0.6-0.3z"
                  id="TR77"
                  name="Yalova"
                ></path>
                <path
                  d="M247.9 115.4l0.2 1.5 0.5 1.3-2.5 1.4-1.1 0.8-0.9 1-0.9 1.7-0.2 2-0.5 1.7-0.6 1.5 0.2 1.9 1.1 1.4 0.2 1.7-0.8 1.7-4.6 4.9-1.3 6.2 0 3.1 2.5 3.6 1.3 1 0.6 3.1-2.3 0.8-2.7-0.5-1.5 0.1-1.3 0.7-1.5 0.2-1.6-0.1-4.9-2-2.4 1.7-1 3.7-0.5 1.4-1 1-1.3 5-2.2 1.6-1 2.8-0.3 4.1-2.6 1.6-3.4 0.1-3.4-0.4-2.1-0.5-1.8-1.4-1.1-1.3-1.5-0.6-2.2-0.1-2.2 0.4-1.8-0.3-1.2-1.8-0.7-0.5-0.9 0-0.5-0.4-0.3-0.7-0.9-1.5-0.1-1.7 0.2-1.3-0.7-1-1.3 0.5-1 1.1-3.8-0.5-12.5-9.3-0.9-1.9-0.8-4.3-0.9-1.6-2.6-1.8-1.7-2.8-0.9-2.3-0.2-1.2 0.6-2.4 0.6-1 0.5-1.2-0.9-2-1.1-0.3-0.4-2.1 1.3-1.3 1.4-1.1 2.3-3.5 0.2-0.5 6.6-0.2 2.5 0.4 4.1-0.2-0.5 0.1-0.4 0.3-0.3 0.3-0.1 0.6 1.9-0.7 2.2-0.1 2.3 0.6 2.1 1.1 0.3-0.4 0.2 0.1 0.1 0.2 0.4 0.1 0.4-0.2 0.5-0.5 0.4-0.2 2.7-0.5 0.7-0.6 0.7-0.4 0.9 0.2 1.6 0.5 1.5 0.1 1.3 0.3 0.7 0.3 1.1 0.7 0.8 0.3 0.8 0.1 3.7-0.1 0.9-0.2 0.6-0.5 0.4-0.8 0.4-1.7 0.4-0.7 0.6 0.3 0.8-0.1 0.8-0.2 0.7-0.4-0.8-1.1-1.1-0.9-1.1-0.7-1.1-0.3-2.6 0-0.4 0.2-1 0.9-0.1-6.8 3.3 0.8 1.4-0.2 7.3 3.2 3-0.6 3-3.2 3 0.6 2.7 0.9 2.7-0.2 8.8-3.3 3.3-0.6 6.5 2.3z"
                  id="TR16"
                  name="Bursa"
                ></path>
                <path
                  d="M93.1 179.1l-0.1-4.9 1.5-4.9 1.9-0.4 4.3 0.5 2.1-0.6 1.7-1.5 3.5-1.5 2-1.6 2.2 0 1.5 0.2 1.3-0.7 3.5 0.6 3.5 1.1 2-0.8 1.9-1.3 2-0.9 1.9-1.4 1-2.3 0.8-2.6 0.3-2.5-0.8-0.6-0.9-1.4 0.5-1 0.4-0.5 0.5-1.4 0.2-1.6 0.6-2.1 0.3-1.2-0.2-0.4-0.8-0.1-1.9-0.8-0.9-1.9 0.5-0.4 0.8-0.3 3.3-3.5 2.3-1.3 0.5-2.8-0.2-0.5 1.2-0.8 0.7-0.2 1.4 0.3 1.1 0.5 1.1 0.3 1.2-0.7 0.9 0.5 1.5 0.1 1.2-0.3 1.2-1.7 2.5-1.4 0.8-1-0.9-0.1-2.4-0.8-0.7 0.4-0.4 0-0.2-0.6-0.1-0.6-0.2-0.2-0.3 0-0.3-0.3-0.3-0.5-0.2-0.2-0.2-0.4 0-0.8-0.1-0.4-0.4-0.1-0.4 0-0.4-0.1-1.5-1.6-0.5-0.6 0-0.4 0.6-0.2 0.5-0.4 0.7-0.9 0.5-0.2 0.5 0.2 0.4 0.1 0.4-0.8 0.9 1.2 0.1 0.2 0.4 0.1 0.2-0.2 0.1-0.2 0.4-0.2 7.9 1.3 2.1 0.7 1 1.1-0.9 1.4-4.1 2.1-1.2 1.1 0.2 1.6 1.7 0.5 3.9-0.4 4-1.5 4.6-0.2-0.2 0.5-2.3 3.5-1.4 1.1-1.3 1.3 0.4 2.1 1.1 0.3 0.9 2-0.5 1.2-0.6 1-0.6 2.4 0.2 1.2 0.9 2.3 1.7 2.8 2.6 1.8 0.9 1.6 0.8 4.3 0.9 1.9 12.5 9.3 3.8 0.5 1-1.1 1.3-0.5 0.7 1-0.2 1.3 0.1 1.7 0.9 1.5 0.3 0.7 0.5 0.4 0.9 0 0.7 0.5 1.2 1.8 1.8 0.3 2.2-0.4 2.2 0.1 1.5 0.6 1.1 1.3-0.9 1.8-0.3 1.2 0 1.2-0.3 0.7-0.6 0.6-0.6 1.8-0.6 4.3-1.1 1.8-1.1 1.4-0.4 0.9-1.4 2.6-1.6 1.1-3.5 1.2-1.8 0.9-0.4 2.1 0.2 0.8 0.2 1.5-0.4 0.6-4.1-0.6-4.4 2.2-1.8 0.5-1.8 0.2-2-0.2-1.9 0.4-1.3 1.7-1.8 1.1-2.1 0.4-2 0.8-2.1-0.7-2.5-5.5-3.9-4.3-0.3-0.9-0.8-0.4-3.5 1.3-1.9-0.9 0.6-2.1 1.4-2.6-1.2-1.9-0.8 0-1.5 0.3-0.7-0.1-1.9-0.9-2-0.2-3.3 0.5-3.3-1-2.1-1.7-2.2-1-3.6 1.9-1.4-2.7-2.1-1.9-2.6-0.6-8.7 5.6-1.5 0.6-1.3 0.8-0.9 1.3-0.8 1.4-1 1-10.2 7.4-0.3-0.2-1-0.7-0.6-0.8 0-0.6-0.3-1.2-0.3-1.2-0.3-1-0.8-0.4-2.3 0.2-1.2-0.1-0.9-0.4 0.5-0.5 0.2-1.3 0.4-0.6 0.6-0.2 0.4 0.2 0 0.4-0.9 0.3 0 0.3 0.6 0 0.4 0.2 0.4 0.3 0.3 0.4-0.3-0.9 0.3-0.5 0.5-0.3 0.5-0.4 0.8-1.2 5-3.9-0.4-0.3-0.2-0.1 0-0.4 0.1-0.4 0-0.3-0.1-0.6 1.7-0.1 0.6-0.2 0.6-0.5-0.5-0.9 0.8-0.6 2.7-0.9 0.2-0.1 0-0.2 0.1-0.7 0.6-2.3 0-1.3-0.3-0.8-0.8-0.5-1.3-0.1-1.1 0.4-0.9 0.9-0.9 0.4-1.2-0.9-6.7 1.2z m47.1-68.4l0.7-0.2 1.4 0.2 1.2 0.6 0.2 1.1-0.9 0.7-1.7 0.3-0.9 0.3-1.6 1.4-0.9 0.4-1.1-0.5-0.5 0-1-0.4-0.7-0.7 0.1-1.3-0.3-0.5-0.3-0.5 0.2-0.7 0.4-0.4 0.4-0.3 0.2 0.2 0.2 0.7 1.1-0.6 1.3-0.4 1.2-0.1 1.3 0.7z m-0.6 12.1l0.3-0.1 0.2 0.2 0 0.5-0.5 0.1-0.7-0.4-0.8-0.1-0.8-0.1-0.7-0.5-0.1-0.7 0.5-0.4 0.4-0.8 0.4-0.6 1.5 0.7 0.1 0.5-0.2 0.5-0.1 0.7 0.5 0.5z"
                  id="TR10"
                  name="Balikesir"
                ></path>
                <path
                  d="M98.4 202.5l10.2-7.4 1-1 0.8-1.4 0.9-1.3 1.3-0.8 1.5-0.6 8.7-5.6 2.6 0.6 2.1 1.9 1.4 2.7 1.8 4.7 1.6 7.4 0.8 2.2 1.7 3.3-0.3 3.2-1.3 1.5-3.1 2.6-3 1.2-2.6 1.8-0.6 2.2-0.3 2.4-0.8 2-1.5 1.5-1.3 2.2-0.2 1.3 1.3 1.3 0.7 0.3 1.1 1.4 1.3 4.2 2.8 2.5 3.3 0.3 5.4-0.2 0.9 0.2 1.5 1.4 0.8 3.4 3.3 2.3 3.7 1.7 0.4 0.9 0.5 1.9 0.4 0.9 2 1.9 2.4-0.6 1.1-2 0.8-1.9 2.3 0.1 2.3 1 1.4 0.1 1.4-0.4 2 2.1 1.3 3 4.6-0.7 1.8-0.6 1.3 0.2 0.9 1.1 1.7 0.5 1.1 0.1 2.1 0.6 1.3 1.7 2 6.2 2.1 3.2-7.5 2.7-1.9 0.3-1.9 0.6-1.6 0.9-1.7 0.7-9.2 0.1-11 3-11.7-1-2.4 0.4-5.8 3.9-3.8 1.4-3.1 0.1-0.1 0 0-0.3 0.1-0.5 0-0.5-0.2-0.5 0.7-0.6-0.4-1.4-1.3-2.2-1.2 0.4-1.7 0-1.8-0.3-1.1-0.4-0.3-0.4-0.2-0.4-0.2-0.4-0.5-0.1-0.4 0-0.4-0.1-0.2-0.2-0.1-0.3-0.2-1-0.4-0.3-1.4-0.2-2.4-0.9-1.3-0.2-1.5 0.3-0.8 0.4-0.3 0-0.3 0.2-0.1 0.4-0.2 0.5-0.4 0.2-0.4-0.3-1.1-2.2 0.3-0.8-0.1-0.8-0.2-0.9-0.3-0.8-0.5-0.8-1-1.1-0.5-0.6-0.8 0.4-0.2-0.7 0.1-1.8-0.7-0.6-0.7 0.1-0.8 0.3-0.9 0.2-3 0-0.5 0.2-0.6 3.9-0.2-0.1 0-0.1-0.1-0.1-0.3 0 0.1 0.8 0 0.8-0.2 0.6-0.6 0.2-0.3-0.2-0.6-1.5-0.4-0.7 0 1.2-0.5 0.1-0.7-0.5-0.9-1-0.3-0.5-0.1-0.6 0.2-0.8-1.3 0.3-1.2-1-1.2-1.3-1.1-0.5 0.2 0.9 0.1 0.3-0.3 0-0.3-0.4-1-1 0.1-0.6 0.1-0.6 0-0.5-0.2-0.6-0.4 0-0.3 1.6-1 0.4-1.2-0.4-1.6-1.3-0.4-0.3-2.5-0.7-0.1-0.4 0.3-1 1.2 0.6 0.6-0.8 0.4-1.1 1.1-0.3 0-0.5-0.9-0.6-0.2-1.1 0.4-0.6 0.7 0.7 0-0.6 0.2-0.2 0.1-0.2 0.3-0.2 0.2 0.7 0 0.7-0.1 0.5-0.4 0.6 0.2 0.1 0.3 0.2 0.2 0 0.4 0 0.4 0.1 0.1 0.3 0.1 0.3 0.3 0.2 0.6 0 0.7-0.1 0.4-0.4-0.5-0.7 0-0.5 0.8 0.5 0.3 0 0.6 0 0.5-0.3 1.2-0.8 0.4-0.2 0.6-0.6 0.1-1.2-0.4-1.2-0.8-0.7 0-0.5 1.2 0.6 0.6 0.6 0.4 0 0.7-1.6-0.7-0.4-1.1-1.1-1-0.3-0.6-0.6-0.5-0.1-0.4 0.3-0.4 0.8-0.3 0.2-0.9-0.1 0-0.2 0.4-0.6 0.3-1.2-0.2-0.7-1.4-2.2 0.3 0.4-0.1-0.7-0.6-1.8-0.1-0.4 0-0.4-0.5-2.3 0-0.9 0.1-0.9 0.3-0.7 0.4-0.2 0.7-0.2 1.1-0.5 0.5-0.1 1.3 0.1 1.2 0.3 1 0.5 0.9 0.7 1.6 1.8 0.7 1.1 0.3 0.9 1.1 1.8 1.2 1.5 0.4 0.4 0.3 0.3 0.3 0.3 0.2 0.7 0.1 0.6 0 0.6 0 0.6 0.3 0.6-0.3 0.3-0.2 0.1-0.2 0-0.3-0.3-0.5-0.1-0.6 0.1-0.5 0.3 0.8 0.5 0 0.5-0.4 0.7-0.4 0.9 1.1 0.1 0.5 0.7 0.3 0.9 0.5 1 0.8 2.7 0.8 1.3 1-0.3 0.3-0.8-0.3-0.5-0.8-0.6-0.3-2.5 0.2-1 0.6-1.1 0.7-0.6 0.8 0.2 0-0.4 0.3 0 0.5 0.6 0.6 1.1 0.9 2.5 0.2-0.1 0.5-0.2 0.2-0.1 0.4 0.7 0.7-0.1 0.8-0.4 0.9-0.2 3.8-0.4 2.3-1.1 0.7-0.2 0.3-0.1 0.6-0.6 0.3-0.1 0.5 0 0.4 0.2 0.5 0.1 0.5-0.3 0.1 0.2 0.2-0.2 1.3 0.6 1.1-0.7 1.2-1.2 1.3-0.8 0-0.4-0.4-0.4-0.2 0.1-0.4 0.3-0.3-0.5-0.9 0.5-1.6-0.2-0.4 0.2-1.2-0.6-1.3 0-3 1.3-1 0.5-0.1-0.1 0.4-0.7-0.8-0.6-1.4-2.3-0.7-0.4-1.2-0.4-0.4-0.3-1.1-0.8-0.6-1 0.7 0.7 0.9 0.6 1.3 0.8 0.7 0 0-0.4-0.6-0.7-0.6-0.6-0.6-0.4-0.8-0.4 0.2-0.7 0.3-1.5 0.2-0.8-0.7-0.6-0.5 0.2-0.5 0.4-1 0 0.2-0.6 0.2-0.2-1-0.4-0.6-0.1-0.7 0.1 0.1-0.3 0.1-0.7 0.1-0.2-0.6-0.1-0.5-0.2-0.8-0.6 0.3-0.4 0.4-0.3 0.4-0.1 0.5 0-0.9-1.5-0.4-0.8-0.3-1.1 0.9-0.6 2.4-0.5 1.4-1 0.3 0.1 0.1 0.3 0.1 0.1 0.1 0 0.5 0.1 0.5 0 1.1 0.5 0.6 0.2 0.8 0-0.3-0.8 0.4-0.4 0.4-0.3 0.4-0.2 0.4 0.5 0.1-0.5 0-0.5 0-0.5-0.1-0.6-0.3 0.4-1-1.7-0.3-0.4 1.9-0.8 0.3 1.3 0.8 0.1 0.5-0.7-0.6-1.6 0.8-0.2 0.7-0.4 0.6-0.5 0.5-0.5 0.9 0.6 1-0.6 0.4-1-1-0.3 0.8-1.6 0-0.7-0.8-0.6-0.8 0.7-0.6 0.4-0.5 0.1-0.9 0-0.2-0.2-0.5-0.8-0.3-0.2-0.5 0.2-0.4 0.3-0.4 0-0.4-0.5-0.6 0.5-1.6 0.9-0.6 0.2-0.5-0.2-0.8-1.2-0.7-0.2-0.9-0.6-0.4-1.4 0.1-1.6 0.7-1 0-0.4-0.2 0-0.8 0 0.6-0.8 1.1-0.8 1.2-0.7 0.9-0.2 0.5-0.6-0.4-1.1-1.1-1.9-1.3-1.7-0.7-0.5-1.5-0.8z"
                  id="TR35"
                  name="Izmir"
                ></path>
                <path
                  d="M121.4 280.3l0.1 0 3.1-0.1 3.8-1.4 5.8-3.9 2.4-0.4 11.7 1 11-3 9.2-0.1 1.7-0.7 1.6-0.9 1.9-0.6 1.9-0.3 7.5-2.7 6.5 0.1 0.5 1.4 0.6 1.3 2.7 1.9 0.6 1.7-0.1 2.1 0.7 3.6-0.4 1.3-1.1 0.8-0.1 1.5 1 1.4 0.8 1.6 1 1.5 3 0.6 0.5 1.4-0.2 1.7-1.4 1-1.9 0.1-2.2 3.1-2.5 2.4-3.7-0.4-3.3 1-1 2.2 0.6 2.2 0.5 0.2 0.5 0.4 0.4 0.8 0.2 0.9-6.2-0.9-3.5-1.9-2.9-2.5-3.5-0.7-2.5 1.8-1.7 3.3-1.9 1.5-5.7 0.4-3.4-0.1-2-1.1-2.2 0-2.1 0.6-2.2-0.4-3.3-2.3-3.8-0.5-1.9-0.7-1.6-1.1-1.6 0-1.5 0.9-0.6 1-2.5 5.6-0.6 1-0.8 0-0.8 0.4-0.4 0.8-0.2 0.8-0.5 0.5-1.2-0.1 0 0.5 0.7 0.5-0.3 0.2-2.8 0.2-0.6 0.2-0.6 0.5-0.3-0.4-0.4-0.3-0.3 0-0.3 0.3-0.1-0.5 0-0.2-0.2-0.1-0.4 0.2-0.1 0.1-0.1-0.2-0.4-0.1 0.5-0.6 0.3-0.6 0.1-0.6-0.2-0.7 0.7-0.3 0.3-0.1 0-0.4-0.4-1.2 0.2-2.7-0.8 0.3-0.3-0.4-0.3 0-0.4 0.3-0.3 0.4 0.2-1 0.3-0.9 0.1-0.8-0.3-1 0.2-0.3-0.2-0.3-0.2-0.3 0.2-0.7 0.7 0.5 0.5-1 0.1-1.3-0.2-0.7-0.7-0.2-2.7-1.8-4.1-1.2-1.8-0.9 0.4-1.2 0.9-0.3 3.1 0.3 1.1-0.2 4.1-1.5 0.6-0.4 1.1-1.4 0.3-0.9 0.2-1.4 0.1-1.3-0.1-0.7-0.4-0.6-0.4-0.5-0.3-0.7 0.1-1.1 0.2-0.3 0.6-0.5 0.2-0.4 0.1-0.3z"
                  id="TR09"
                  name="Aydin"
                ></path>
                <path
                  d="M128.1 308.9l0.6-1 2.5-5.6 0.6-1 1.5-0.9 1.6 0 1.6 1.1 1.9 0.7 3.8 0.5 3.3 2.3 2.2 0.4 2.1-0.6 2.2 0 2 1.1 3.4 0.1 5.7-0.4 1.9-1.5 1.7-3.3 2.5-1.8 3.5 0.7 2.9 2.5 3.5 1.9 6.2 0.9 0.6 1-0.2 1.2 0 1.7 0.5 1.8 1.2 0.4 1.7 0 2.2 0.9 1.4 2.5 0.7 2.7 1.6 2.3 3.5 0.6 0.6 0.6 0.8 1.6 0.6 0.6 3.5 1.6 2.8 2.8 0.7 2.3 0.4 2.3 0.1 1.9 0.2 1 1.2 1.1 2.6 0.2 4.7 2.6 1.7-0.8 0.9-1.3 0.6-1.5 0.3-3.9 2.2-1.8 1.1 3-0.8 3.5 1.9 0.7 2.3-2.9 5.7-0.8 4 5.6-5.1 11.8 0.6 3.7-0.9 3.2-2.3 1.7-2.1 2-0.5 1.6 0.3 1.6-1.1 0.6-1.4-0.6-3 1.2-1.6 3.2-0.3 1-0.4 0.9 0 0.7 0 0.8-0.3 1.5-0.9 1.2-1.3 0.7-0.2-0.2-0.4-0.9-0.6-0.7-2.9-1-0.8-1-0.6 0.5-0.2-0.8 0-0.2 0.2-0.2 0-0.5-1.3 0 0.3-0.3-0.5-0.4-0.3-0.1-0.5 0 0.2-0.5 0.4-0.4 0.5-0.2 0.5-0.1 0-0.2-0.1-0.1-0.2-0.1 0.3-1.1-0.3-1-0.6-0.7-0.7-0.4 0-0.4 0.6-0.5 0.3-0.2 0.4-0.1 0-0.5-0.2-2-0.2-1-0.6 0.3-0.3 0-0.6-0.8-2.3 0.9-1-0.1 0.9-0.3 0.2-0.5-0.3-0.8-0.5-0.9 0.2-0.4 0.5-1.1 0.3-0.6 0.7 0.6 0.5-0.5 0.7-0.9 0.7-0.3 0 0.3-0.3 0.7 0.4 0.2 0.6-0.2 0.3-0.4-0.2-0.6-0.2-0.5-0.3-0.5-0.3-0.3-3-2.1-3.7-1.6-0.4-0.5-0.4-0.9-0.8 0.6-0.8 1.1-0.7 0.6 0 0.4 0.4 0 0 0.4-0.9 0.1-0.4 0.1-0.4 0.2-0.1 0.5 0.1 0.5 0 0.5-0.6 0.1 0.2 0.5 0.2 0.2 0.2 0.2-0.3 0.4 0.5 0.5 0.2 0.3 0.3 0 0.2-0.6 0.3-0.5 0.1-0.5-0.3-0.9 0.8 0.8-0.3 1-0.8 0.9-0.7 0.6 0.3 0.6-0.5 0.3-0.8 0.1-0.6-0.2-0.5-0.5 0-0.6 0.4-0.4 0.7-0.1 0-0.5-0.8-0.4-0.5-0.4-0.5-0.2-0.8 0.3-0.2-1.6-1.7-1.2-2.2-0.4-1.4 0.7-0.3 0-0.2-1.1-0.6-0.4-0.6 0.2-0.6 0.9-0.3-0.4-0.6-1.6 0.4-0.8 0.2-1.2-0.1-1.1-0.5-0.6 0-0.2 0-0.1 0.2 0 0.1-0.1-0.1-0.2-0.1-0.1 0-0.2 0.2-0.3-0.3 0-0.2 0.5-0.1 0.3 0 0.4-0.3-0.2 0-0.1-0.1-0.4-0.6-0.7-1.4-0.3-1.1 0.6 0.2 1.5-0.5-0.4-0.5-0.1-0.4-0.1-0.6-0.2 0.7-0.4 0-0.4-1.1 0-0.7-0.2-0.3-0.6-0.2-1.2 0.3 0 0.3 0 0.1-0.2-0.1-0.4-0.1-0.1-0.1-0.2-0.1-0.1-0.1-0.7-0.2-0.3-0.2 0-0.1 0.6-0.5 0.5-0.9 0.3-1.4 0.1-0.5 0.2 0 0.4 0.3 0.4 0.4 0.3 0.3 0 0.4-0.1 0.4 0 0.5 0.5-0.5 0.9-0.4 0.8-0.5 0.7-0.9 0-2.4-1.3-0.5-0.3-0.3 0 0.2 0.9 0.1 0.3-1.3 0.1-0.5-0.2-0.2-0.5 0.2-0.5 0.5-0.3 1.3-0.2 0-0.4-0.6-0.4-0.7-0.6-0.7-0.4-0.8 0.4-0.6 1-0.3 0.9 0.1 0.9 0.7 0.6-0.3 1.3 1.9 1.4 0.3 1.4-0.5-0.4-0.8-0.3-0.7 0.2-0.3 0.9-0.3-0.2-0.3-0.2 0 0.3-0.1 0.2-0.1 0.1-0.2 0.2 0.3 0.1 0.1 0.3-2.4 0.9-1 0.7-0.8 1.3-0.7 1.3-0.9 1.4-1.1 0.9-1.2-0.4-0.2 0.4-0.2 0.2-0.2 0.1-0.4 0.1 0.1 0.2 0.1 0.4 0.1 0.2-0.6 0.3-0.4 0-0.4-0.2-0.2-0.5-0.5 0.8-0.6 0.2-0.6-0.4-0.6-0.6 0.1-0.2 0.1-0.4 0.1-0.2-0.2-0.2-0.2-0.2-0.2-0.4 3.6 0 0.9-0.4 0.5-0.6 0.5-0.7 0.7-0.7-0.7-0.8-0.9 0.8-0.4 0.3 0-0.3 0.5-0.5 0.1-0.8-0.1-0.7-0.5-0.5-0.5-0.3-0.1 0.2 0 0.7-0.3 0.3-0.5 0-0.5-0.3-0.4-0.2-0.4 0.2-0.5 0-0.5-0.2-0.2-0.6 0.3-0.2 1.7-0.4-0.4-0.4 1 0.1 0.3-0.1 0.3-0.2 0.2-0.3 0.2-0.2 0-0.1 1.8-0.1 0.5-0.3-0.2 0.9 0.7 0.1 0.7-0.6 0.1-0.8-0.5-0.4-0.9-0.1-0.3-0.3 0.1-0.4 0.4-0.4 0.5-0.4 0.3-0.5 0.2 0.4 0.2 0.3 0.6 0.6-0.2-1.1-0.1-1.2-0.3-0.5-0.5-0.1-0.6 0-0.4 0.3-0.8 1.3-1 0.7-0.4-0.1-0.2-0.6-0.7 0.7-1.9 0.6-0.7 0.4-0.3 0-0.3-0.8-0.4 0.2-0.6 0.6-0.7 0.4-0.2-0.8-0.4 0-0.3 0.5-0.4 0.3-0.3-0.2-0.3-0.5-0.3-0.1-0.4 0.8-0.3 0-0.6-0.5-0.8-0.5-1-0.2-0.9 0.4 0-0.4-0.5 0.2-0.6 0-0.6-0.2-0.7 0-0.8 0.2-1 1.3-0.7 0.5 0 0.4 0.2 0.5-0.7 1.9-0.1 1.3-1.6-0.8-0.8-0.3-0.8-0.2-0.9 0.1-0.7 0.2-0.5 0-0.2-0.7-3.3 1.2-0.2 0.4-0.2 0.3-0.2 0.2-0.2-0.2-0.6-0.5-0.4-0.2-1.1-0.2-0.4 0-0.6 0.2-0.1-0.7-0.4-0.4-0.5 0-0.6 0.3 0-0.2 0-0.1-0.2 0-0.1-0.1 0.2-0.3 0-0.4-0.2-0.3-0.4-0.2 0-0.4 0.6 0.3 0.8 0.2 0.8 0 0.6-0.3 0.4-0.6 0.2-1 0.6-0.6 0.5-0.4 0.4-0.1 1.2 0.1 3.5-0.6 1.7 0.2 0.9-0.3 0.2-0.7 0.1-0.8 0.2-0.7 1.1-0.3 2.2 1.4 0.6-0.3 0.8 0.4 0.6 0 0.7-0.1 0.5 0.1 1.2-1.1 0.7-0.4 4.1-0.1 0.5-0.2 0.3 0.4 0.8 0.3 1 0.3 0.9 0 1.6-0.2 0.8 0.3 0.8 0.7 0.2-0.6 0.3-0.3 0.9-0.7-0.3-0.7-0.5-0.3-0.7-0.2-0.8 0 0.3-0.4 0.4 0.2 0.1-0.4-0.3-0.6-0.5-0.5 0.2 0 0.1-0.1 0.1-0.1 0.2-0.1 0.4 0.2 0.3-0.2 0.5-0.3 0.5-0.2 0-0.3-1-1-0.4-0.6-0.3-0.5 0.7 0.4 0.3-0.4-0.3-0.2-0.1-0.2-0.1-0.2-0.2-0.2 0.4 0 0.8-0.1 0.5 0.1 0-0.4-1 0 0-0.4 1.1 0 0.5 0.1 0.3 0.3 0.3-0.3 0.3-0.1 0.2 0.1 0.2 0.3 0.3 0 0.5-0.3 1.1 0.5 0.4-0.2 0.3 0 0.1 0.4 0.3 0.8 0.1-0.3 0-0.1 0-0.1-0.1-0.3 0.1-0.3 0-0.2-0.2-0.2-0.3-0.1 0-0.4 0.4-0.1 0.4 0.1 0.3 0.1 0.2 0.3 1-1.6 0.3-0.4 0-0.5-0.6 0 0.3-0.6 0.3-0.6 0.8 0.6 0.7 0.2 0.4-0.4-0.3-0.8 2.6-1 1.4-0.4 0-0.3-0.4-0.5-0.3-0.2-0.5-0.2-6.2 1-2.3-0.1-0.9 0.2 0.1 0.7 0 0.4-0.8-0.4-5-0.8-0.5 0.1-0.6 0.6-0.6 0.1-0.5-0.1-1.4-0.3-1.2 0.1-3.9 1.3-0.5-0.2-0.3 0 0 0.2-0.3 0.6-0.4-0.1-0.4-0.1-0.6 0-0.5 0.2-1-0.7-1.5 0-1.5 0.6-0.9 0.9-0.9-0.5-1.3-0.5-1.1 0.1-0.3 1.4-1.1-0.9-0.5-0.2-0.7-0.2-1.4 0.2-0.5-0.2-0.5-0.4-0.8-1.3-0.3-0.3-0.8 0-0.7 0.7-0.3-0.5 0.1-0.2-0.3 0-0.1 0.6-0.6 1.4-0.1-0.3-0.1-0.1 0.1-0.1 0.1-0.3 0-0.1-0.1 0-0.1-0.1-0.1-0.2 0.2-0.2 0.1-0.1-0.1-0.1-0.2-0.4-0.4 0.3-1.3 0.3-0.6 0.2 0 0.4 0.2 0.6-0.3 0.7-0.5 0.5-0.7 0.3 0.2 0.5 0.2 0.3-0.5-0.2-0.2 0-0.3 0.2-0.3-0.5-0.9 0.2-0.5-0.9-0.2-1.3-0.1-1-0.2-1-0.4-0.8-0.2-0.8 0.5-1.3 0.4 0 0.8-0.3 0.8-0.6 0.6-0.7-0.3-0.3-0.4-0.1-0.4 0.1-0.5 0.3-0.2-0.2-0.1-0.2-0.1-0.4 0.4 0.3 0.5-0.6 1.5 0.5 0.9-0.2 0-0.4 0.1-0.5 0.1-0.5-0.2-0.7 0.5 0.2 0.4 0.3 0.1 0.5 0 0.7 0.3 0 0.3-0.6 0.3-0.5 0.4-0.1 0.3 0.4 0.2-0.2 0-0.1 0.1-0.2 0 1 0.2 0.3 0.3 0 0.5 0 0.4 0.3 0.3 0.2 0.3 0.1 0.7-0.2-0.2 0.1 0 0.1-0.1 0-0.1 0.1 0.7 1.3 0.7 0.4 0.7-0.1 1.2-0.9 1-0.5 0.4-0.3 0.5-0.3 1.2 0 0.5-0.3 0-0.4-0.7 0.1-0.1 0.1-0.2 0.2-0.4-1.9-0.3-1-0.6-0.4 0-0.4 2.2-0.5 0.6 0.1-0.5 1.2 0.5-0.1 0.4-0.3 0.7-0.8-0.7-0.3-0.3-0.4 0-1.2 0.1-0.2 0.6-0.5 0.3-0.3 0.1-0.4 0.1-0.8 0.1-0.4-0.6-0.4 0-0.1-0.4-0.1-0.2 0.2 0 0.3-0.1 0.1-1.1-0.1-0.6 0.2-0.2 0.5-0.3 0.9-0.6 0.8-0.7 0.4-0.7-0.3 0.8-0.7 0.1-0.8-0.5-0.5-1.1 0-0.1 0.2-0.5 0.5-0.4 0.2-0.3-0.3 0.1-0.6 0.4-0.5 0.8-1.1 0-0.5 0.2-0.6 0.1-0.9-0.6-0.9-0.3 0-0.2 1.6-1.1 0.6-1.3-0.2-1-0.8-0.4-1.3 0.4-1.2 0.9-1 1.1-0.5-0.6-0.9-0.9-0.5-0.2 0.1z"
                  id="TR48"
                  name="Mugla"
                ></path>
                <path
                  d="M216.3 375.3l1.3-0.7 0.9-1.2 0.3-1.5 0-0.8 0-0.7 0.4-0.9 0.3-1 1.6-3.2 3-1.2 1.4 0.6 1.1-0.6-0.3-1.6 0.5-1.6 2.1-2 2.3-1.7 0.9-3.2-0.6-3.7 5.1-11.8 1.4-0.2 1.3-0.7 1.1-1.6 1.3-1.3 1.9-3.5 0.7-4.4 2.6-3.6 8.6-4.4 2.9-0.5 7-3 2.9 0.4 2.9 3.6 1.1 0.4 6.6-0.3 4.3 0.5 3.7 0 1.8-0.5 2.9-3.1 0.9-2.6 3.3-2.9 4.2 1.7 4.9-1.7 2.6-0.4 2.2-1.6 2.4-0.9 2.5 0.4 1 1.3 1.2 1.2 1.8 1.5 2.1 0.8 4.7 0 12.2 2.6 4 2.6 3 2.8 3.3 2.3 2.4 2.5 2.1 3 1.4 1.3 1.4 1 3.2 3 1.8 0.8 1.5 0.9 0.2 1.9-1.2 0.9-0.5 1.8 0.7 1.6 2 0.8 2.2 0.2 1.5 0.3 1 1.4 0.8 4-0.4 4 1.7 4.3 1.9 4 1.3 1.6 1.6 1.1 1.9 1 0.9 1.9-0.4 3.5 0.2 6.5-0.7 2.8-0.8 1.5-0.6 1.6-0.2 1.5-0.4 1.4-0.7 1.1-0.2 0.4-0.2-0.1-1.7-0.2-0.8-0.2-3.7-2.1-0.5-0.5-0.3-0.7-0.8-0.4-1-0.3-0.8-0.4-2.2-3-0.4-0.1-0.2-0.3-0.9-0.6-0.2-0.3-0.2-1.7-0.1-0.5-1.1-1.6-2.9-2.6-0.6-1.6-0.1-0.5-0.2-0.4-0.5-0.8-0.5-0.9-0.2-0.2-0.6-0.3-0.2 0-0.8-1.8-0.6-1-0.7-0.4-2.9-3-0.3-0.2-0.5 0-0.5 0-0.5-0.2-0.4-0.2-0.3-0.3-0.2-0.2-1.9 0.1-1-0.1-0.4-0.6-0.5-0.5-1.3-0.5-1.3-0.2-0.8 0.2-0.5-0.4-1.8-0.8-0.9-1.3-0.6-0.6-0.6 0.1-1-0.2-3.6-1.6-0.8-0.7-0.7-0.8-9-4-1.3-0.2-0.1-1.5-1.7-1-19.1-2.9-7.8 0.5-1.3-0.4-1-0.6-1.9-1.5-2.8 1.9-1.2 1.3-0.7 1.8-0.6 1.1-0.2 0.4 0 1.9-0.3 1.9 0.1 1 0.5 0.9 0 0.4-0.4 0.5-0.3 0.5-0.2 0.6-0.1 0.8 0.1 1.1 0.3 0.6 0.3 0.5 0.3 0.7 0.3-0.4-0.1 1.2-0.7 2.1 0.2 0.7-1.7 1.4-0.7 0.9-0.5 2.1-1.1 1.6 0.1 0.5-0.7 1 0.1 1 0.5 0.8 0.7 0.4-0.3 0.9 1.2 0.4-0.7 0.9-1.3 1.1-0.5 1.2 0.3 0 0.2-0.3 0 0.1 0.1 0.1 0.4 0.1-0.5 0.5-0.7 0.5-0.6 0.5-0.5 1.4-0.6 0.7-1.4 1.2 0.1-2.5-0.2-1-0.6-0.5-1.1 0.3-5-2.3-1 0-3.6 0.6-0.4 0.5-0.2 0.9 0 1.2-0.3 0-0.1-0.4-0.3-0.8-0.3 1.1 0 0.5-0.7-0.4 0.1 0.4-0.1 0.4-1.7-0.4-0.4-0.2-0.4-0.3-0.4-0.2-0.6 0.3 1 0 0 0.4-3.3 1.7-0.7-0.1-0.5 0.4-0.6-0.1-0.5-0.2-0.6-0.1-0.5 0.1-0.2 0.1-0.6 0.6-0.8 0.5-1.8 0.4-0.7 0.3 0.2 0.1 0.1 0 0.1 0.1 0 0.3-0.5 0-1.2 0.3 0 0.4 1 0-0.6 0.7-0.7 0.5-0.8 0.3-0.8 0.2 0-0.5 0.2-0.2 0.2-0.1 0.1-0.2 0.1-0.3-0.6 0.1-0.1 0.1-0.2 0.3-0.6-0.6-0.6-0.2-0.5 0.1-0.6 0.2-0.2 0.3 0 0.2-0.2 0.2-0.5 0.1 0 0.2-0.8 1.1-0.2-1.2-1.6-0.8-0.5-1.2 0.7 0.3 0.3-0.3-0.1-0.6-0.4-0.2-2.4 0.3 0-0.3 0.4-0.3 0.5-0.1 0.5-0.1 0.5 0-1-0.4-3.8 0-1.3-0.3-3-1.7-0.5 0.6-0.3-0.6-0.1-1.6-0.3 0.4-0.2-0.1-0.2-0.2-0.3-0.1-0.5 0.2-0.2 0.1-0.1 0.2-0.2 0.3-0.3 0.3-0.1 0.4-0.2 0.4-0.4 0.2-0.4-0.3-0.3-0.6-0.3-0.4-0.3 0.4-2.9-2.7z"
                  id="TR07"
                  name="Antalya"
                ></path>
                <path
                  d="M373.5 387l0.2-0.4 0.7-1.1 0.4-1.4 0.2-1.5 0.6-1.6 0.8-1.5 0.7-2.8-0.2-6.5 0.4-3.5 3.1-0.4 3.1 0.8 2.9 0 2.7-1.3 2.8-0.6 2.9 0.1 1.5-0.2 1.2-1.2 0.5-2.1 0.7-1.9 0.7-0.3 0.7-0.1 1.7 0.9 1.8 0.7 1.2-1.6-1.1-1.8-2.2-1.6-2.5-0.7-1-0.8-0.9-1.2-1.2-0.5-0.7-1.6-0.4-1.7-0.8-1.4-1-1.2-0.1-1.9 1.8-0.3 0.8-0.3 2.1-1.4 1.4-0.5 0.6 0.3 0.1 0.5 0.4 0.7 1.7 0.4 1-1.7 0.6-2.6 1.5-1.9 1.8-0.6 1.6-1.1 1.5-1.4 1.7-1 2-0.6 6-0.5 3.8-1.5 3.9-0.4 1.6 0.5 1.2-0.1 1.7-0.9 3.5-0.7 4.6-3.5 1.7-0.7 3.6-0.3 3.3-1.3 1.6-1.9 1.3-0.8 4.2-1.5 2.7-1.3 2.1-2.1 1.4-0.7 2.5-0.9 0.9-0.6 0.6-1.9 3.5-0.1 1.6-0.6 1.5-0.9 3.3-1.4 3.3 0.8 1.6 3.6 1.8 3.3 2.3 1.8 0.1 6.1 1 3.4 2.3 1.7 2.9-0.2 1.8 1.9-1.4 3.7 0 3.4 0.7 3.3 0.2 1.7-0.2 1.6-1.8 1.6-2 0.6-2.5 1.7 0 0.1-0.2 0-0.8 0.2-0.6 0.3 0.1-0.3 0.3-0.9-0.7-0.4-2-2.1-3.9-2-0.2 0.4-1.4-0.8-2 0.5-3.2 1.5-1.4 0.2-0.6 0.2-1.4 0.9-0.3 0.2-0.5 1.1-0.2 0.3-1.5 0.8-4.1 3.8-5.2 3.2-1.4 1.2-4.1 5.3-0.4 0.9-0.2 0.6-0.5 0.3-1 0.5-1.6 1.7-1.7 1-0.1 1.5 0.2 1.9-0.1 1.6-0.9 0.8-1.5 0.3-1.3 0.4-0.5 1.3 0.3-0.1 0.2-0.2-0.1 0.4-0.9 1.2-0.4 0.7-0.2 0.9 0 0.7-0.3 0.4-0.8 0 0-0.4 0.4-1-0.2-1.7-0.7-1.5-1-0.6-0.7-0.2-0.6-0.4-0.6-0.2-0.7 0.6-0.9 1.4-0.4 0.6-0.8 0.2 0.3 0.1 0.2 0.1 0.1 0.2-0.5 0.5-0.7 1.3-0.6 0.2-0.4 0.1-1.7 0.7-0.3 0.3-1.5 1.6-0.2 0.6-0.3 1.6-0.3 0.4-0.1-0.4-1.2-1.8-0.2-0.9-0.4-0.2-0.6 0.1-0.7 0.3-1.1 1-1.1 1.4-1 1-1-0.2 0.6-0.3 0-0.5-1.1-0.1-2-0.5-1-0.2-1 0.2-1.7 0.9-0.7 0.2-1.8-0.7-1-0.1-0.9 1.1-1 0.1-2.2-0.1-1.4 0.4-0.5 0.1-0.6-0.2-1-0.5-0.5-0.1-1.1 0.3-0.6 0.7-0.4 1-0.7 0.8-0.5 0.4-0.2 0-0.1-0.2-0.5-0.2-0.6-0.1-1.5 0.1-3.1-0.8-0.9 0-3.1 1.6-0.5 0.2-0.4 0.7-2.6 1.9-2-0.1-4.3-1-1.8-0.9-2.9-1.9z"
                  id="TR33"
                  name="Mersin"
                ></path>
                <path
                  d="M539.2 332.9l-1-0.4-1-0.2-0.9 0.5-3.9 4.5 0 2.5 0.7 1.2-1.8 2.3-0.7 0.5-0.8 0.2-0.4 0.4-1.6 1.8-0.7 0.6-1.8 0.4-4 0.2-1.7 0.7-0.6 0.5-0.5 1.1-0.5 0.4 0.2-0.4 0-0.3-0.2-0.3-0.4-0.2-0.4 0.9-0.7 0.7-0.5 0.7 0.4 0.9 0.7-0.9 1 0.3 0.2-1 0.2 0.1 0.2 0.2 0.1 0.2 0.1 0.3 0.3-0.4 0-0.4 0-0.4-0.3-0.4 0.7-0.4 0.3 0.3 0.2 0.6 0.3 0.3 0.5-0.1 0.5-0.4 0.5-0.2 0.6 0.3-2.8 1.3-1.3 1-0.8 1.4 0.1 0.6 0.7-0.4 2.1-2.5 0.6-0.4 0.8-0.2-1.8 1.8-0.3 0.5-0.4 1.1-0.3 0.5-0.2 0.5 0 0.8-0.1 0.7-0.4 0.2-0.4 0.1-0.7 0.6-0.5 0.1-1.5 0.1-0.6-0.1-1.5-1.4-0.4-0.2-0.7 0.2-0.4 0.3-0.1 0.6 0.4 0.1 0.4 0.1 1.7 0.7-2.1-0.6-2-0.2-0.4 0.1-1.1 0.6-0.3 0.3-0.2 0.4-0.4 0.3-1 0.3-1.2 1.1-0.1 0.2-0.5-0.3-0.8-1.1-0.5-0.3-0.7-0.2-12-7.3-1.3-0.4-1.5-1.1-1-0.3-0.7-0.1-0.6 0 0-0.1 2.5-1.7 2-0.6 1.8-1.6 0.2-1.6-0.2-1.7-0.7-3.3 0-3.4 1.4-3.7-1.8-1.9-2.9 0.2-2.3-1.7-1-3.4-0.1-6.1-2.3-1.8-1.8-3.3-1.6-3.6 0-2.8 0.2-1.3 1.2-1.9 0.7-0.6 0.7-0.7 0.7-2.5-0.3-1.7-1.2-3-0.2-3 1.5-2.2 2.4 0.2 2.4 0.8 4.7-0.8 6.2-6.3 8.2 0.7 4.3 1.1 5.8 2.3 2.5-1.2-1-11 17.6-12.2 2.6-3.5 2-4.1 1.3-1.9 2.1-5 2-1.7 3.7-2.1 3.8-1 2.5 1.1 1.8 2.8 1.2 3.2 2 2.1-2.6 2.5-1.5 3.8-2.4 10.7-1.4 3.8-0.6 4-0.1 1.8 0.3 2.9 1.5 3.7-0.1 2.4-1.6 1.6-0.9 0.5-1.3 1.7-3.6 0-4.5 0.3-2.2 3.7-2.7 6.2-3.1 7.7 0 4.6 1.4 4.4 1-0.1 3.1 0.6 1.3 1.1 0.4 0.8 0.6 3.4-0.7 3.1-1.4 3.6z"
                  id="TR01"
                  name="Adana"
                ></path>
                <path
                  d="M336.8 86.3l3.5 0.3 3 0.8 10.2 0.3 2.3 0.1 4.1 0.9-0.3 1.7 0 1.8 1.1 0.7 1.4 0.2 2.2 1.4 5.4 2.7 1.8 3.3-0.1 1.7-0.1 0.8-0.1 1-0.7 3.4-1.7 0.7-1.4 1.3-0.2 3.4 1.8 5-0.8 1.5-1.7 1.9-1.3 2.3-3.9 2.1-6.5-1-2 0.6-2 1.4-5.6 1.3-3.3 1.5-4 0.9-5.9-0.1-1.9 0.3-4.2 1.7-1.6-0.2-1.5-0.6-1.4-0.2-1-0.5-1.6-1.7-8 0.9-4.2-2.6-4.4-1.5-1.2 1.3-0.5 2.4 0.2 3.7-1.5 3.2-3.9 2.2-2.9 3.9-3.1-0.5-2.8-1-1.8-2.3-2.9-5.2-0.7-2.7 0.9-2.3-1.1-2.2-1-1.2-0.3-1.6 0.6-0.7 0.8-0.4 1.3-3.9 1.9-2.9 3.3 0.2 3.1-1.5 0.6-1.9-0.4-1.9-0.1-2.1 3-2.3 3.9 1.4 7.7-0.3 5.4 2.3 4.6-0.5 3.6-2.8 1.8-4.5 0.2-3.3 2.3-1.8 6.1-1 4.4-1.1 2.3-2.7 0.8-5.5z"
                  id="TR14"
                  name="Bolu"
                ></path>
                <path
                  d="M292.4 142.5l2.9-3.9 3.9-2.2 1.5-3.2-0.2-3.7 0.5-2.4 1.2-1.3 4.4 1.5 4.2 2.6 8-0.9 1.6 1.7 1 0.5 1.4 0.2 1.5 0.6 1.6 0.2 4.2-1.7 1.9-0.3 5.9 0.1 4-0.9 3.3-1.5 5.6-1.3 2-1.4 2-0.6 6.5 1 3.9-2.1 1.3-2.3 1.7-1.9 0.8-1.5-1.8-5 0.2-3.4 1.4-1.3 1.7-0.7 3.7-0.1 3.8 0.6 13.9 6.5 1.4 1.8 1.1 2.2 1.5 1.8 1.8 1.3 0.8 0.4 2.1 1.8 1.5 0.9 1 1.6 0.5 2.1 3.1 2.5 3.5-0.4 0.2-1.6 0.6-1.8 0.8-0.3 1 0 1.5 0.6 1.4 0.9 6.4 2.1-0.6 5.1-0.6 3.5 0 4.2 0.3 3.7-0.6 3.6-5.5 0.6-3.5 1.5-2.2 5.3-1.2 5.2-1.4 5.2 1.2 5.3 3.8 4.6 1.3 2.3 1.9 8.8 3.8 7.4 0.9 3.4 5.4 3.4 2.4 2 0.8 0.3 0.7 0.6 0.7 1.2 1 0.7 2.5 0.2 1.6 0.6 0.6 0.7 3 1.1-0.4 2-1.6 4.1 1.9 5.1-0.1 3.6-4 3.8-1.8 0.9-4.2-0.2-4.2 1.2-3.9 2.2-4.1 1.1-1-2.4-1.5-2.2-1.3-2.3-1-2.5-0.1-3.5 0.6-3.6 0.2-3.3-0.3-3.3-2.5-5.9-4.7-3.4-2.7-0.8-0.8-0.5-2.1-1.6-2 0-0.9 1.3-0.5 1.6-0.4 3.2-0.7 1.1-2.1-0.3-2.7-2.2-2.3-0.7-4.8 4-1.8 2.3-2.1 1.2-3.6-1.5-3 1.4-2.8 2.4-2-0.2-1.7-1.6-1.6-0.9-4-1.4-2.4 0.3-5 1.5-1.7-0.5-6.8-5.7-2.1-1.3-2-0.9 1.1-0.8 2.5-1.2 0.5-0.6 0.4-0.7 1-0.3 1.1-0.1 1.1-1.2 0.9-1.4-0.3-1.9-1.5-1-1-1.6-0.1-2.4-0.1-0.3-0.2-0.8 0.5-1.9 0.2-1.8 0.1-1.9-0.6-1.3-0.7-1.1-0.2-1.9-0.6-1.3-2.4-2.1-2.4-6.5-0.3-3.1 2-2.2-0.1-1.6-0.6-0.4-0.7-1.1-0.4-0.9-1.6-0.1-1.4-0.7-1.2-1.9-0.6-1.9-0.2-1.6-0.6-1.1 0-1.8-2.2 0.3-1.9-1.3-1.7 0.6-3.5 0.1-1.8 0.4-1.6-0.5-1.4 0.4-1.9-2.1-1.2 0.2-1.2 0.4-4.4-0.6-1.3 1.1-1.3 1-1.3-0.5-1.2-0.7-2.7-0.3-4.6 1.9-1.9 0.2-1.1-0.4-0.3-1.3-0.4-3.7-2.1-2.6z"
                  id="TR06"
                  name="Ankara"
                ></path>
                <path
                  d="M248.6 118.2l1.3 2.8 1.7 2.5 1.9 1.6 1.7 1.9 4.2 1.3 6.6-1.7 2.1-0.2 1.7 0.5 1.8 0 1.5 0.9 1.2 1.8 0.8 0.7 3.5-0.1 2.5 0.6 0.7 2.7 2.9 5.2 1.8 2.3-4.9 0.2-3.8 3.1-0.9 3.1-0.6 3.3-1.6 2.8-2.4 1.8-2.6 0-2.4 1-0.8 1.4-0.8 1.2-1.4 0.7-1.3 1.1-0.4 6.3-2.1 3.6-2.6 3.1-1.8 0.5-3.6 0.4-3.7 1-2.1-0.5-5.2-3.8-1.7-0.7-1.9-1.2-0.7-1-1.7-3.3-0.2-2.7 0.8-5.2 2.7 0.5 2.3-0.8-0.6-3.1-1.3-1-2.5-3.6 0-3.1 1.3-6.2 4.6-4.9 0.8-1.7-0.2-1.7-1.1-1.4-0.2-1.9 0.6-1.5 0.5-1.7 0.2-2 0.9-1.7 0.9-1 1.1-0.8 2.5-1.4z"
                  id="TR11"
                  name="Bilecik"
                ></path>
                <path
                  d="M286.5 141l2.8 1 3.1 0.5 2.1 2.6 0.4 3.7 0.3 1.3 1.1 0.4 1.9-0.2 4.6-1.9 2.7 0.3 1.2 0.7 1.3 0.5 1.3-1 1.3-1.1 4.4 0.6 1.2-0.4 1.2-0.2 1.9 2.1 1.4-0.4 1.6 0.5 1.8-0.4 3.5-0.1 1.7-0.6 1.9 1.3 2.2-0.3 0 1.8 0.6 1.1 0.2 1.6 0.6 1.9 1.2 1.9 1.4 0.7 1.6 0.1 0.4 0.9 0.7 1.1 0.6 0.4 0.1 1.6-2 2.2 0.3 3.1 2.4 6.5 2.4 2.1 0.6 1.3 0.2 1.9 0.7 1.1 0.6 1.3-0.1 1.9-0.2 1.8-0.5 1.9 0.2 0.8 0.1 0.3 0.1 2.4 1 1.6 1.5 1 0.3 1.9-0.9 1.4-1.1 1.2-1.1 0.1-1 0.3-0.4 0.7-0.5 0.6-2.5 1.2-1.1 0.8-0.2 1.8-0.3 1.8-0.7 0.9-3.8 0.7-2.8-0.3-1.8-0.5-3.7-0.1-6.1 0.6-3.6-2.7-2.3-5.4-3.4-3.5-1.9 0.5-1.7 1.5-1.7 1.1-1.8 0.5-3.5 2.7-3.4 1.5-2.1-2.4-2.4-1.1-1.6 1.3-3 3-3.2 2.1-1.7 0.8-2.2 0.4-2.1-1.2-0.8-0.6-0.8-0.5-1.1-1.4-0.7-1.9-1.3-1.3-1.7-0.4-0.9-3.9-4.7-6.3-0.6-4.7-1.3-3.7-5.2-5-1.8-3 2.6-3.1 2.1-3.6 0.4-6.3 1.3-1.1 1.4-0.7 0.8-1.2 0.8-1.4 2.4-1 2.6 0 2.4-1.8 1.6-2.8 0.6-3.3 0.9-3.1 3.8-3.1 4.9-0.2z"
                  id="TR26"
                  name="Eskisehir"
                ></path>
                <path
                  d="M397.1 83.1l1.1 1.4 0.7 0.6 6.1 0.6 4.3 2.9 1.8 2.1 2.2 0.7 1.8-0.6 5-3 3.7-0.7 7.7-4.1 1.6-0.6 0.7 0.4 0.5 2.5-1.5 2-1.2 1.4-0.5 1.9 1.7 3 2.8 1 1.5 1.4 1.6 1.2 2 0.4 1.9 0.1 2.4 3.9-0.6 2.5-1 2.5 0.3 2.6 1.2 2.4 0.3 2.2-0.1 2.2 0.6 2.3 1.2 2 0.7 1.5 0.5 1.7 0.4 1.6-0.1 1.6-0.7 0.4-1.1 2.6-1.4 2.4-0.8 0.4-1 0.2-3.8 2.2-4.4-0.4-13.1-4.2-6.4-2.1-1.4-0.9-1.5-0.6-1 0-0.8 0.3-0.6 1.8-0.2 1.6-3.5 0.4-3.1-2.5-0.5-2.1-1-1.6-1.5-0.9-2.1-1.8-0.8-0.4-1.8-1.3-1.5-1.8-1.1-2.2-1.4-1.8-13.9-6.5-3.8-0.6-3.7 0.1 0.7-3.4 0.1-1 0.1-0.8 4.9-2.3 5.6-4.5 2.3-4.2 1.7-1 4.6 0.5 4.6-2.5 2-5.1z"
                  id="TR18"
                  name="Çankiri"
                ></path>
                <path
                  d="M385.2 52.5l1.6 1.8 2.9-0.4 3.5-1.2 3.5 2.6 1.3 5.6 1.3 3.1-4.6 0.9-2.5 2.5-2.2 3-0.4 2.1 0.9 6.8 2.7 0.4 2.5 1.1 1 1.5 0.4 0.8-2 5.1-4.6 2.5-4.6-0.5-1.7 1-2.3 4.2-5.6 4.5-4.9 2.3 0.1-1.7-1.8-3.3-5.4-2.7-2.2-1.4-1.4-0.2-1.1-0.7 0-1.8 0.3-1.7-4.1-0.9-2.3-0.1 0-5.6 1.5-5.1 0.8-3.3 1.7-2.1 2.5-3.5 5.2 0.2 5-1.3 5.7-4.8 2.1-4.6 2.7-0.8 4.5-4.3z"
                  id="TR78"
                  name="Karabük"
                ></path>
                <path
                  d="M569.8 97.6l-0.8 1.6 0.5 1.9 0.6 0.4 2.1 0.5 2.7 1.6 3.9 1.4 2.5 1.5 3.1 1.3 3.4 0.3 3.3-0.5 3.1 0.3 10.9 7.6 6.4 0.4 1.6 3 0.9 3.8-2.5 4.9-2.8 4.1-1.7 0.7-1.8 0.4-0.7 0-0.6 0.2-1.7 1.3-1.7 1-4 0.6-4-0.2-4.4 0.6-6.3 1.6-1.7 0.8-5.4-0.8-1.8 0.6-1.7 1-1.3 3.6-1 3.7-0.9 2.4-1.5 1.6-7 2.7-7.9 0.4-11.1 2.2-0.5-1.3-0.9-0.9-0.9-1.8 0.1-2.4 0.3-3.5-2-1.9-2 0.6-1.9 0.8-2.3 0.5-2.2 0.2-3.7-0.8-4.4 0.4-3.8-2.6-3.3-3.7 1.9-4 1.4-1.6 2.6-1 0.9-0.3 1.6-0.7 1.4-1.6 1.3-1.9 1.4-2.5 2.1-4.7 1.2-1.5 4-0.3 3.8 1.1 1.2 1 1.3 0.7 1.1 0.3 0.9 0.4 0.8 0.7 0.9 0 2.9-1.4 3.9-2.6 0.7-0.7 0.4-0.1 0.4-0.2 0.3-3.2 3.8-0.8 2.1-3 0-2.4 0.8-2 1.7-2.5-1-2.5-0.8-0.3-0.7-0.6-1-0.9-0.5-1.1 0.6-0.8 0-0.5-0.4 0 0-0.4 1.4-0.7 5.2-1.3 1.6-0.8 0.3-0.4 0.3 0.2 2.5 0.1 1.7 1.1 0.8 1.6z"
                  id="TR60"
                  name="Tokat"
                ></path>
                <path
                  d="M638.8 118.8l3.9 14.9-0.1 3.5 1.7 2.6 2.7 1.4 2.9 2.2 3 1.8 4.8-1.2 5 1.6 4.8 2.4-0.2 3.1-1 2.4-2.2 0-2.1-0.4-2.5 0.6-2.3 1.6-2.3-0.4-2.2-1.7-2.1 1.2-1.8 5.2 0.4 1.9 3.3-0.1 3.5-0.9 1 1.2-0.5 1.7-1 0.2-0.9 0-1.8 0.9-1.2 2.1-2.3 3.1-0.6 6.4-1.1 2.2 0.6 2.3 1.7 1.6 1.5 1 1.1 1.5-0.4 1-1.7 1.1-0.5 0.5-0.4 0.5-0.4 0.5-0.6 0.1 0.2 0.7 0.1 1.1 0 2 0.6 1.8 0.3 1.8-0.2 1.1-0.1 1.2 0.3 0.9 0.2 1-0.4 2.1-1 1.6-3.7 1.3-3.4 2.6-3.1 0.9-3.2-0.5-1.7 0.3-1.6 0.7-3.7 0.1-1.8 0.9-1.7 1.1-1.8 0.8-1.7 1.1-3.9 0.9-4.1-2-2.1 0.5-1 2.4 0.5 2.9 0.8 2.7 0.6 1.3 0.3 1.3-0.8 2.3-3.4 3.7-2 1.3-1.1 0.4-2.4 2.9-2.4 2.2-2.8 1.3-10.7 0.9-1.2-0.6-1.2-0.7-6.1-0.9-6.3 1.5 0.1-2.5 1.2-2 0.3-0.9 0.2-1 1.9-5.6 1.3-4.8 0.9-2.3 1.1-2.2 2-5.8-1.7-5.4-3.8-2.1-4.1-0.7-4.4 0-4.3 1.4-2.4-0.8-2.3-1.8-7.9-1.1-3.6 1.3-4.7-0.7-4.5-3.3-1.6-0.2-1.5-0.6-0.4-0.5-0.6-1.3-0.4-0.6-2.3-1.2-2.5-0.2 1.8-2.1 2.1-1.6 1.5-2 1.2-2.3 1.4-2.3 1.7-2 4.1-3.2 3.4-3.4 1.4-5.1-0.1-1.4-0.5-1.4-1.6-0.9-2.3-4.1-1.4-4.5 11.1-2.2 7.9-0.4 7-2.7 1.5-1.6 0.9-2.4 1-3.7 1.3-3.6 1.7-1 1.8-0.6 5.4 0.8 1.7-0.8 6.3-1.6 4.4-0.6 4 0.2 4-0.6 1.7-1 1.7-1.3 0.6-0.2 0.7 0 1.8-0.4 1.7-0.7 2.8-4.1 2.5-4.9 2.6 5.1 3.6 2.9 1.7-0.2 1.4-1.1 1.4-1.9 1.7-1.3 4 0.2 1-0.6 0.2-1.1-0.5-0.7 0.1-1.8 0.5-0.8 1.9-0.4 2.2-0.2 1.6-0.8 1.4-1.2z"
                  id="TR58"
                  name="Sivas"
                ></path>
                <path
                  d="M483.5 71.9l2.5 4.4 0.6 2.3 0.3 2.5 0.7 2.3 3.7-0.4 1.8 0.4 0.4 1.8-0.7 1.6-1 1.2-0.6 1.9-0.2 6.3-0.7 4.1 0.5 1.7 1.9 4.2 1.3 2.2 1.5 1 7.9 1.3 3.6 2.2 1.6 0.3 0.8 0.3 1.2 2.9 0 2.3-1.1 4.5-4.1 6.3-0.5 1.4 0 1.7-0.5 4.2-1.9 0.7-4.6 0.4-0.5 2.8 0.6 0.7-0.1 1-0.5 1.4-0.1 1.2-1.3 1.7-3.7 2.3-2.2 0.2-4-1.2-7.2 3.5-3.8 0.2-1.9-0.2-2.8 1.1-7.3-1.3-4.6 1.5-4.5 2.1-7.5 0.9-2.7 1.3 0-3.2 1.2-2.5 1-1.5 0.2-2.1-0.7-1.8 0-0.7-0.1-0.7-0.9-1-1.2-0.1-1.2-1.2-1.9-4.5-0.6-2.9 3.8-2.2 1-0.2 0.8-0.4 1.4-2.4 1.1-2.6 0.7-0.4 0.1-1.6-0.4-1.6-0.5-1.7-0.7-1.5-1.2-2-0.6-2.3 0.1-2.2-0.3-2.2-1.2-2.4-0.3-2.6 1-2.5 0.6-2.5-2.4-3.9 4.2-0.9 6.7-2.8 1.2-3.1-1.6-1.7-1-0.4-0.9-2.4 0.5-1.2 1.6-2 1.7-4.2 2.2-2 1.1-1.3 1.3-3.4 1.7-2.4 2.5-0.6 1.2 1.6 1.8-0.1 1 0.2 0.8 0.9 1.6 0.3 1.6-0.4 1.4 0.3 1.2 0.9 0.5 0.9 0.8 0.6 5.4 1.7 1-1.2 0.9-0.8 0.2-0.3 0.1-0.5 0.1-1.1 0.1-0.4z"
                  id="TR19"
                  name="Çorum"
                ></path>
                <path
                  d="M493.1 83.4l14.7 4.2 2.9-0.5 1.6 0.9 6.9 5.3 1.6 2.8 1.6 0.4 3.6-0.2 1.8 0.3 6.3 0.1 3.7 1.8 2-0.5 1.7-1.4 2.2-1.5 1.4-2.5 0.9-2.9 1.9-1.2 1.7 0.6 1.3 1.5 0.6 1.4 0.8 1.4 5.1 3.7-1.4 0.7 0 0.4 0.4 0 0 0.5-0.6 0.8 0.5 1.1 1 0.9 0.7 0.6 0.8 0.3 1 2.5-1.7 2.5-0.8 2 0 2.4-2.1 3-3.8 0.8-0.3 3.2-0.4 0.2-0.4 0.1-0.7 0.7-3.9 2.6-2.9 1.4-0.9 0-0.8-0.7-0.9-0.4-1.1-0.3-1.3-0.7-1.2-1-3.8-1.1-4 0.3-1.2 1.5-2.1 4.7-1.4 2.5-1.3 1.9-1.4 1.6-1.6 0.7-0.9 0.3-2.6 1-1.4 1.6-1.9 4-2.3-0.8-2.5 0.2-2.1-0.2-0.9-0.6-0.4-1.3-0.4-0.2 0.5-4.2 0-1.7 0.5-1.4 4.1-6.3 1.1-4.5 0-2.3-1.2-2.9-0.8-0.3-1.6-0.3-3.6-2.2-7.9-1.3-1.5-1-1.3-2.2-1.9-4.2-0.5-1.7 0.7-4.1 0.2-6.3 0.6-1.9 1-1.2 0.7-1.6-0.4-1.8z"
                  id="TR05"
                  name="Amasya"
                ></path>
                <path
                  d="M202.3 176.8l1.8 1.4 2.1 0.5 3.4 0.4 3.4-0.1 2.6-1.6 0.3-4.1 1-2.8 2.2-1.6 1.3-5 1-1 0.5-1.4 1-3.7 2.4-1.7 4.9 2 1.6 0.1 1.5-0.2 1.3-0.7 1.5-0.1-0.8 5.2 0.2 2.7 1.7 3.3 0.7 1 1.9 1.2 1.7 0.7 5.2 3.8 2.1 0.5 3.7-1 3.6-0.4 1.8-0.5 1.8 3 5.2 5 1.3 3.7 0.6 4.7 4.7 6.3 0.9 3.9-3.3 5.1-5.7 2.6-0.9 2.5-0.7 2.7-1.3 2.6-1.3 4.1-1.9 3.2-1.4 1.8-9.8 5.9 0-6.3-2.2-2.6-1.4-0.6-5.9-1.4-3 0.1-1.4 0.4-1.2 0.7-0.8 3.5-1.2 2.8-2.7 0.2-6 1.7-3.4 0.1-3.2-0.4-2.9-1.9-2.8 0-2.6 2.3 0.2-3.9-1-2.8-1.5-2.9 0.4-3.7-0.3-1.1-0.5-1-3.1-4.7-1.3-1-7.2-1.5-1-0.8-2.4-2.7 0.4-0.6-0.2-1.5-0.2-0.8 0.4-2.1 1.8-0.9 3.5-1.2 1.6-1.1 1.4-2.6 0.4-0.9 1.1-1.4 1.1-1.8 0.6-4.3 0.6-1.8 0.6-0.6 0.3-0.7 0-1.2 0.3-1.2 0.9-1.8z"
                  id="TR43"
                  name="Kütahya"
                ></path>
                <path
                  d="M368 352.8l0.4-4-0.8-4-1-1.4-1.5-0.3-2.2-0.2-2-0.8-0.7-1.6 0.5-1.8 1.2-0.9-0.2-1.9-1.5-0.9-1.8-0.8-3.2-3-1.4-1-1.4-1.3-2.1-3-2.4-2.5-3.3-2.3-3-2.8-4-2.6-12.2-2.6-4.7 0-2.1-0.8-1.8-1.5-1.2-1.2-1-1.3 1.3-3.3 0.4-3.6-0.6-2.2-1.2-1.8 0-2.1 2.1-0.6 1.5-2 0.2-2.8 2-3.1-0.1-4.7-1.1-4.7 1-4 1.6-0.9 3.3-1.3 3-1.9 0.4-2-3.5-3-1.1-1.6-1.6-0.7-3.3-2.8-2.9-3.7-3.7-3-1.3-4.5 8.3-5.5 3.7-3.8 4.1-5.4 0.2-1.4-0.2-1.2-0.1-1.2 0.8-3.3 1.4-2.9 3.6-6 0.4-2.4-0.8-0.5-1.5-1.5 0.2-0.8 0.3-0.8 0.6-4 2.8 0.3 3.8-0.7 0.7-0.9 0.3-1.8 0.2-1.8 2 0.9 2.1 1.3 6.8 5.7 1.7 0.5 5-1.5 2.4-0.3 4 1.4 1.6 0.9 1.7 1.6 2 0.2 2.8-2.4 3-1.4 3.6 1.5 2.1-1.2 1.8-2.3 4.8-4 2.3 0.7 2.7 2.2 2.1 0.3 0.7-1.1 0.4-3.2 0.5-1.6 0.9-1.3 2 0 2.1 1.6 0.8 0.5 2.7 0.8 4.7 3.4 2.5 5.9 0.3 3.3-0.2 3.3-0.6 3.6 0.1 3.5 1 2.5 1.3 2.3 1.5 2.2 1 2.4-0.9 1.7-2.7 3.1-1.1 2-0.7 4.8-1.7 2.6-1.1 3.2-0.3 2.3-0.7 2-0.4 5 1.8 4.6 0.6 0.5 0.5 0.6 0.4 2.5 0.7 2.3 3.2 4 1.9 1.8 2.1 0.1 5.6-1.2 7.4-0.5 3.8 0.1 3.7-0.9 3.7-1.5 3.6-0.8 3.5 0.6 0.7 3.1 1.5 2.4 2.3 0.9 2.2 1.2 1.8 2.6 1.7 2.9 1.9 1.8 1.5 2.2 0.5 3.4-1.1 3.1-1.1 1.3-0.7 1.6-0.3 2.7 0 1.7 1.8 2.6 1.2 1 1.8 1.9 1.1 2.6-0.6 1.9-0.9 0.6-2.5 0.9-1.4 0.7-2.1 2.1-2.7 1.3-4.2 1.5-1.3 0.8-1.6 1.9-1.1-2.3-3.2-5.4-2.8-3.1-3.9-4.1-5.3-5.9-1.1-3.8 0.2-0.9-0.4-3-1.9-0.7-4.7-0.2-2.2 1.4-1.4 1.8-2.3 3.1-4.8 1.8-7.4 0.5-7.1 0.9-4.5 1.8-2.5 2.8-5.6 6.2-5.7 7.9-4.5 5.8-1.2 4 0.3 2.1 2.9 1.3 2.9 0.2 0.7 1.6-0.2 1.8-1.4 2.2-6.8 6.7-5.4 4.1z"
                  id="TR42"
                  name="Konya"
                ></path>
                <path
                  d="M377.3 366.7l-0.9-1.9-1.9-1-1.6-1.1-1.3-1.6-1.9-4-1.7-4.3 5.4-4.1 6.8-6.7 1.4-2.2 0.2-1.8-0.7-1.6-2.9-0.2-2.9-1.3-0.3-2.1 1.2-4 4.5-5.8 5.7-7.9 5.6-6.2 2.5-2.8 4.5-1.8 7.1-0.9 7.4-0.5 4.8-1.8 2.3-3.1 1.4-1.8 2.2-1.4 4.7 0.2 1.9 0.7 0.4 3-0.2 0.9 1.1 3.8 5.3 5.9 3.9 4.1 2.8 3.1 3.2 5.4 1.1 2.3-3.3 1.3-3.6 0.3-1.7 0.7-4.6 3.5-3.5 0.7-1.7 0.9-1.2 0.1-1.6-0.5-3.9 0.4-3.8 1.5-6 0.5-2 0.6-1.7 1-1.5 1.4-1.6 1.1-1.8 0.6-1.5 1.9-0.6 2.6-1 1.7-1.7-0.4-0.4-0.7-0.1-0.5-0.6-0.3-1.4 0.5-2.1 1.4-0.8 0.3-1.8 0.3 0.1 1.9 1 1.2 0.8 1.4 0.4 1.7 0.7 1.6 1.2 0.5 0.9 1.2 1 0.8 2.5 0.7 2.2 1.6 1.1 1.8-1.2 1.6-1.8-0.7-1.7-0.9-0.7 0.1-0.7 0.3-0.7 1.9-0.5 2.1-1.2 1.2-1.5 0.2-2.9-0.1-2.8 0.6-2.7 1.3-2.9 0-3.1-0.8-3.1 0.4z"
                  id="TR70"
                  name="Karaman"
                ></path>
                <path
                  d="M478.9 310.3l-3.3-0.8-3.3 1.4-1.5 0.9-1.6 0.6-3.5 0.1-1.1-2.6-1.8-1.9-1.2-1-1.8-2.6 0-1.7 0.3-2.7 0.7-1.6 1.1-1.3 1.1-3.1-0.5-3.4-1.5-2.2-1.9-1.8-1.7-2.9-1.8-2.6-2.2-1.2-2.3-0.9-1.5-2.4-0.7-3.1 15.7-23.4 2.7 0.4 4.1-1.2 2.7-0.1 1.3 0.7 2 2 2.4 0.7 5-1.9 3.3 9.2 2.8 2.9 7.7 0.1 1.3 4.1-0.5 2.5 0.3 2.6 0.9 2.1 0.5 2.2-2.2 1.4-0.2 3.1-0.8 3.6-6.2 6.3-4.7 0.8-2.4-0.8-2.4-0.2-1.5 2.2 0.2 3 1.2 3 0.3 1.7-0.7 2.5-0.7 0.7-0.7 0.6-1.2 1.9-0.2 1.3 0 2.8z"
                  id="TR51"
                  name="Nigde"
                ></path>
                <path
                  d="M560.3 254.1l-2-2.1-1.2-3.2-1.8-2.8-2.5-1.1-3.8 1-3.7 2.1-2 1.7-2.1 5-1.3 1.9-2 4.1-2.6 3.5-17.6 12.2 1 11-2.5 1.2-5.8-2.3-4.3-1.1-8.2-0.7 0.8-3.6 0.2-3.1 2.2-1.4-0.5-2.2-0.9-2.1-0.3-2.6 0.5-2.5-1.3-4.1-7.7-0.1-2.8-2.9-3.3-9.2 1.3-2.6 1.9-2 4.2-6.7 0.2-2.9-0.5-2.8 0.2-2.8-3.3-4.3-2.2-4.4 1.3-3.1 1-3.3 0-1.5 0.1-1.5 0.5-1.3 0.7-1.1 10.7 1.7 5.4-3.4 2.1-0.7 15.4-10 4-5.6 2.5 0.2 2.3 1.2 0.4 0.6 0.6 1.3 0.4 0.5 1.5 0.6 1.6 0.2 4.5 3.3 4.7 0.7 3.6-1.3 7.9 1.1 2.3 1.8 2.4 0.8 4.3-1.4 4.4 0 4.1 0.7 3.8 2.1 1.7 5.4-2 5.8-1.1 2.2-0.9 2.3-1.3 4.8-1.9 5.6-0.2 1-0.3 0.9-1.2 2-0.1 2.5-1.6 1-0.9 1.9-0.6 3.2-1.5 2.4-3.7 4.3-1.5 2.2-1.7 1.8z"
                  id="TR38"
                  name="Kayseri"
                ></path>
                <path
                  d="M314.6 308.3l-2.5-0.4-2.4 0.9-2.2 1.6-2.6 0.4-4.9 1.7-4.2-1.7-0.7-1.7-1-1.4-1.4-0.2-0.7 0.1-0.9-1.1-0.4-1-2.9-4.4-1.3-2.9-1.6-4.8-2.8-1.8-3 0.1-3.3-0.6-2.1-1.6-1.8-2-2.2-3-2.3-1.6-2.2 2.3-1.3 0.9-1.4 0.7-5.2 3.8-1.8 0.8-1.8-0.6-0.9-1.8-0.7-2 3.7-0.1 3.4-1.9 1.6-2.9 0.6-6.2 1-1.9 3.2-1.9 4.3-4.2 1.9-1.1 2.4-2.2 2.4-2.7 1.7-0.7 1.9-0.5 2.6-2.2 1-0.2 1.1-0.1 3.6-1.4 3-2.8 2.9-3.4 5.9-4.8 2.4-2.6 0.9-0.8 1.2 0.2 0.9 0.6 0.9 0.7 2.1 3.1 1.1 0.7 1.2 0.3 1.3 4.5 3.7 3 2.9 3.7 3.3 2.8 1.6 0.7 1.1 1.6 3.5 3-0.4 2-3 1.9-3.3 1.3-1.6 0.9-1 4 1.1 4.7 0.1 4.7-2 3.1-0.2 2.8-1.5 2-2.1 0.6 0 2.1 1.2 1.8 0.6 2.2-0.4 3.6-1.3 3.3z"
                  id="TR32"
                  name="Isparta"
                ></path>
                <path
                  d="M128.9 189.6l3.6-1.9 2.2 1 2.1 1.7 3.3 1 3.3-0.5 2 0.2 1.9 0.9 0.7 0.1 1.5-0.3 0.8 0 1.2 1.9-1.4 2.6-0.6 2.1 1.9 0.9 3.5-1.3 0.8 0.4 0.3 0.9 3.9 4.3 2.5 5.5 2.1 0.7 2-0.8 2.1-0.4 1.8-1.1 1.3-1.7 1.9-0.4 2 0.2 1.8-0.2 1.8-0.5 4.4-2.2 4.1 0.6 2.4 2.7 1 0.8 7.2 1.5 1.3 1 3.1 4.7 0.5 1 0.3 1.1-0.4 3.7 1.5 2.9 1 2.8-0.2 3.9-1.7 1.7-3.7 2.9-0.5 1.2-1.4 1.6-1.4 0.5 0.2 2.1 1.3 0.5 1.1 1.3 0.2 1.4 0 1.4 0.4 3-0.3 2.8-0.7 1.9-0.9 1.5-1.3 1.2-0.5 0.9 1.2 0.9 1.5 0 1.7 2-4.4 1-2.1 4.1 0.2 1.6-0.6 1.5-2 0.9-2.1 0-6.5-0.1-2.1-3.2-2-6.2-1.3-1.7-2.1-0.6-1.1-0.1-1.7-0.5-0.9-1.1-1.3-0.2-1.8 0.6-4.6 0.7-1.3-3-2-2.1-1.4 0.4-1.4-0.1-2.3-1-2.3-0.1-0.8 1.9-1.1 2-2.4 0.6-2-1.9-0.4-0.9-0.5-1.9-0.4-0.9-3.7-1.7-3.3-2.3-0.8-3.4-1.5-1.4-0.9-0.2-5.4 0.2-3.3-0.3-2.8-2.5-1.3-4.2-1.1-1.4-0.7-0.3-1.3-1.3 0.2-1.3 1.3-2.2 1.5-1.5 0.8-2 0.3-2.4 0.6-2.2 2.6-1.8 3-1.2 3.1-2.6 1.3-1.5 0.3-3.2-1.7-3.3-0.8-2.2-1.6-7.4-1.8-4.7z"
                  id="TR45"
                  name="Manisa"
                ></path>
                <path
                  d="M185.3 305l-0.2-0.9-0.4-0.8-0.5-0.4-0.5-0.2-0.6-2.2 1-2.2 3.3-1 3.7 0.4 2.5-2.4 2.2-3.1 1.9-0.1 1.4-1 0.2-1.7-0.5-1.4-3-0.6-1-1.5-0.8-1.6-1-1.4 0.1-1.5 1.1-0.8 0.4-1.3-0.7-3.6 0.1-2.1-0.6-1.7-2.7-1.9-0.6-1.3-0.5-1.4 2.1 0 2-0.9 0.6-1.5-0.2-1.6 2.1-4.1 4.4-1 2.9 0.6 2.5 1.6 2.7 0 2.4-1.7 1.7 0.3 1.5 1.7 1.6 0.1 1.5-0.5 0.7-0.4 0.6-0.3 1.2 0.8 0.5-0.1 0.7-1.2 0-0.7 2.4-1.9 3.4 0.4 3.6-0.1 1.7-0.5 1.5-1.4-0.4-3.6 0.5-3.6 5.9-1.3 6.5 1.5 5.1 8.6 1.3 1.5 0.7 4-1.3 1.5-12.8 9.6-1.5 0.8-1.5 0.6-0.7 1.8-0.1 6.1 1.2 0.5 2.2-0.1 0.6 0.3 1.3 0.7 0.7 0.2 0.5 0.4 2.2 3.7-3.4 2.8-2 1.2-0.7 0.3-1.4 0.8-0.6 1.3-0.5 1.3-6 3.3-0.1 1.5 0.5 1.7-0.1 2.4 0.6 2.2 0.4 0 0.2 0 0.3 0.3 0.3 0.4 0.2 0.9-0.1 0.5-2.1 4.2-4.7 7-1.3 2.9-0.8 3.1-0.4 3.2-2.2 1.8-0.3 3.9-0.6 1.5-0.9 1.3-1.7 0.8-4.7-2.6-2.6-0.2-1.2-1.1-0.2-1-0.1-1.9-0.4-2.3-0.7-2.3-2.8-2.8-3.5-1.6-0.6-0.6-0.8-1.6-0.6-0.6-3.5-0.6-1.6-2.3-0.7-2.7-1.4-2.5-2.2-0.9-1.7 0-1.2-0.4-0.5-1.8 0-1.7 0.2-1.2-0.6-1z"
                  id="TR20"
                  name="Denizli"
                ></path>
                <path
                  d="M236.6 338.5l-4-5.6-5.7 0.8-2.3 2.9-1.9-0.7 0.8-3.5-1.1-3 0.4-3.2 0.8-3.1 1.3-2.9 4.7-7 2.1-4.2 0.1-0.5-0.2-0.9-0.3-0.4-0.3-0.3-0.2 0-0.4 0-0.6-2.2 0.1-2.4-0.5-1.7 0.1-1.5 6-3.3 0.5-1.3 0.6-1.3 1.4-0.8 0.7-0.3 2-1.2 3.4-2.8 3.8-1.4 4.2 0.3 0.7 2 0.9 1.8 1.8 0.6 1.8-0.8 5.2-3.8 1.4-0.7 1.3-0.9 2.2-2.3 2.3 1.6 2.2 3 1.8 2 2.1 1.6 3.3 0.6 3-0.1 2.8 1.8 1.6 4.8 1.3 2.9 2.9 4.4 0.4 1 0.9 1.1 0.7-0.1 1.4 0.2 1 1.4 0.7 1.7-3.3 2.9-0.9 2.6-2.9 3.1-1.8 0.5-3.7 0-4.3-0.5-6.6 0.3-1.1-0.4-2.9-3.6-2.9-0.4-7 3-2.9 0.5-8.6 4.4-2.6 3.6-0.7 4.4-1.9 3.5-1.3 1.3-1.1 1.6-1.3 0.7-1.4 0.2z"
                  id="TR15"
                  name="Burdur"
                ></path>
                <path
                  d="M416 234.6l4.1-1.1 3.9-2.2 4.2-1.2 4.2 0.2 1.8-0.9 4-3.8 0.1-3.6-1.9-5.1 1.6-4.1 0.7 1 1.9 0.4 1.1-0.1 1.8 0.8 0.7 2.5 1.9 1.6 2.4-1.4 1-0.3 2 0.3 1-0.2-0.5 2.5 1.6 5.4-0.8 3.1-1.7 2.7 0.7 3.1 2.2 1.4 2.4 0.7 2-0.4 1 0.3 0.4 2.8-1.4 3.1 0 2.9 0.9 0.8 1.9 1.5 1.6 1.7 1.8 1.1-15.7 23.4-3.5-0.6-3.6 0.8-3.7 1.5-3.7 0.9-3.8-0.1-7.4 0.5-5.6 1.2-2.1-0.1-1.9-1.8-3.2-4-0.7-2.3-0.4-2.5-0.5-0.6-0.6-0.5-1.8-4.6 0.4-5 0.7-2 0.3-2.3 1.1-3.2 1.7-2.6 0.7-4.8 1.1-2 2.7-3.1 0.9-1.7z"
                  id="TR68"
                  name="Aksaray"
                ></path>
                <path
                  d="M484.8 250.7l-5 1.9-2.4-0.7-2-2-1.3-0.7-2.7 0.1-4.1 1.2-2.7-0.4-1.8-1.1-1.6-1.7-1.9-1.5-0.9-0.8 0-2.9 1.4-3.1-0.4-2.8-1-0.3-2 0.4-2.4-0.7-2.2-1.4-0.7-3.1 1.7-2.7 0.8-3.1-1.6-5.4 0.5-2.5 5.3 4.5 1.9-0.5-0.2-1.4-0.5-2.7 1-3.3 5.7-2.4 2.6-1.6 3-1.3 1.9-2.6-1-2.3-1.4-2.5 0.6-4-0.6-3.1-1-1.9-0.4-2.2 0.2-1.4 1-0.6 3 0.8 2.8-0.5 0.7 0.2 1.4 0.6 0.4 0.8 0.2 1.3 0.4 1.2 1.2 1.4 1.5 0.8 1 1.9 0.6 2.4 3.8 2.4 1.1 1.9-0.5 1.4 0.2 1.4 0.4 1.3 0.7 1.5 0.7 1.5-0.7 1.1-0.5 1.3-0.1 1.5 0 1.5-1 3.3-1.3 3.1 2.2 4.4 3.3 4.3-0.2 2.8 0.5 2.8-0.2 2.9-4.2 6.7-1.9 2-1.3 2.6z"
                  id="TR50"
                  name="Nevsehir"
                ></path>
                <path
                  d="M513 139.7l3.3 3.7 3.8 2.6 4.4-0.4 3.7 0.8 2.2-0.2 2.3-0.5 1.9-0.8 2-0.6 2 1.9-0.3 3.5-0.1 2.4 0.9 1.8 0.9 0.9 0.5 1.3 1.4 4.5 2.3 4.1 1.6 0.9 0.5 1.4 0.1 1.4-1.4 5.1-3.4 3.4-4.1 3.2-1.7 2-1.4 2.3-1.2 2.3-1.5 2-2.1 1.6-1.8 2.1-4 5.6-15.4 10-2.1 0.7-5.4 3.4-10.7-1.7-0.7-1.5-0.7-1.5-0.4-1.3-0.2-1.4 0.5-1.4-1.1-1.9-3.8-2.4-0.6-2.4-1-1.9-1.5-0.8-1.2-1.4-0.4-1.2-0.2-1.3-0.4-0.8-1.4-0.6-0.7-0.2-1.3-2.9-2-1.8-2.4-0.4-2.1-1.4-6.6-8-0.6-1.3-0.4-1.4-0.9-1.3-3.7-0.1-2.2-0.7-2.4-2.7-2.5-2.3-2.6-0.1-0.9-0.8-1-1-0.5-1.3 0.2-1.3-0.2-1.3-0.5-1.2 2.7-1.3 7.5-0.9 4.5-2.1 4.6-1.5 7.3 1.3 2.8-1.1 1.9 0.2 3.8-0.2 7.2-3.5 4 1.2 2.2-0.2 3.7-2.3 1.3-1.7 0.1-1.2 0.5-1.4 0.1-1-0.6-0.7 0.5-2.8 4.6-0.4 1.9-0.7 0.4 0.2 0.4 1.3 0.9 0.6 2.1 0.2 2.5-0.2 2.3 0.8z"
                  id="TR66"
                  name="Yozgat"
                ></path>
                <path
                  d="M438 212.8l0.4-2-3-1.1-0.6-0.7-1.6-0.6-2.5-0.2-1-0.7-0.7-1.2-0.7-0.6-0.8-0.3-2.4-2-5.4-3.4-0.9-3.4 5.3-9.3 7.7-6.3 3.3-5.2 2.1-6.3 7.6-7.3 1 1 0.9 0.8 2.6 0.1 2.5 2.3 2.4 2.7 2.2 0.7 3.7 0.1 0.9 1.3 0.4 1.4 0.6 1.3 6.6 8 2.1 1.4 2.4 0.4 2 1.8 1.3 2.9-2.8 0.5-3-0.8-1 0.6-0.2 1.4 0.4 2.2 1 1.9 0.6 3.1-0.6 4 1.4 2.5 1 2.3-1.9 2.6-3 1.3-2.6 1.6-5.7 2.4-1 3.3 0.5 2.7 0.2 1.4-1.9 0.5-5.3-4.5-1 0.2-2-0.3-1 0.3-2.4 1.4-1.9-1.6-0.7-2.5-1.8-0.8-1.1 0.1-1.9-0.4-0.7-1z"
                  id="TR40"
                  name="Kirsehir"
                ></path>
                <path
                  d="M205.4 229.4l2.6-2.3 2.8 0 2.9 1.9 3.2 0.4 3.4-0.1 6-1.7 2.7-0.2 1.2-2.8 0.8-3.5 1.2-0.7 1.4-0.4 3-0.1 5.9 1.4 1.4 0.6 2.2 2.6 0 6.3-0.3 2.5-0.7 2.3-2.9 2.8-0.6 2.4-0.9 2.5-1.1 1.4 0.1 1.7-5.9 1.3-0.5 3.6 0.4 3.6-1.5 1.4-1.7 0.5-3.6 0.1-3.4-0.4-2.4 1.9 0 0.7-0.7 1.2-0.5 0.1-1.2-0.8-0.6 0.3-0.7 0.4-1.5 0.5-1.6-0.1-1.5-1.7-1.7-0.3-2.4 1.7-2.7 0-2.5-1.6-2.9-0.6-1.7-2-1.5 0-1.2-0.9 0.5-0.9 1.3-1.2 0.9-1.5 0.7-1.9 0.3-2.8-0.4-3 0-1.4-0.2-1.4-1.1-1.3-1.3-0.5-0.2-2.1 1.4-0.5 1.4-1.6 0.5-1.2 3.7-2.9 1.7-1.7z"
                  id="TR64"
                  name="Usak"
                ></path>
                <path
                  d="M667.5 148l2 0.1 4.2-0.3 1.9-0.9 1.3 0.8 1.4 0.6 1.4 0.3 1.4 0.3 5.2 3.1 1.8 0.7 1.8 1.1 0.7 1.1 0.8 0.8 2.9 0.9 2.8 1.4 2.9 0.3 2.8-0.3 2.8 0.2 2.7-0.2 2.6-1.3 5.3-1.3 2.1-2.2 1.8-2.7 2.5-1 2.7 0.3 2.4-0.3 1.9-1.8 2.3-0.8 7.7 1.5-1.4 3.3-0.4 3.6 2.5 1.3 8.1 0.1 3.2 4.2-0.1 1.8 0.2 1.7 0.7 1.2 0.8 1.1 1.8 1.4 2 0.8 1.6 1.8-1.1 2.6-1.7 3.1-0.1 3.9-3.1-1.1-3.9-0.1-3.8-0.6-7.1-0.2-0.9-0.5-1.8-0.7-0.5 0-0.2 0.2-1.5 0.4-1.8 0.9-1 0.1-4.4 1.6-3.7-1-2.5-1.5-1.8 0.6-2.2 2.7-3.3 1.1-7.2 1.1-3.8 0.1-3.6-1-3.7-0.2-1.9 1-2 0.5-2.4-0.4-2.5 0.5-4.7 1.8-1.3 1-0.9 1.7-1.2 0.6-1.3-0.1-0.7 1-0.6 1-0.5 0.3-2.9 1.1-0.7 1.9 0.6 1.9 0.7 1.1-1.3 0.8-1.3 0.3-1.1 0.7-0.7 3.5-1.1 0.5-0.2 0-0.4 0.4-0.2 1.4 0.2 1.4 0.4 0.3 0.4 0.4 0.4 3.8-1.5 0.3-1.2 0.6-3.1-3.6-2-1.2-2.2-0.9-4.5-0.2-3.7-1.9 1-1.6 0.4-2.1-0.2-1-0.3-0.9 0.1-1.2 0.2-1.1-0.3-1.8-0.6-1.8 0-2-0.1-1.1-0.2-0.7 0.6-0.1 0.4-0.5 0.4-0.5 0.5-0.5 1.7-1.1 0.4-1-1.1-1.5-1.5-1-1.7-1.6-0.6-2.3 1.1-2.2 0.6-6.4 2.3-3.1 1.2-2.1 1.8-0.9 0.9 0 1-0.2 0.5-1.7-1-1.2-3.5 0.9-3.3 0.1-0.4-1.9 1.8-5.2 2.1-1.2 2.2 1.7 2.3 0.4 2.3-1.6 2.5-0.6 2.1 0.4 2.2 0 1-2.4 0.2-3.1z"
                  id="TR24"
                  name="Erzincan"
                ></path>
                <path
                  d="M666.7 210.6l-0.4-3.8-0.4-0.4-0.4-0.3-0.2-1.4 0.2-1.4 0.4-0.4 0.2 0 1.1-0.5 0.7-3.5 1.1-0.7 1.3-0.3 1.3-0.8-0.7-1.1-0.6-1.9 0.7-1.9 2.9-1.1 0.5-0.3 0.6-1 0.7-1 1.3 0.1 1.2-0.6 0.9-1.7 1.3-1 4.7-1.8 2.5-0.5 2.4 0.4 2-0.5 1.9-1 3.7 0.2 3.6 1 3.8-0.1 7.2-1.1 3.3-1.1 2.2-2.7 1.8-0.6 2.5 1.5 3.7 1 4.4-1.6 1-0.1 1.8-0.9 1.5-0.4 0.2-0.2 0.5 0 1.8 0.7 0.9 0.5 7.1 0.2 3.8 0.6 3.9 0.1-1.8 2.3-2.4 1.1-1.3-0.1-2.2-0.4-0.8 0.7-1.3 2.7-2.2 1.6-2.3 0-2.2 0.8-0.4 1.2-0.4 1-0.7 0.3-3.3-0.2-2.4 0.8-0.7 1.2-0.2 3-0.6 1.6-0.2 1.6 1 4.6-0.4 2.1-0.8 1.9-2.6 0.1-2.3 0.6-0.9 0.6-0.7 1 0.5 2.3 0.3 2.2-0.7 2.9-1.3 2.6-2.9 3.2-4.4 1.7-4.6 0.4-1.7-0.2-1.7 0.4-1.2 1.5-1.4 1.1-1.2-0.4-1.2-0.6-0.8 0.2-0.4 0.1-1.1-1.3-0.9-0.7-2.1-0.6-2-1.5-2.2-1-2-0.2-1.9-0.6-1.5-0.1-1.5-0.9-0.5 0-0.9 0.2-0.5 0.1-2-0.9-3.7 0.3-3.7 1-1.4-0.1-2-2 0-1.7 0.7-1.6-0.1-1.8-1.8-2.7z"
                  id="TR62"
                  name="Tunceli"
                ></path>
                <path
                  d="M272.4 200.3l1.7 0.4 1.3 1.3 0.7 1.9 1.1 1.4 0.8 0.5 0.8 0.6 2.1 1.2 2.2-0.4 1.7-0.8 3.2-2.1 3-3 1.6-1.3 2.4 1.1 2.1 2.4 3.4-1.5 3.5-2.7 1.8-0.5 1.7-1.1 1.7-1.5 1.9-0.5 3.4 3.5 2.3 5.4 3.6 2.7 6.1-0.6 3.7 0.1 1.8 0.5-0.6 4-0.3 0.8-0.2 0.8 1.5 1.5 0.8 0.5-0.4 2.4-3.6 6-1.4 2.9-0.8 3.3 0.1 1.2 0.2 1.2-0.2 1.4-4.1 5.4-3.7 3.8-8.3 5.5-1.2-0.3-1.1-0.7-2.1-3.1-0.9-0.7-0.9-0.6-1.2-0.2-0.9 0.8-2.4 2.6-5.9 4.8-2.9 3.4-3 2.8-3.6 1.4-1.1 0.1-1 0.2-2.6 2.2-1.9 0.5-1.7 0.7-2.4 2.7-2.4 2.2-1.9 1.1-4.3 4.2-3.2 1.9-1 1.9-0.6 6.2-1.6 2.9-3.4 1.9-3.7 0.1-4.2-0.3-3.8 1.4-2.2-3.7-0.5-0.4-0.7-0.2-1.3-0.7-0.6-0.3-2.2 0.1-1.2-0.5 0.1-6.1 0.7-1.8 1.5-0.6 1.5-0.8 12.8-9.6 1.3-1.5-0.7-4-1.3-1.5-5.1-8.6-6.5-1.5-0.1-1.7 1.1-1.4 0.9-2.5 0.6-2.4 2.9-2.8 0.7-2.3 0.3-2.5 9.8-5.9 1.4-1.8 1.9-3.2 1.3-4.1 1.3-2.6 0.7-2.7 0.9-2.5 5.7-2.6 3.3-5.1z"
                  id="TR03"
                  name="Afyonkarahisar"
                ></path>
                <path
                  d="M418.8 196.6l-3.8-7.4-1.9-8.8-1.3-2.3-3.8-4.6-1.2-5.3 1.4-5.2 1.2-5.2 2.2-5.3 3.5-1.5 5.5-0.6 0.6-3.6-0.3-3.7 0-4.2 0.6-3.5 0.6-5.1 13.1 4.2 4.4 0.4 0.6 2.9 1.9 4.5 1.2 1.2 1.2 0.1 0.9 1 0.1 0.7 0 0.7 0.7 1.8-0.2 2.1-1 1.5-1.2 2.5 0 3.2 0.5 1.2 0.2 1.3-0.2 1.3 0.5 1.3-7.6 7.3-2.1 6.3-3.3 5.2-7.7 6.3-5.3 9.3z"
                  id="TR71"
                  name="Kinkkale"
                ></path>
                <path
                  d="M613.5 302.5l-0.1 1.8 0.3 1.7-0.6 2.7-2 1.4-3-0.1-3 0.4-6.3 2.2-6 1.1-2.6 1.3-4.3 4.6-3.7-1.2-1.3-5.3-2.3-1.5-2.3 3.1-5.7 2.6-2.1-3.1-2.6-2.4-1.6-1.1-1.8-0.5-3.9 0.8-1.3 0.3-1.8 0.6-1.6 0.3-0.8-0.9-0.6 0.3-0.5 0.2-0.5 0.2-0.3 0.6-0.5 0.1-1.9-2.4-0.3-4-0.5-0.7-0.6-0.7-0.6-1.7 0.4-1.8 1.3-1.4 1.1-1.8 0-4.7 1.3-1.7 0.9-0.5 1.6-1.6 0.1-2.4-1.5-3.7-0.3-2.9 0.1-1.8 0.6-4 1.4-3.8 2.4-10.7 1.5-3.8 2.6-2.5 1.7-1.8 1.5-2.2 3.7-4.3 1.5-2.4 0.6-3.2 0.9-1.9 1.6-1 6.3-1.5 6.1 0.9 1.2 0.7 1.2 0.6 10.7-0.9-2.8 4.7 0.6 3.6 0.8 0 1.8-0.4 0.9-0.1 7 1.5 6.1 3.6 3.2 1 3.7 1.7 2.4 4.1-0.3 2.5-2.6 6.9-0.3 1.8-0.9 1.4-0.7 0.8-1.1 1.8-2.5 8.8-1.9 3.6-2.3 2.9-2.7 2.3-1.8 2.4-0.1 3.5 1.7 2.8 2.8 1 5.2 3.2z"
                  id="TR46"
                  name="K. Maras"
                ></path>
                <path
                  d="M841.7 182.8l1.4 3.8 1.9 3.4 1.5 1.4 1.5 1.7 0.8 1.7 1.2 1.2 1.7 2.5-0.2 4 0.2 0.8 0.1 0.9-0.5 2.4-0.4 3 0.2 3.2-3.5 2.8-3.9 1.5-7.7-0.9-1.7 0.6-1.5 1.1-1.9 0.8-2.1 0.2-0.9 0.7-0.6 2.8-1 0.9-4.2-0.1-0.9 2.2 1.5 2.4 0.3 2.6-2 1.7-2.8 3.1-3.3 1.9-8.1-0.6-7.3 2.8-0.5 0.8-2.7 0.3-2.6 0.9-1.4-0.5-0.6-1.6 0.6-2.5-0.6-2.4-3.2-2.3-2.4-3.2-0.3-1.6-0.6-1.4-1-0.9-0.6-0.2-1-1.4-1-3.5 1.4-4.8 3.3-4.1 0.6-2.4-0.3-2.7-1.3-2.4-0.9-0.9-1.1-1.9 0.1-1.9 0-1-0.4-1.1-0.3-0.5-0.8-1.8 0.3-1.4 1.3-2.4 7.6-1.3 3.8 0.9 1.8 1.2 4.6 4.6 2.8 3.9 1.1 1 4.1 1.6 4.1 0.2 1.8-1.4 2-1.2 5.8-1 1.5-1.3 0.9-1.1 3.2-2 1.4-2.3-0.3-2.8 1-2.7 6.5-3.3 1.8-0.1 1.8-0.3 0.9-0.3z"
                  id="TR49"
                  name="Mus"
                ></path>
                <path
                  d="M834.5 93.6l1 4.9 2.4 3.7 1.8 1.2 1.3 1.7 1 2.4 1.4 2.1 1.9 1.9 1.4 2.6-0.3 2.1-0.7 2.2 1.1 6.2-1.9 1.2-2 0.9-1.6 1.1-1.5 1.2-3 1.7-6.9 2.4-1.5 3.3 1.7 2 1 1.7 1.1 1.3 4.1 0.7 1.5 0.5 1.4 1 0.8 1.1 0.6 1.3 0.8 0.6 1 0.3 1.9 0.8 1.2 1.9 1.1 2.5 1.4 2.3-1.3 1.5-1.6 0.9-1.2-0.1-0.6 0.1-1.2 0.5-1 1.5-1.7 0.6-3.6-0.6-1.7 0.7 3.9 4.7 6.7 2.8-0.6 0.4-0.5 1.7 0.1 8.3-0.5 2.9-1.5 2.5-0.9 0.3-1.8 0.3-1.8 0.1-6.5 3.3-1 2.7 0.3 2.8-1.4 2.3-3.2 2-0.9 1.1-1.5 1.3-5.8 1-2 1.2-1.8 1.4-4.1-0.2-4.1-1.6-1.1-1-2.8-3.9-4.6-4.6-1.8-1.2-3.8-0.9-7.6 1.3-2-3.5-2.1-2.5-2.9-0.3-3.5-1.7-4.6-4.3-1.5-0.9-1.4 0.7-1.4 1-4.2 1.9-4.2-0.6 0.1-3.9 1.7-3.1 1.1-2.6-1.6-1.8-2-0.8-1.8-1.4-0.8-1.1-0.7-1.2-0.2-1.7 0.1-1.8-3.2-4.2-8.1-0.1-2.5-1.3 0.4-3.6 1.4-3.3 7.8 1 1.7-0.3 2.1-1.6 2-2 2.7-1.3 5.9-2.1 1.3-1.7 1.2-2.2-0.2-1.2-3.1-0.3-1.4-0.6-3.1-0.7-2.9-1.7-1.2-3.8-1.4-8.5-1.5-3.8 7.7-3.8 1.9-0.6 1.9-0.2 2.9-0.8 1.4-3.6 1.7-0.7 2.1 0 2-0.7 5.1-4.4 3.6-2.1 3.7 2.1 5.9 0.9 1.7 0.6 0.4 2.1-1.3 2.2-0.6 5.3 3.2 2.9 4.4-1.7 4-2.6 3.7-1.1 3.8 1.1 4 2.3 2.2-3.3 0.1-2.8 0.9-2.5 1.9-1.9 1.2-2.5-0.3-2.7 0.6-2.5 1.5-1.3 1.6-0.8 3.2-0.3 3.3 0.3 2.8-0.6 2.8-0.3z"
                  id="TR25"
                  name="Erzurum"
                ></path>
                <path
                  d="M597.3 237.1l2.8-1.3 2.4-2.2 2.4-2.9 1.1-0.4 2-1.3 3.4-3.7 0.8-2.3-0.3-1.3-0.6-1.3-0.8-2.7-0.5-2.9 1-2.4 2.1-0.5 4.1 2 3.9-0.9 1.7-1.1 1.8-0.8 1.7-1.1 1.8-0.9 3.7-0.1 1.6-0.7 1.7-0.3 3.2 0.5 3.1-0.9 3.4-2.6 3.7-1.3 3.7 1.9 4.5 0.2 2.2 0.9 2 1.2 3.1 3.6-3.4 0.2-1.3 1.7 0 2.5 0.8 1.6 0.6 1.8 0.2 3.7 0 1.4 0.6 1.8-0.2 0.4 0.1 0.8-0.1 0.6-0.6 0.3-5.2-0.5-1.2 0.2-0.6 0.7 0.3 1-0.3 1.1-0.5 0.9-0.7 0.4-1.2 0.2-0.9 0.5-0.7 0.8-0.6 0.9-0.2 0.7-0.3 1.1-0.7 1.3 0.1 0.4 0.3 0.3 0.2 0.6 0.1 1 0.4 1.2 0.7 1.1 0.7 0.7 0.6-0.6 0.5 0.3 0.8 1.1 0.7 0.6 0.6 0.3 0.6 0.2 2.7 0.4 1.3 0.3 0.2 0.1 0.8 0.8 0.3 0 0.3-0.2 0.4 0 3.8 2 1.4 0.1 2.9-1.1 1.5-0.2 1.1 0.8-0.3 0.5 1.2 0.8 0.4 0.4 1.1 1.8 0 0.3 1.3 0.2 0.8 0.5 1.4 1.3 1.6 0.3 3.9-1.3 1.9 0.2 0.9 0.9 0.6 1.3 0.1 1.4-1.6 5.6-2.1 2.5-2.6 1.2-2 0.5-1.9 0.9-1.3 0.3-1.4 0-3.5 0.7-3.3-0.4-3.3 0-2.8-0.8-0.8-0.5-0.8-0.4-0.3-1.3 1-0.6 1.8-1.8-0.2-1.9-2.8-0.6-2.8 0.8-3.3 0.1-3.5 3.9-3 4.5-3.7 2.1-1.2 2.3-0.1 3.2-2.9 2.6-4 1-1.7 0.8-1.7 0.6-2-0.3-2-0.6-4.4 0.6-4.2 1.7-6.7 1.3 1.9-3.6 2.5-8.8 1.1-1.8 0.7-0.8 0.9-1.4 0.3-1.8 2.6-6.9 0.3-2.5-2.4-4.1-3.7-1.7-3.2-1-6.1-3.6-7-1.5-0.9 0.1-1.8 0.4-0.8 0-0.6-3.6 2.8-4.7z"
                  id="TR44"
                  name="Malatya"
                ></path>
                <path
                  d="M664 211.5l1.2-0.6 1.5-0.3 1.8 2.7 0.1 1.8-0.7 1.6 0 1.7 2 2 1.4 0.1 3.7-1 3.7-0.3 2 0.9 0.5-0.1 0.9-0.2 0.5 0 1.5 0.9 1.5 0.1 1.9 0.6 2 0.2 2.2 1 2 1.5 2.1 0.6 0.9 0.7 1.1 1.3 0.4-0.1 0.8-0.2 1.2 0.6 1.2 0.4 1.4-1.1 1.2-1.5 1.7-0.4 1.7 0.2 4.6-0.4 4.4-1.7 2.9-3.2 1.3-2.6 0.7-2.9-0.3-2.2-0.5-2.3 0.7-1 0.9-0.6 2.3-0.6 2.6-0.1 10.9-5.9 1.6-0.5 3.9-1.7 1.5 2.9-2 3.9-2.4 3.3-1 4.3 1.2 4.8 2.9 3.5 0.8 2-0.3 2.2-0.6 2.4-0.9 1.6-1.6-0.5-1.4-0.3-2.3 6.4 2.6 3.8 0.5 2.7 0.2 2.8-15.9 0.5-0.8-0.3-1.7-1.3-1-0.5-2.2 0.4-0.7 2-1.1 1.9-1.2 1.9-1.4 0.9-1.6 0.6-3.7-0.2-3.6 0.3-1.3 0.8-3.7 0.5-2 0.8-4-0.3-3.9-1-6.6 1.1-1.9-0.2-3.9 1.3-1.6-0.3-1.4-1.3-0.8-0.5-1.3-0.2 0-0.3-1.1-1.8-0.4-0.4-1.2-0.8 0.3-0.5-1.1-0.8-1.5 0.2-2.9 1.1-1.4-0.1-3.8-2-0.4 0-0.3 0.2-0.3 0-0.8-0.8-0.2-0.1-1.3-0.3-2.7-0.4-0.6-0.2-0.6-0.3-0.7-0.6-0.8-1.1-0.5-0.3-0.6 0.6-0.7-0.7-0.7-1.1-0.4-1.2-0.1-1-0.2-0.6-0.3-0.3-0.1-0.4 0.7-1.3 0.3-1.1 0.2-0.7 0.6-0.9 0.7-0.8 0.9-0.5 1.2-0.2 0.7-0.4 0.5-0.9 0.3-1.1-0.3-1 0.6-0.7 1.2-0.2 5.2 0.5 0.6-0.3 0.1-0.6-0.1-0.8 0.2-0.4-0.6-1.8 0-1.4-0.2-3.7-0.6-1.8-0.8-1.6 0-2.5 1.3-1.7 3.4-0.2z"
                  id="TR23"
                  name="Elazig"
                ></path>
                <path
                  d="M851.1 212.8l2-1.3 2.3-0.7 1.6-1 1.6-1.3 8.8-1.9 0.1 4.6 1.4 3.1 5.6 3.4 1.9 3-1.4 4.5-3.4 2.6-2.6 2.8-4.4 6.5-0.8 1.9-1 1.7-3 2.1-5.1 2.9-0.8 0.9-0.5 2.5 0.3 1.3 1.7 3.9 0.2 0.7 0.1 1.6-0.5 2.1 0.9 3.1 2.5 0.9-1.3 4-0.6 0.7-0.5 0.8 0 1.8 0.2 1.9-0.2 2.8-1.4 2.1-1.8 0.2-3.2-2.4-4.8-2.7-3-2.2-2.1-0.9-3.3-0.8-3.4-1.4-3.1-2.4-2.8-2.8-3.2-1.5-4.1 1.6-4.1 1-5.1-0.4-4.9-1.9-0.1-1.3 0.2-1.4 0-2.2-0.2-2.2-1.1-3.3-1.7-8.2-4-1.5 0.5-0.8 7.3-2.8 8.1 0.6 3.3-1.9 2.8-3.1 2-1.7-0.3-2.6-1.5-2.4 0.9-2.2 4.2 0.1 1-0.9 0.6-2.8 0.9-0.7 2.1-0.2 1.9-0.8 1.5-1.1 1.7-0.6 7.7 0.9 3.9-1.5 3.5-2.8z"
                  id="TR13"
                  name="Bitlis"
                ></path>
                <path
                  d="M752.6 179.2l3.1 1.1 4.2 0.6 4.2-1.9 1.4-1 1.4-0.7 1.5 0.9 4.6 4.3 3.5 1.7 2.9 0.3 2.1 2.5 2 3.5-1.3 2.4-0.3 1.4 0.8 1.8 0.3 0.5 0.4 1.1 0 1-0.1 1.9 1.1 1.9 0.9 0.9 1.3 2.4 0.3 2.7-0.6 2.4-3.3 4.1-1.4 4.8 1 3.5 1 1.4 0.6 0.2 1 0.9 0.6 1.4 0.3 1.6-2.3 1.7-5.8 2.2-5.9 3.6-3.1 0.9-8.1-0.3-2.3-0.6-1.7-0.2-4.6 0.7-2.7 1.3-0.3 1.7 0 1.8-1.3 2-1.9 0.9-5.5 0.2-0.2-2.8-0.5-2.7-2.6-3.8 2.3-6.4 1.4 0.3 1.6 0.5 0.9-1.6 0.6-2.4 0.3-2.2-0.8-2-2.9-3.5-1.2-4.8 1-4.3 2.4-3.3 2-3.9-1.5-2.9-3.9 1.7-1.6 0.5-10.9 5.9 0.8-1.9 0.4-2.1-1-4.6 0.2-1.6 0.6-1.6 0.2-3 0.7-1.2 2.4-0.8 3.3 0.2 0.7-0.3 0.4-1 0.4-1.2 2.2-0.8 2.3 0 2.2-1.6 1.3-2.7 0.8-0.7 2.2 0.4 1.3 0.1 2.4-1.1 1.8-2.3z"
                  id="TR12"
                  name="Bingöl"
                ></path>
                <path
                  d="M570.6 317.3l-2.2 2.5-1.8 2.8-2.6 5.1-3.3 4.2-1.8 1.7-1.1 2.2-2.4 0.3-2.4-0.1-1.8-1.1-1.6-1.7-2.1-0.7-2.3 0.6-1.9 0-2-0.6-2.1 0.4 1.4-3.6 0.7-3.1-0.6-3.4-0.4-0.8-1.3-1.1-3.1-0.6-1 0.1-1.4-4.4 0-4.6 3.1-7.7 2.7-6.2 2.2-3.7 4.5-0.3 3.6 0 0 4.7-1.1 1.8-1.3 1.4-0.4 1.8 0.6 1.7 0.6 0.7 0.5 0.7 0.3 4 1.9 2.4 0.5-0.1 0.3-0.6 0.5-0.2 0.5-0.2 0.6-0.3 0.8 0.9 1.6-0.3 1.8-0.6 1.3-0.3 3.9-0.8 1.8 0.5 1.6 1.1 2.6 2.4 2.1 3.1z"
                  id="TR80"
                  name="Osmaniye"
                ></path>
                <path
                  d="M687.8 271l-1.9 1.3-2.9 0.9-0.8 0.4-0.6 0.7-0.2 0.9-0.2 2-0.5 0.9-0.6 0.4-2.3 0.8 0.4 0.9 0.1 0.7-0.3 0.8-0.6 0.9 0.3 0.3 0.1 0.4-0.1 0.5-0.3 0.5-0.3-0.1-0.9-0.3-0.4 0-0.7 0.2-0.7 0.5-0.5 0.8-0.4 0.9 0.7-0.2 1.3-0.9 0.6-0.1 0.5 0.5-0.1 1.5 0.6 0.9-0.9 0.2-1.1 0.1-1 0.2-0.6 0.7 0.4 0.3 1.2 1.3-0.3 0.3-0.4 0.2-0.4 0-0.8-0.6-0.4 0.3-0.2 0.5-0.2 0.2-0.2 0.1-0.3 0.1-0.3 0.1-0.4 0.1-0.2-0.1-0.6-0.6-0.1-0.2-1 0.6 0.9 1 2.3 1.8-10.5 1.6-2.3 2.4-0.5 0.3-0.6 1-2.8 3.1-1 0.6-5.8 1-1.5-0.6-2.8 1-0.5 0-0.3 1.4-0.7 0.3-2.1-0.3-0.4 0.3-0.1 0.8 0.1 0.7-0.2 0.3-0.2 0.1-0.1 0.1-0.1 0.1-0.2 0.1-0.2-0.1-0.1-0.5-0.2-0.2-2.1-0.5-0.9-0.4-0.6-0.8-1.4 1-0.5 0-0.7-0.5-3.7-3.3-5.6-2.1-5.2-0.1-5.2 1.1-5.2-3.2-2.8-1-1.7-2.8 0.1-3.5 1.8-2.4 2.7-2.3 2.3-2.9 6.7-1.3 4.2-1.7 4.4-0.6 2 0.6 2 0.3 1.7-0.6 1.7-0.8 4-1 2.9-2.6 0.1-3.2 1.2-2.3 3.7-2.1 3-4.5 3.5-3.9 3.3-0.1 2.8-0.8 2.8 0.6 0.2 1.9-1.8 1.8-1 0.6 0.3 1.3 0.8 0.4 0.8 0.5 2.8 0.8 3.3 0 3.3 0.4 3.5-0.7 1.4 0 1.3-0.3 1.9-0.9 2-0.5 2.6-1.2 2.1-2.5 1.3 0.8 3.8-1.6 1.5 1.6 0.2 1.6-0.4 1.3-1.1 2.5 0.4 0.8-0.4 0.8-1.7 1.2z"
                  id="TR02"
                  name="Adiyaman"
                ></path>
                <path
                  d="M777.3 287.7l-3 1.5-16.6-0.5-5.2 1.1-0.6 1.4-2.3 3-3.8 0.3-5.2 4.1-2.4 0.9-5.2 3.6-2.1 2-4.6 6-2.2-2.5-1.6-3.1-1.7-4.9-0.2-0.9-0.4-1.7-0.7-4.5 0.3-6.5-0.7-1.5-0.9-0.2-0.8-0.5-1.2-1.1-1.4-0.6-0.4 0.8-0.5 0.7-2.3-1-1.8-1.7-1.6-0.5-1.5-0.8-3.1-2.6-2.8-3-2.6-1.2-2.9-0.1-3.8-0.7-3.7-2 1.7-1.2 0.4-0.8-0.4-0.8 1.1-2.5 0.4-1.3-0.2-1.6-1.5-1.6-3.8 1.6-1.3-0.8 1.6-5.6-0.1-1.4-0.6-1.3-0.9-0.9 6.6-1.1 3.9 1 4 0.3 2-0.8 3.7-0.5 1.3-0.8 3.6-0.3 3.7 0.2 1.6-0.6 1.4-0.9 1.2-1.9 1.1-1.9 0.7-2 2.2-0.4 1 0.5 1.7 1.3 0.8 0.3 15.9-0.5 5.5-0.2 1.9-0.9 1.3-2 0-1.8 0.3-1.7 2.7-1.3 4.6-0.7 1.7 0.2 2.3 0.6 8.1 0.3 3.1-0.9 5.9-3.6 5.8-2.2 2.3-1.7 2.4 3.2 3.2 2.3 0.6 2.4-0.6 2.5 0.6 1.6 1.4 0.5 0.3 2-1.8 1-1.5 0.2-1.5 0.5-2.5 1.2-1.1 1.5-0.4 2.2-1 3.3-0.5 3.5 0.6 6.6-0.4 3.4-0.7 1.4-0.9 1.3-2.2 7.3-1.9 2.2-2.3 1.6-0.5 1.4 0.5 1.7 0.4 2.3 1 1.8z"
                  id="TR21"
                  name="Diyarbakir"
                ></path>
                <path
                  d="M812.6 290.8l-8.8 6.3-5.3 3.9-7.3 1.5-5.1-3.2-3.3-6.9-3.4-4.3-0.9-0.4-1.2 0-1-1.8-0.4-2.3-0.5-1.7 0.5-1.4 2.3-1.6 1.9-2.2 2.2-7.3 0.9-1.3 0.7-1.4 0.4-3.4-0.6-6.6 0.5-3.5 1-3.3 0.4-2.2 1.1-1.5 2.5-1.2 1.5-0.5 1.5-0.2 1.8-1-0.3-2 2.6-0.9 2.7-0.3 4 1.5 1.7 8.2 1.1 3.3 0.2 2.2 0 2.2-0.2 1.4 0.1 1.3-2.1 2.6-1 3.2-2.4 2.7-5.5 1.5-1.5 2.8 0.4 4 5.1 4.7 8.6 3.5 5.1 5.6z"
                  id="TR72"
                  name="Batman"
                ></path>
                <path
                  d="M869.8 289.7l-0.5 3.4-2.9 3-8.1-0.8-10-1.4-9.6-2.5-6.3-0.3-3.7 3.7-9.4-0.7-6.7-3.3-5.1-5.6-8.6-3.5-5.1-4.7-0.4-4 1.5-2.8 5.5-1.5 2.4-2.7 1-3.2 2.1-2.6 4.9 1.9 5.1 0.4 4.1-1 4.1-1.6 3.2 1.5 2.8 2.8 3.1 2.4 3.4 1.4 3.3 0.8 2.1 0.9 3 2.2 4.8 2.7 3.2 2.4 1.8-0.2 1.4-2.1 0.2-2.8-0.2-1.9 0-1.8 0.5-0.8 0.6-0.7 1.3-4 3.7-0.3 3.8 0.7 3.4 1.3 0.5 4.3-0.2 2.5 0.3 2.4-0.2 2.6-0.6 2.4-0.2 3.5-0.7 1.5-0.5 1.6 1.9 4.5z"
                  id="TR56"
                  name="Siirt"
                ></path>
                <path
                  d="M718.4 116.5l1-0.1 0.7-0.8-0.3-1.5 0.6-0.6 0.3-0.2 1.5 0.5 2.9 3.3 1.9 1.1 4.1 0.1 3.9 1.3 2.1 0 2.3-0.8 2.4-0.4 4.9 0 1.9-0.6 0.9-0.2 1.5 3.8 1.4 8.5 1.2 3.8 2.9 1.7 3.1 0.7 1.4 0.6 3.1 0.3 0.2 1.2-1.2 2.2-1.3 1.7-5.9 2.1-2.7 1.3-2 2-2.1 1.6-1.7 0.3-7.8-1-7.7-1.5-2.3 0.8-1.9 1.8-2.4 0.3-2.7-0.3-2.5 1-1.8-3.9-1-4.2 0.2-2 0.6-2 1.5-3.6 0.4-9.1-1.6-9.2z"
                  id="TR69"
                  name="Bayburt"
                ></path>
                <path
                  d="M685.6 103.3l0.9 0.7 1 0.1 1-0.9 0.8-1.1 2 1.2 0.7 3.7 3.2 3.3 11.1 4.5 2.8 0.3 1-0.2 0.7-0.8 0.6-2.6 1.4-1.1 3.3 2.2 2.3 3.9 1.6 9.2-0.4 9.1-1.5 3.6-0.6 2-0.2 2 1 4.2 1.8 3.9-1.8 2.7-2.1 2.2-5.3 1.3-2.6 1.3-2.7 0.2-2.8-0.2-2.8 0.3-2.9-0.3-2.8-1.4-2.9-0.9-0.8-0.8-0.7-1.1-1.8-1.1-1.8-0.7-5.2-3.1-1.4-0.3-1.4-0.3-1.4-0.6-1.3-0.8 0.4-2-1.3-1.7-0.4-2.2 0.2-2.2-0.2-2.1 0.1-2.1 1-1.3 4.8-2.6 1.4-1.2 0.3-2.3-1.1-0.3-1.3-1.3-1.3-1.1-5.6-1.4-1.3-1.6-0.1-2.2 0.3-2.8 0.1-0.5 0.5-0.7 1.4-1.5 0.9-1.7-0.6-1.9 0.6-1.6 1.5-1 1.4-1.2 1.2-0.3 0.9-0.5 0.2-0.9 0.3-0.7 5.7-0.7z"
                  id="TR29"
                  name="Gümüshane"
                ></path>
              </g>
              <g id="points">
                <circle
                  class="36.13372868983344|26.620445987274163"
                  cx="90.9"
                  cy="384.7"
                  id="0"
                ></circle>
                <circle
                  class="38.645329944972325|36.192312755397474"
                  cx="545.5"
                  cy="234.5"
                  id="1"
                ></circle>
                <circle
                  class="41.78483151389593|43.84980616989612"
                  cx="909.1"
                  cy="39.2"
                  id="2"
                ></circle>
              </g>
              <g id="label_points">
                <circle class="Ardahan" cx="859.9" cy="77.1" id="TR75"></circle>
                <circle class="Artvin" cx="807.8" cy="83.6" id="TR08"></circle>
                <circle class="Sirnak" cx="843.9" cy="306.9" id="TR73"></circle>
                <circle
                  class="Hakkari"
                  cx="925.7"
                  cy="298.3"
                  id="TR30"
                ></circle>
                <circle class="Iğdir" cx="915.7" cy="161.9" id="TR76"></circle>
                <circle class="Agri" cx="869.6" cy="173.1" id="TR04"></circle>
                <circle class="Van" cx="900.1" cy="246.9" id="TR65"></circle>
                <circle
                  class="Kirklareli"
                  cx="126.1"
                  cy="47.6"
                  id="TR39"
                ></circle>
                <circle class="Edirne" cx="87.9" cy="81.4" id="TR22"></circle>
                <circle class="Kars" cx="875.3" cy="126.2" id="TR36"></circle>
                <circle class="Mardin" cx="770.8" cy="307.9" id="TR47"></circle>
                <circle
                  class="Sanliurfa"
                  cx="688.5"
                  cy="321"
                  id="TR63"
                ></circle>
                <circle class="Kilis" cx="586.2" cy="344.3" id="TR79"></circle>
                <circle
                  class="Gaziantep"
                  cx="604.7"
                  cy="330"
                  id="TR27"
                ></circle>
                <circle class="Hatay" cx="546" cy="370.8" id="TR31"></circle>
                <circle
                  class="Istanbul"
                  cx="176.3"
                  cy="73.8"
                  id="TR34"
                ></circle>
                <circle
                  class="Tekirdag"
                  cx="115.5"
                  cy="87.1"
                  id="TR59"
                ></circle>
                <circle
                  class="Çanakkale"
                  cx="100.3"
                  cy="147.9"
                  id="TR17"
                ></circle>
                <circle class="Rize" cx="768.1" cy="92.4" id="TR53"></circle>
                <circle class="Trabzon" cx="716.4" cy="101" id="TR61"></circle>
                <circle
                  class="Giresun"
                  cx="656.3"
                  cy="115.1"
                  id="TR28"
                ></circle>
                <circle class="Ordu" cx="609.4" cy="101.2" id="TR52"></circle>
                <circle class="Samsun" cx="529.3" cy="74" id="TR55"></circle>
                <circle class="Sinop" cx="483.6" cy="48.4" id="TR57"></circle>
                <circle class="Kastamonu" cx="421" cy="56.5" id="TR37"></circle>
                <circle class="Bartın" cx="369.3" cy="53.1" id="TR74"></circle>
                <circle
                  class="Zinguldak"
                  cx="343.3"
                  cy="71.5"
                  id="TR67"
                ></circle>
                <circle class="Düzce" cx="311.3" cy="95.8" id="TR81"></circle>
                <circle
                  class="Sakarya"
                  cx="274.8"
                  cy="107.5"
                  id="TR54"
                ></circle>
                <circle class="Kocaeli" cx="254.3" cy="93.2" id="TR41"></circle>
                <circle class="Yalova" cx="215.4" cy="114.7" id="TR77"></circle>
                <circle class="Bursa" cx="205.6" cy="151.1" id="TR16"></circle>
                <circle
                  class="Balikesir"
                  cx="151.2"
                  cy="166.5"
                  id="TR10"
                ></circle>
                <circle class="Izmir" cx="127.6" cy="259" id="TR35"></circle>
                <circle class="Aydin" cx="157" cy="289.6" id="TR09"></circle>
                <circle class="Mugla" cx="184.9" cy="328.4" id="TR48"></circle>
                <circle
                  class="Antalya"
                  cx="260.9"
                  cy="341.5"
                  id="TR07"
                ></circle>
                <circle class="Mersin" cx="432.7" cy="353.6" id="TR33"></circle>
                <circle class="Adana" cx="511.6" cy="315.2" id="TR01"></circle>
                <circle class="Bolu" cx="331.9" cy="112.6" id="TR14"></circle>
                <circle class="Ankara" cx="374.2" cy="163.1" id="TR06"></circle>
                <circle
                  class="Bilecik"
                  cx="254.6"
                  cy="145.8"
                  id="TR11"
                ></circle>
                <circle
                  class="Eskisehir"
                  cx="302.9"
                  cy="174.3"
                  id="TR26"
                ></circle>
                <circle class="Çankiri" cx="422" cy="108.9" id="TR18"></circle>
                <circle class="Karabük" cx="378.4" cy="77.9" id="TR78"></circle>
                <circle class="Tokat" cx="568.3" cy="122.1" id="TR60"></circle>
                <circle class="Sivas" cx="600.3" cy="177.5" id="TR58"></circle>
                <circle class="Çorum" cx="471.6" cy="120.2" id="TR19"></circle>
                <circle class="Amasya" cx="520.5" cy="110" id="TR05"></circle>
                <circle
                  class="Kütahya"
                  cx="231.8"
                  cy="194.5"
                  id="TR43"
                ></circle>
                <circle class="Konya" cx="368.1" cy="267.4" id="TR42"></circle>
                <circle
                  class="Karaman"
                  cx="398.4"
                  cy="323.7"
                  id="TR70"
                ></circle>
                <circle class="Nigde" cx="472.6" cy="272.5" id="TR51"></circle>
                <circle
                  class="Kayseri"
                  cx="519.8"
                  cy="237.9"
                  id="TR38"
                ></circle>
                <circle
                  class="Isparta"
                  cx="299.1"
                  cy="277.4"
                  id="TR32"
                ></circle>
                <circle class="Manisa" cx="175.1" cy="230.6" id="TR45"></circle>
                <circle
                  class="Denizli"
                  cx="216.6"
                  cy="283.6"
                  id="TR20"
                ></circle>
                <circle class="Burdur" cx="247.3" cy="305" id="TR15"></circle>
                <circle
                  class="Aksaray"
                  cx="434.1"
                  cy="252.8"
                  id="TR68"
                ></circle>
                <circle
                  class="Nevsehir"
                  cx="473.8"
                  cy="226.7"
                  id="TR50"
                ></circle>
                <circle class="Yozgat" cx="504.6" cy="176.7" id="TR66"></circle>
                <circle
                  class="Kirsehir"
                  cx="448.9"
                  cy="192.6"
                  id="TR40"
                ></circle>
                <circle class="Usak" cx="223.2" cy="242.7" id="TR64"></circle>
                <circle
                  class="Erzincan"
                  cx="675.3"
                  cy="168.3"
                  id="TR24"
                ></circle>
                <circle class="Tunceli" cx="704.7" cy="204" id="TR62"></circle>
                <circle
                  class="Afyonkarahisar"
                  cx="277.4"
                  cy="233.6"
                  id="TR03"
                ></circle>
                <circle
                  class="Kinkkale"
                  cx="424.4"
                  cy="166.1"
                  id="TR71"
                ></circle>
                <circle
                  class="K. Maras"
                  cx="580.1"
                  cy="277.2"
                  id="TR46"
                ></circle>
                <circle class="Mus" cx="808.6" cy="219.4" id="TR49"></circle>
                <circle class="Erzurum" cx="799.5" cy="154" id="TR25"></circle>
                <circle
                  class="Malatya"
                  cx="630.1"
                  cy="234.9"
                  id="TR44"
                ></circle>
                <circle class="Elazig" cx="686.5" cy="236.8" id="TR23"></circle>
                <circle class="Bitlis" cx="837.3" cy="242.3" id="TR13"></circle>
                <circle class="Bingöl" cx="763.2" cy="210.5" id="TR12"></circle>
                <circle
                  class="Osmaniye"
                  cx="550.1"
                  cy="322.8"
                  id="TR80"
                ></circle>
                <circle
                  class="Adiyaman"
                  cx="654.1"
                  cy="282.2"
                  id="TR02"
                ></circle>
                <circle
                  class="Diyarbakir"
                  cx="737.1"
                  cy="269.4"
                  id="TR21"
                ></circle>
                <circle class="Batman" cx="794.5" cy="257.1" id="TR72"></circle>
                <circle class="Siirt" cx="822.5" cy="277.5" id="TR56"></circle>
                <circle
                  class="Bayburt"
                  cx="736.9"
                  cy="133.7"
                  id="TR69"
                ></circle>
                <circle
                  class="Gümüshane"
                  cx="698.7"
                  cy="135"
                  id="TR29"
                ></circle>
              </g>
            </svg>
          </div>
          <div id="mapPin" class="map-pin">
            <div class="pin-pulse"></div>
            <div class="pin-icon"></div>
            <div class="pin-label">Bahçemiz burada</div>
          </div>
        </div>
      </div>
    </div>

    <div
      class="parallax-section"
      style="
        background-image: url('https://www.dogalyasam.net/image/catalog/icons/new/olivePhoto0006.jpg');
      "
    >
      <div class="parallax-overlay"></div>
      <div class="content-wrapper" data-aos="fade-in">
        <h2>Zeytinin Anavatanından: Amanos Dağları</h2>
        <p>
          Zeytinin ana yurdu olan Doğu Akdeniz, yüzyıllardır bu mucizevi
          meyvenin yetiştiği kadim topraklara ev sahipliği yapıyor.
        </p>
        <p>
          Amanos Dağları'nın eteklerinde, Akyar bölgesinde yer alan
          zeytinliklerimiz, zengin mineralli topraklar, tertemiz hava ve doğal
          kaynak sularıyla birleşerek eşsiz bir aroma sunuyor.
        </p>
        <p>
          Bu coğrafyada yetişen zeytinler, erken hasat edilip titizlikle
          işlenerek hem lezzet hem de şifa açısından üstün kalitede zeytinyağına
          dönüşüyor.
        </p>
      </div>
    </div>

    <div class="process-section">
      <div class="container">
        <div class="process-timeline">
          <h2 data-aos="fade-up">Soğuk Sıkım Zeytinyağı Üretim Süreci</h2>
          <p class="section-subtitle" data-aos="fade-up" data-aos-delay="100">
            Zeytinyağımızın kalitesini ve lezzetini belirleyen titiz üretim
            aşamaları
          </p>
          <div class="timeline-container">
            <div class="timeline-line"></div>
            <div class="timeline-item" data-aos="fade-up" data-aos-delay="100">
              <h3>1. Erken Hasat</h3>
              <p>
                Zeytinler, tam olgunlaşmadan, yeşilden mora dönmeye başladığı
                dönemde elle toplanır. Bu dönemde toplanan zeytinler daha az yağ
                içerir ancak polifenol ve antioksidan değerleri çok daha
                yüksektir.
              </p>
            </div>
            <div class="timeline-item" data-aos="fade-up" data-aos-delay="200">
              <h3>2. Hızlı İşleme</h3>
              <p>
                Toplanan zeytinler, oksidasyonu önlemek için 24 saat içinde
                işlenir. Bu hızlı işleme, zeytinyağının tazeliğini ve besin
                değerlerini korur.
              </p>
            </div>
            <div class="timeline-item" data-aos="fade-up" data-aos-delay="300">
              <h3>3. Soğuk Sıkım</h3>
              <p>
                Zeytinler, 27°C'nin altında sıkılarak yağı çıkarılır. Düşük
                sıcaklık, zeytinyağının içindeki değerli besin maddelerinin
                korunmasını sağlar.
              </p>
            </div>
            <div class="timeline-item" data-aos="fade-up" data-aos-delay="400">
              <h3>4. Doğal Filtreleme</h3>
              <p>
                Elde edilen zeytinyağı, doğal yöntemlerle filtrelenir. Kimyasal
                işlem uygulanmaz, böylece doğal aroması ve besin değerleri
                korunur.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="benefits-section">
      <div class="container">
        <h2 class="section-title" data-aos="fade-up">
          Neden Soğuk Sıkım Zeytinyağı Tercih Etmelisiniz?
        </h2>
        <p class="section-subtitle" data-aos="fade-up" data-aos-delay="100">
          Soğuk sıkım zeytinyağının sağlığınıza ve mutfağınıza sunduğu eşsiz
          faydalar
        </p>
        <div class="benefits-grid">
          <div class="benefit-card" data-aos="fade-up" data-aos-delay="100">
            <div class="benefit-icon">
              <img
                src="https://www.dogalyasam.net/image/catalog/icons/heart.png"
                alt="Kalp Sağlığı"
              />
            </div>
            <h3>Kalp Sağlığı</h3>
            <p>
              İçerdiği tekli doymamış yağ asitleri sayesinde kalp-damar
              sağlığını destekler, kolesterol seviyelerini dengeler.
            </p>
          </div>
          <div class="benefit-card" data-aos="fade-up" data-aos-delay="200">
            <div class="benefit-icon">
              <img
                src="https://www.dogalyasam.net/image/catalog/icons/antioxidant.png"
                alt="Antioksidan Kaynağı"
              />
            </div>
            <h3>Antioksidan Kaynağı</h3>
            <p>
              Yüksek polifenol içeriği ile güçlü antioksidan etki gösterir,
              hücreleri serbest radikallere karşı korur.
            </p>
          </div>
          <div class="benefit-card" data-aos="fade-up" data-aos-delay="300">
            <div class="benefit-icon">
              <img
                src="https://www.dogalyasam.net/image/catalog/icons/digestion.png"
                alt="Sindirim Sistemi"
              />
            </div>
            <h3>Sindirim Sistemi</h3>
            <p>
              Sindirim sistemini destekler, mide asidini dengeler ve bağırsak
              sağlığına katkıda bulunur.
            </p>
          </div>
          <div class="benefit-card" data-aos="fade-up" data-aos-delay="400">
            <div class="benefit-icon">
              <img
                src="https://www.dogalyasam.net/image/catalog/icons/taste.png"
                alt="Gerçek Meyvemsi Tat"
              />
            </div>
            <h3>Gerçek Meyvemsi Tat</h3>
            <p>
              Damakta kalan hoş ve doğal bir aroması vardır. Yüksek ısıda
              işlenen zeytinyağlarında bulunamayan zengin tat profili sunar.
            </p>
          </div>
          <div class="benefit-card" data-aos="fade-up" data-aos-delay="500">
            <div class="benefit-icon">
              <img
                src="https://www.dogalyasam.net/image/catalog/icons/shelf-life.png"
                alt="Uzun Raf Ömrü"
              />
            </div>
            <h3>Dayanıklıdır</h3>
            <p>
              Raf ömrü uzundur, doğru şekilde saklandığında kolay bozulmaz.
              Antioksidan içeriği sayesinde uzun süre tazeliğini korur.
            </p>
          </div>
          <div class="benefit-card" data-aos="fade-up" data-aos-delay="600">
            <div class="benefit-icon">
              <img
                src="https://www.dogalyasam.net/image/catalog/icons/natural.png"
                alt="Doğal Şifa"
              />
            </div>
            <h3>Doğal Şifa Kaynağı</h3>
            <p>
              Aç karnına içilebilecek kadar saftır. Özellikle erken hasat soğuk
              sıkım zeytinyağı, vücut direncini artırır.
            </p>
          </div>
        </div>
        <div class="benefits-footer">
          <p class="benefits-note" data-aos="fade-up" data-aos-delay="700">
            Özellikle erken hasat soğuk sıkım zeytinyağı, vücut direncini
            artırır, sindirimi destekler, kalp-damar sağlığını korumaya yardımcı
            olur.
          </p>
        </div>
      </div>
    </div>

    <!-- Detaylı Analiz Bölümü -->
    <div class="analysis-section">
      <div class="container">
        <div class="title-container">
          <h2 class="section-title" data-aos="fade-up">
            Zeytinyağımızın Detaylı Analizi
          </h2>
        </div>
        <p class="section-subtitle" data-aos="fade-up" data-aos-delay="100">
          Laboratuvar analiz sonuçları ve besin değerleri
        </p>

        <!-- Forbes Style Leaderboard Analysis -->
        <div class="analysis-boards" data-aos="fade-up" data-aos-delay="300">
          <!-- Besin Değerleri Board -->
          <div class="analysis-board analysis-board--blur">
            <header class="board-header board-header--nutrition">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink"
                fill="#fff"
                height="50px"
                width="50px"
                version="1.1"
                id="Capa_1"
                viewBox="0 0 378.928 378.928"
                xml:space="preserve"
              >
                <path
                  id="XMLID_1375_"
                  d="M7.965,325.71c0.078,1.295,0.427,2.512,0.991,3.598c0.529,1.021,1.262,1.956,2.192,2.738  c0.122,0.104,0.247,0.203,0.375,0.3c11.251,8.624,38.103,11.699,59.52,11.699c2.297,0,4.633-0.034,6.941-0.103  c4.968-0.146,8.877-4.293,8.731-9.262s-4.307-8.898-9.261-8.73c-2.132,0.063-4.289,0.095-6.411,0.095  c-22.922,0-38.302-3.317-45.262-6.216l-7.151-220.16c2.277,0.861,4.76,1.662,7.45,2.403c13.311,3.665,30.85,5.684,49.389,5.684  c5.063,0,10.053-0.15,14.902-0.444l-3.548,116.575c-0.151,4.968,3.753,9.118,8.722,9.27c4.972,0.17,9.119-3.754,9.27-8.723  l3.621-118.978c5.947-0.888,11.478-2.022,16.422-3.384c2.698-0.743,5.188-1.548,7.471-2.412l-2.606,83.414  c-0.155,4.969,3.747,9.122,8.714,9.276c4.982,0.197,9.121-3.746,9.277-8.714l3.196-102.307c0.017-0.291,0.025-0.584,0.025-0.879  c0-7.645-5.566-13.835-16.544-18.4c-4.586-1.909-9.856,0.265-11.766,4.854c-1.909,4.59,0.265,9.857,4.854,11.767  c1.643,0.683,2.851,1.305,3.729,1.827c-3.609,2.142-11.133,4.751-22.217,6.652l2.308-75.82c0.151-4.968-3.753-9.118-8.722-9.27  c-4.946-0.128-9.119,3.755-9.27,8.723l-1.306,42.91c-5.384-0.364-10.915-0.549-16.534-0.549c-18.539,0-36.078,2.019-49.389,5.685  C8.829,63.58,0.056,70.824,0,80.36c0,0.052,0,0.104,0,0.156c0.001,0.255,0.008,0.508,0.022,0.76l7.929,244.119  C7.954,325.501,7.958,325.605,7.965,325.71z M75.467,71.145c5.45,0,10.799,0.186,15.986,0.555l-0.534,17.55  c-4.78,0.323-9.935,0.506-15.452,0.506c-30.077,0-49.383-5.435-55.821-9.305C26.084,76.581,45.391,71.145,75.467,71.145z   M41.299,217.208c-3.515-3.515-3.514-9.213,0.001-12.728c3.516-3.513,9.214-3.514,12.728,0.001l3.269,3.27  c3.515,3.515,3.514,9.213-0.001,12.728c-1.757,1.757-4.06,2.636-6.363,2.636c-2.304,0-4.607-0.879-6.364-2.637L41.299,217.208z   M207.794,104.228c1.429-10.193,6.736-20.885,11.868-31.225c12.053-24.283,14.133-32.772,3.671-38.57  c-4.348-2.41-5.918-7.888-3.509-12.235c2.409-4.347,7.888-5.915,12.234-3.509c12.108,6.711,17.508,17.435,15.615,31.011  c-1.426,10.227-6.745,20.943-11.889,31.307c-12.012,24.201-14.094,32.655-3.726,38.402c4.347,2.41,5.918,7.888,3.508,12.235  c-1.644,2.965-4.714,4.639-7.88,4.639c-1.476,0-2.973-0.364-4.355-1.131C211.269,128.465,205.896,117.771,207.794,104.228z   M125.414,361.879c1.068,4.854-2.001,9.656-6.855,10.725c-12.502,2.751-28.859,4.266-46.058,4.266  c-23.595,0-53.145-3.02-65.32-11.493c-4.08-2.84-5.085-8.448-2.245-12.528c2.839-4.079,8.449-5.084,12.528-2.245  c5.758,4.008,27.003,8.267,55.037,8.267c15.937,0,30.92-1.365,42.19-3.846C119.544,353.96,124.346,357.025,125.414,361.879z   M372.192,253.614c-2.105-1.823-4.937-2.57-7.667-2.02c-6.877,1.384-13.934,2.658-21.141,3.821c-1.602-2.726-3.55-5.24-5.831-7.521  l-15.847-15.846c14.773-4.678,28.455-13.849,38.946-26.877c21.39-26.564,24.282-62.652,7.198-89.801  c-1.068-1.698-2.672-2.99-4.559-3.672c-30.166-10.896-64.806-0.373-86.197,26.191c-19.931,24.752-23.805,57.768-10.422,84.117  l-1.867,2.318c-3.118,3.871-2.506,9.537,1.365,12.654c3.872,3.119,9.538,2.505,12.654-1.365l1.867-2.317  c6.288,1.638,12.709,2.392,19.119,2.311l22.8,22.799c-30.18,3.843-62.477,5.836-95.317,5.836c-32.84,0-65.138-1.994-95.317-5.836  l81.335-81.334c6.176-6.176,15.857-7.542,23.545-3.319c4.355,2.393,9.828,0.802,12.221-3.556c2.393-4.356,0.801-9.828-3.555-12.222  c-14.66-8.051-33.14-5.433-44.939,6.369l-83.551,83.55c-2.279,2.28-4.227,4.795-5.828,7.52c-7.206-1.163-14.263-2.438-21.14-3.821  c-2.73-0.546-5.561,0.196-7.667,2.02s-3.248,4.519-3.095,7.3c2.645,47.948,42.008,87.876,96.435,103.581  c-0.421,1.041-0.653,2.179-0.653,3.371c0,4.971,4.029,9,9,9H270.5c4.971,0,9-4.029,9-9c0-1.192-0.231-2.33-0.652-3.371  c54.426-15.705,93.792-55.633,96.439-103.581C375.44,258.132,374.297,255.437,372.192,253.614z M293.529,217.356l36.06-44.783  c3.118-3.871,2.506-9.537-1.365-12.654c-3.87-3.117-9.537-2.506-12.654,1.365l-36.06,44.783  c-6.006-18.569-1.915-40.096,11.605-56.887c15.862-19.699,40.861-28.106,63.089-21.527c11.173,20.315,8.29,46.533-7.57,66.23  c-11.165,13.864-26.854,22.137-42.92,23.525C303.045,217.382,296.25,217.624,293.529,217.356z M318.341,325.619  c-24.5,18.078-56.834,28.034-91.047,28.034s-66.547-9.956-91.047-28.034c-20.002-14.759-32.974-33.628-37.395-54.042  c5.285,0.94,10.653,1.82,16.102,2.64c0.161,0.028,0.321,0.053,0.481,0.072c34.942,5.228,73.056,7.955,111.859,7.955  c38.806,0,76.922-2.729,111.868-7.956c0.154-0.02,0.308-0.043,0.462-0.069c5.452-0.82,10.826-1.7,16.113-2.642  C351.315,291.991,338.343,310.86,318.341,325.619z M64.629,184.984c-1.757,1.757-4.06,2.635-6.363,2.635  c-2.304,0-4.607-0.879-6.365-2.637c-3.514-3.516-3.514-9.214,0.002-12.729l3.271-3.27c3.515-3.515,9.214-3.513,12.728,0.002  c3.514,3.516,3.514,9.214-0.002,12.729L64.629,184.984z M41.409,145.746l-3.269-3.27c-3.515-3.515-3.514-9.213,0.001-12.728  c3.515-3.514,9.213-3.515,12.728,0.001l3.269,3.27c3.515,3.515,3.514,9.213-0.001,12.728c-1.757,1.757-4.06,2.636-6.363,2.636  C45.469,148.382,43.166,147.503,41.409,145.746z"
                />
              </svg>
              <h2 class="board-title">
                <span class="board-title--top">Besin Değerleri</span>
                <span class="board-title--bottom">100g için</span>
              </h2>
            </header>

            <main class="board-profiles board-profiles--blur">
              <article class="board-profile">
                <div class="board-profile-icon nutrition-icon">E</div>
                <span class="board-profile-name">Enerji</span>
                <span class="board-profile-value">884<span>kcal</span></span>
                <div class="board-tooltip">
                  Zeytinyağının enerji değeri, yüksek kaliteli yağ asitleri
                  içeriğinden gelir. Bu enerji, vücudunuz için sağlıklı bir
                  yakıt kaynağıdır.
                </div>
              </article>

              <article class="board-profile">
                <div class="board-profile-icon nutrition-icon">Y</div>
                <span class="board-profile-name">Toplam Yağ</span>
                <span class="board-profile-value">100<span>g</span></span>
                <div class="board-tooltip">
                  Zeytinyağı, %100 saf yağdır ve hiçbir katkı maddesi içermez.
                  Doğal ve sağlıklı bir yağ kaynağıdır.
                </div>
              </article>

              <article class="board-profile">
                <div class="board-profile-icon nutrition-icon">D</div>
                <span class="board-profile-name">Doymuş Yağ</span>
                <span class="board-profile-value">14<span>g</span></span>
                <div class="board-tooltip">
                  Zeytinyağı, diğer bitkisel yağlara göre daha düşük doymuş yağ
                  içerir. Bu, kalp sağlığı için olumlu bir özelliktir.
                </div>
              </article>

              <article class="board-profile">
                <div class="board-profile-icon nutrition-icon">T</div>
                <span class="board-profile-name">Tekli Doymamış Yağ</span>
                <span class="board-profile-value">73<span>g</span></span>
                <div class="board-tooltip">
                  Zeytinyağının en değerli özelliği, yüksek tekli doymamış yağ
                  asidi (oleik asit) içeriğidir. Bu yağ asitleri, kalp-damar
                  sağlığını destekler.
                </div>
              </article>

              <article class="board-profile">
                <div class="board-profile-icon nutrition-icon">Ç</div>
                <span class="board-profile-name">Çoklu Doymamış Yağ</span>
                <span class="board-profile-value">11<span>g</span></span>
                <div class="board-tooltip">
                  Omega-3 ve Omega-6 gibi esansiyel yağ asitlerini içerir. Bu
                  yağ asitleri vücut tarafından üretilemez ve dışarıdan alınması
                  gerekir.
                </div>
              </article>

              <article class="board-profile">
                <div class="board-profile-icon nutrition-icon">E</div>
                <span class="board-profile-name">E Vitamini</span>
                <span class="board-profile-value">14.4<span>mg</span></span>
                <div class="board-tooltip">
                  E vitamini güçlü bir antioksidandır. Hücreleri serbest
                  radikallerin zararlı etkilerinden korur ve bağışıklık
                  sistemini güçlendirir.
                </div>
              </article>

              <article class="board-profile">
                <div class="board-profile-icon nutrition-icon">K</div>
                <span class="board-profile-name">K Vitamini</span>
                <span class="board-profile-value">60.2<span>μg</span></span>
                <div class="board-tooltip">
                  K vitamini, kan pıhtılaşması ve kemik sağlığı için önemlidir.
                  Zeytinyağı, K vitamini için iyi bir kaynaktır.
                </div>
              </article>

              <article class="board-profile">
                <div class="board-profile-icon nutrition-icon">P</div>
                <span class="board-profile-name">Polifenoller</span>
                <span class="board-profile-value">250<span>mg/kg</span></span>
                <div class="board-tooltip">
                  Polifenoller, zeytinyağının en değerli bileşenleridir. Güçlü
                  antioksidan etkileri vardır ve erken hasat zeytinyağlarında
                  daha yüksek miktarda bulunur.
                </div>
              </article>
            </main>
          </div>

          <!-- Analiz Raporu Board -->
          <div class="analysis-board analysis-board--blur">
            <header class="board-header board-header--analysis">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                class="board-icon"
              >
                <path
                  d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V5h14v14zM7 10h2v7H7zm4-3h2v10h-2zm4 6h2v4h-2z"
                />
              </svg>
              <h2 class="board-title">
                <span class="board-title--top">Analiz Raporu</span>
                <span class="board-title--bottom">Laboratuvar Sonuçları</span>
              </h2>
            </header>

            <main class="board-profiles board-profiles--blur">
              <article class="board-profile">
                <div class="board-profile-icon analysis-icon">A</div>
                <span class="board-profile-name">Serbest Asitlik</span>
                <span class="board-profile-value analysis-value"
                  >0.3-0.6<span>%</span></span
                >
                <div class="board-tooltip">
                  Serbest asitlik, zeytinyağının kalitesini belirleyen en önemli
                  parametredir. Düşük asitlik, yüksek kaliteyi gösterir. Natürel
                  Sızma Zeytinyağı için maksimum değer %0.8'dir.
                </div>
              </article>

              <article class="board-profile">
                <div class="board-profile-icon analysis-icon">P</div>
                <span class="board-profile-name">Peroksit Değeri</span>
                <span class="board-profile-value analysis-value"
                  >< 10<span>meq O₂/kg</span></span
                >
                <div class="board-tooltip">
                  Peroksit değeri, zeytinyağının oksidasyonunu gösterir. Düşük
                  değer, yağın daha taze ve daha az okside olduğunu gösterir.
                </div>
              </article>

              <article class="board-profile">
                <div class="board-profile-icon analysis-icon">K</div>
                <span class="board-profile-name">K232 Değeri</span>
                <span class="board-profile-value analysis-value">1.85</span>
                <div class="board-tooltip">
                  K232 değeri, zeytinyağının oksidasyonunun bir başka
                  göstergesidir. 232 nm dalga boyunda ölçülür ve düşük değerler
                  daha iyidir.
                </div>
              </article>

              <article class="board-profile">
                <div class="board-profile-icon analysis-icon">K</div>
                <span class="board-profile-name">K270 Değeri</span>
                <span class="board-profile-value analysis-value">0.15</span>
                <div class="board-tooltip">
                  K270 değeri, 270 nm dalga boyunda ölçülür ve zeytinyağının
                  saflığını gösterir. Düşük değerler, yağın rafine edilmediğini
                  gösterir.
                </div>
              </article>

              <article class="board-profile">
                <div class="board-profile-icon analysis-icon">D</div>
                <span class="board-profile-name">Delta-K</span>
                <span class="board-profile-value analysis-value">0.002</span>
                <div class="board-tooltip">
                  Delta-K, zeytinyağının saflığını kontrol etmek için kullanılan
                  bir başka parametredir. Düşük değerler, yağın rafine
                  edilmediğini gösterir.
                </div>
              </article>

              <article class="board-profile">
                <div class="board-profile-icon analysis-icon">N</div>
                <span class="board-profile-name">Nem ve Uçucu Madde</span>
                <span class="board-profile-value analysis-value"
                  >< 0.1<span>%</span></span
                >
                <div class="board-tooltip">
                  Zeytinyağındaki nem ve uçucu madde miktarı, yağın saflığını ve
                  kalitesini etkiler. Düşük değerler daha iyidir.
                </div>
              </article>

              <article class="board-profile">
                <div class="board-profile-icon analysis-icon">S</div>
                <span class="board-profile-name">Çözünmeyen Safsızlıklar</span>
                <span class="board-profile-value analysis-value"
                  >< 0.05<span>%</span></span
                >
                <div class="board-tooltip">
                  Zeytinyağındaki çözünmeyen safsızlıklar, filtreleme kalitesini
                  gösterir. Düşük değerler, yağın iyi filtrelendiğini gösterir.
                </div>
              </article>

              <article class="board-profile">
                <div class="board-profile-icon analysis-icon">K</div>
                <span class="board-profile-name">Kategori</span>
                <span class="board-profile-value analysis-value"
                  >Natürel Sızma</span
                >
                <div class="board-tooltip">
                  Zeytinyağımız, en yüksek kalite kategorisi olan "Natürel Sızma
                  Zeytinyağı" sınıfındadır. Bu, hiçbir kimyasal işlem görmeden,
                  sadece mekanik yöntemlerle elde edildiği ve tüm kalite
                  kriterlerini karşıladığı anlamına gelir.
                </div>
              </article>
            </main>
          </div>
        </div>
      </div>
    </div>

    <div class="quality-section">
      <div class="quality-container">
        <div class="quality-image" data-aos="fade-right">
          <img
            src="https://www.dogalyasam.net/image/catalog/slider/zeytinyagi-kalite.jpg"
            alt="Kalite Kontrol"
          />
        </div>
        <div class="quality-content" data-aos="fade-left">
          <h2>Bizim Yağımız Neden Özel?</h2>
          <p>
            Akyar Bölgesi Soğuk Sıkım Zeytinyağımız, Amanoslar'ın eşsiz
            doğasında yetişen zeytinlerden üretilmektedir. Özel üretim sürecimiz
            ve kalite standartlarımız, zeytinyağımızı benzersiz kılar.
          </p>
          <ul class="quality-list">
            <li>
              <span class="quality-check">✓</span> Zeytinlerimiz, kendi
              bahçelerimizden elle toplanıp, toplama işleminden sonra 4 saat
              içinde, ekim ayı başındaki 18 derece sıcaklıkta işlenir.
            </li>
            <li>
              <span class="quality-check">✓</span> Tamamen doğal tarım
              yöntemleriyle yetişen zeytinlerden elde edilir, hiçbir kimyasal
              kullanılmaz.
            </li>
            <li>
              <span class="quality-check">✓</span> Erken hasatla elde edilen,
              18°C'de soğuk sıkım yöntemiyle işlenerek besin değerleri korunur.
            </li>
            <li>
              <span class="quality-check">✓</span> 0.3–0.6 asit oranına sahip
              naturel sızma kalitesiyle uluslararası standartları karşılar.
            </li>
            <li>
              <span class="quality-check">✓</span> Tazeliğini koruyan çelik
              tanklarda dinlendirilmiş ve özenle şişelenmiştir.
            </li>
          </ul>
          <p class="quality-quote">
            "Bu zeytinyağı, sadece bir gıda değil; aynı zamanda binlerce yıllık
            gelenekten gelen bir yaşam tarzıdır."
          </p>
        </div>
      </div>
    </div>

    <div class="usage-section">
      <div class="container">
        <h2 class="section-title" data-aos="fade-up">
          Zeytinyağını Nasıl Kullanmalı, Nasıl Saklamalı?
        </h2>
        <p class="section-subtitle" data-aos="fade-up" data-aos-delay="100">
          Zeytinyağınızın lezzetini ve besin değerlerini uzun süre korumak için
          öneriler
        </p>
        <div class="usage-content">
          <div class="usage-text" data-aos="fade-right" data-aos-delay="200">
            <h3>Doğru Saklama Koşulları</h3>
            <p>
              Zeytinyağının kalitesini uzun süre koruyabilmek için doğru saklama
              koşullarına dikkat etmek önemlidir:
            </p>
            <ul class="usage-list">
              <li>
                <span class="usage-icon">🔆</span> Işıktan uzak bir yerde
                saklayın - Güneş ışığı zeytinyağının oksidasyonunu hızlandırır
              </li>
              <li>
                <span class="usage-icon">❄️</span> Serin ve kuru bir ortamda
                muhafaza edin - İdeal sıcaklık 15-18°C arasıdır
              </li>
              <li>
                <span class="usage-icon">🍶</span> Cam şişe ya da teneke
                ambalajda saklayın - Koyu renkli cam şişeler en idealdir
              </li>
              <li>
                <span class="usage-icon">🚫</span> Plastik kaplarda saklamaktan
                kaçının - Plastik, zeytinyağının kalitesini olumsuz etkiler
              </li>
            </ul>

            <h3>Kullanım Önerileri</h3>
            <p>Soğuk sıkım zeytinyağının tüm faydalarından yararlanmak için:</p>
            <ul class="usage-list">
              <li>
                <span class="usage-icon">🍞</span>
                <strong>Kahvaltıda çiğ tüketim:</strong> Ekmeğinize bandırın,
                sofranızı şenlendirin ve zeytinyağının tüm aromalarını keşfedin.
              </li>
              <li>
                <span class="usage-icon">🥗</span>
                <strong>Salata ve mezelerde:</strong> Meyvemsi aromasını tam
                hissedersiniz, salatalara son anda ekleyin.
              </li>
              <li>
                <span class="usage-icon">🥄</span>
                <strong>Sabah aç karnına içerek:</strong> Doğal detoks etkisi,
                sindirim sistemini destekler ve metabolizmayı hızlandırır.
              </li>
              <li>
                <span class="usage-icon">🍳</span>
                <strong>Yemeklerde ve kızartmalarda:</strong> Isıya
                dayanıklıdır, içeriği bozulmaz ve yemeklere eşsiz bir lezzet
                katar.
              </li>
            </ul>
          </div>
          <div class="usage-image" data-aos="fade-left" data-aos-delay="300">
            <img
              src="https://www.dogalyasam.net/image/catalog/slider/zeytinyagi-kullanim.jpg"
              alt="Zeytinyağı Kullanımı"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- iOS Style Notification -->
    <div class="ios-notification" id="discountAlert">
      <div class="ios-notification-icon">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
          <path
            d="M21.41 11.58l-9-9C12.05 2.22 11.55 2 11 2H4c-1.1 0-2 .9-2 2v7c0 .55.22 1.05.59 1.42l9 9c.36.36.86.58 1.41.58.55 0 1.05-.22 1.41-.59l7-7c.37-.36.59-.86.59-1.41 0-.55-.23-1.06-.59-1.42zM5.5 7C4.67 7 4 6.33 4 5.5S4.67 4 5.5 4 7 4.67 7 5.5 6.33 7 5.5 7z"
          />
        </svg>
      </div>
      <div class="ios-notification-content">
        <div class="ios-notification-app">
          <span>Doğal Yaşam</span>
          <span class="ios-notification-time">şimdi</span>
        </div>
        <div class="ios-notification-title">Özel İndirim Fırsatı!</div>
        <div class="ios-notification-message">
          Sepetinize ekleyeceğiniz tüm ürünlerde %20 indirim! Kod:
          <span class="ios-notification-code">DOGALYASAM20</span>
        </div>
      </div>
    </div>

    <div class="cta-section">
      <div class="cta-background"></div>
      <div class="cta-content">
        <h2 data-aos="fade-up">
          Doğal Yaşam Farkıyla Soğuk Sıkım Zeytinyağları
        </h2>
        <p data-aos="fade-up" data-aos-delay="100">
          Zeytinyağı, hem sağlık hem de damak zevki açısından yapabileceğiniz en
          iyi yatırımlardan biridir. Eğer siz de zeytinin anavatanı olan Doğu
          Akdeniz'in eşsiz doğasında, Amanos Dağları'nın eteğinde yetişmiş
          kaliteli bir soğuk sıkım naturel zeytinyağı arıyorsanız, doğru
          yerdesiniz.
        </p>
        <div class="cta-buttons" data-aos="fade-up" data-aos-delay="200">
          <a href="#products" class="cta-button">Ürünleri İnceleyin</a>
          <a href="#contact" class="cta-button secondary"
            >Akyar'dan Sofranıza Gelen Lezzetler</a
          >
        </div>
      </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.js"></script>
    <script>
      AOS.init({
        duration: 1000,
        easing: "ease-out",
        once: true,
        offset: 100,
        delay: 100,
      });

      // Smooth scroll for anchor links
      document.querySelectorAll('a[href^="#"]').forEach((anchor) => {
        anchor.addEventListener("click", function (e) {
          e.preventDefault();

          const targetId = this.getAttribute("href");
          if (targetId === "#") return;

          const targetElement = document.querySelector(targetId);
          if (targetElement) {
            window.scrollTo({
              top: targetElement.offsetTop - 100,
              behavior: "smooth",
            });
          }
        });
      });

      // iOS Style Notification
      document.addEventListener("DOMContentLoaded", function () {
        const discountAlert = document.getElementById("discountAlert");

        // Check if user has closed the alert before
        const hasClosedAlert = localStorage.getItem("closedDiscountAlert");

        if (!hasClosedAlert) {
          // Show notification immediately
          setTimeout(() => {
            discountAlert.classList.add("show");

            // Auto hide after 5 seconds
            setTimeout(() => {
              discountAlert.classList.remove("show");
            }, 5000);
          }, 1000);
        }

        // Close notification when clicking on it
        discountAlert.addEventListener("click", function () {
          // Copy discount code to clipboard
          const code = "DOGALYASAM20";
          navigator.clipboard.writeText(code).then(() => {
            // Show a small feedback that code was copied
            const notification = document.createElement("div");
            notification.style.position = "fixed";
            notification.style.bottom = "20px";
            notification.style.left = "50%";
            notification.style.transform = "translateX(-50%)";
            notification.style.backgroundColor = "rgba(0, 0, 0, 0.7)";
            notification.style.color = "white";
            notification.style.padding = "8px 16px";
            notification.style.borderRadius = "4px";
            notification.style.fontSize = "14px";
            notification.textContent = "İndirim kodu kopyalandı!";
            document.body.appendChild(notification);

            setTimeout(() => {
              notification.style.opacity = "0";
              notification.style.transition = "opacity 0.5s ease";
              setTimeout(() => {
                document.body.removeChild(notification);
              }, 500);
            }, 2000);
          });

          discountAlert.classList.remove("show");
          // Save to localStorage that user has seen the notification
          localStorage.setItem("closedDiscountAlert", "true");
        });
      });

      // Turkey Map Animation
      document.addEventListener("DOMContentLoaded", function () {
        const turkeyMap = document.getElementById("turkeyMap");
        const mapPin = document.getElementById("mapPin");

        if (turkeyMap && mapPin) {
          // Cihaz tipini kontrol edelim (mobil veya masaüstü)
          const isMobile = window.innerWidth < 768;

          // Observe when map comes into view
          const mapObserver = new IntersectionObserver(
            (entries) => {
              entries.forEach((entry) => {
                if (entry.isIntersecting) {
                  // Sayfa görünür olduktan sonra 3 saniye bekle
                  setTimeout(() => {
                    // Osmaniye ilini bulalım (TR80)
                    const osmaniyeProvince = document.getElementById("TR80");
                    const svgElement = turkeyMap.querySelector("svg");

                    if (osmaniyeProvince && svgElement) {
                      // SVG viewBox boyutlarını alalım
                      const viewBox = svgElement.viewBox.baseVal;
                      const svgWidth = viewBox.width;
                      const svgHeight = viewBox.height;

                      // Osmaniye'nin sınırlarını alalım
                      const bbox = osmaniyeProvince.getBBox();

                      // Osmaniye'nin merkez koordinatlarını hesaplayalım
                      const centerX = bbox.x + bbox.width / 2;
                      const centerY = bbox.y + bbox.height / 2;

                      // Zoom seviyesi - mobil cihazlar için daha yüksek zoom
                      const scaleFactor = isMobile ? 4 : 3;

                      // Osmaniye'yi merkeze almak için gereken kaydırma miktarını hesaplayalım
                      let translateX, translateY;

                      if (isMobile) {
                        // Mobil cihazlar için daha hassas merkezleme
                        translateX = (svgWidth / 2 - centerX) / scaleFactor;
                        // Mobil cihazlarda haritayı daha yukarı konumlandıralım
                        translateY = (svgHeight / 3 - centerY) / scaleFactor;
                      } else {
                        // Masaüstü için normal hesaplama
                        translateX = (svgWidth / 2 - centerX) / scaleFactor;
                        translateY = (svgHeight / 10 - centerY) / scaleFactor;
                      }

                      // Pin'i Osmaniye'nin merkezine konumlandıralım (zoom'dan önce)
                      // Ama önce gizleyelim, zoom tamamlandıktan sonra göstereceğiz
                      mapPin.style.opacity = "0";
                      mapPin.style.visibility = "hidden";

                      // Pin'i sabit koordinatlara yerleştiriyoruz
                      // Kullanıcının belirttiği koordinatlar - cihaz tipine göre farklı koordinatlar
                      const pinX = isMobile ? 200.201 : 540.201;
                      const pinY = isMobile ? 163.55 : 309.55;

                      // Pin'i konumlandırıyoruz
                      mapPin.style.position = "absolute";
                      mapPin.style.left = `${pinX}px`;
                      mapPin.style.top = `${pinY}px`;

                      // Mobil cihazlar için pin pozisyonunu ayarlayalım
                      if (isMobile) {
                        // Mobil cihazlarda pin'i biraz daha belirgin yapalım
                        // Pin'i daha büyük gösterelim
                        mapPin.style.transform =
                          "translate(-50%, -50%) scale(1.5)";
                        // Pin'i daha yüksek z-index ile öne çıkaralım
                        mapPin.style.zIndex = "100";
                        console.log("Mobil pin konumu:", pinX, pinY);
                      } else {
                        // Masaüstü için normal transform
                        mapPin.style.transform = "translate(-50%, -50%)";
                        console.log("Masaüstü pin konumu:", pinX, pinY);
                      }

                      // Animasyon süresini uzatalım - daha yavaş zoom için
                      turkeyMap.style.transition =
                        "transform 5s cubic-bezier(0.26, 0.86, 0.44, 0.985)";

                      // Osmaniye'yi vurgulamak için sınıf ekleyelim
                      osmaniyeProvince.classList.add("province-osmaniye");

                      // Tek bir zoom animasyonu uygulayalım
                      turkeyMap.style.transform = `scale(${scaleFactor}) translate(${translateX}px, ${translateY}px)`;

                      // Animasyon bittiğinde transition'ı kaldıralım ki geri dönmesin
                      setTimeout(() => {
                        // Transition'ı kaldır, böylece animasyon bittikten sonra geri dönmez
                        turkeyMap.style.transition = "none";
                      }, 5000); // Animasyon süresi kadar bekle

                      // Zoom animasyonu tamamlandıktan sonra pin'i gösterelim
                      // Zoom tamamlandıktan sonra pin'i gösterelim (8 saniye)
                      setTimeout(() => {
                        // Zoom tamamlandı, şimdi pin'i gösterelim
                        mapPin.classList.add("visible");
                        mapPin.style.opacity = "1";
                        mapPin.style.visibility = "visible";

                        // Pin konumu sabit kalacak, güncellemeye gerek yok
                        // Sabit koordinatlar: left: 208.201px; top: 163.55px;

                        if (isMobile) {
                          // Mobil cihazlarda pin'in görünürlüğünü garanti edelim
                          // Pin içindeki elementleri de görünür yapalım
                          const pinIcon = mapPin.querySelector(".pin-icon");
                          const pinPulse = mapPin.querySelector(".pin-pulse");
                          const pinLabel = mapPin.querySelector(".pin-label");

                          if (pinIcon) pinIcon.style.display = "block";
                          if (pinPulse) pinPulse.style.display = "block";
                          if (pinLabel) pinLabel.style.display = "block";

                          console.log(
                            "Pin gösterildi, sabit konum:",
                            pinX,
                            pinY
                          );
                        }
                      }, 3500); // Zoom tamamlandıktan sonra pin'i göster
                    } else {
                      console.log("Osmaniye ili (TR80) veya SVG bulunamadı");
                    }
                  }, 1000); // Sayfa görünür olduktan sonra 3 saniye bekle

                  // Gözlemlemeyi durdur
                  mapObserver.unobserve(turkeyMap);
                }
              });
            },
            { threshold: 0.5 }
          );

          mapObserver.observe(turkeyMap);

          // Ekran boyutu değiştiğinde haritayı yeniden ayarlayalım
          window.addEventListener("resize", function () {
            const newIsMobile = window.innerWidth < 768;

            // Cihaz tipi değiştiyse sayfayı yenileyelim
            if (newIsMobile !== isMobile) {
              location.reload();
            }
          });
        }
      });
    </script>
  </body>
</html>
