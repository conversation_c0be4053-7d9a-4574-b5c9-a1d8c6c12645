<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0" />
<title>Doğal Yaşam Zeytinyağları - Soğuk Sıkım Erken Hasat</title>
<meta
  name="description"
  content="Doğal Yaşam'ın erken hasat, soğuk sıkım zeytinyağları. Kendi bahçelerimizden, kendi fabrikamızda üretilen %100 doğal zeytinyağı."
/>
<meta
  name="keywords"
  content="zeytinyağı, soğuk sıkım, erken hasat, doğ<PERSON> zeytinyağı, organik zeytinyağı, s<PERSON><PERSON><PERSON>ğı, <PERSON><PERSON><PERSON>ey<PERSON>yağı"
/>
<meta name="author" content="Doğal Yaşam Zeytinyağları" />
<meta
  property="og:title"
  content="Doğal Yaşam Zeytinyağları - Soğuk Sıkım Erken Hasat"
/>
<meta
  property="og:description"
  content="<PERSON><PERSON> bah<PERSON>elerimizden, kendi fabrikamızda üretilen %100 doğal zeytinyağı."
/>
<meta
  property="og:image"
  content="https://www.dogalyasam.net/image/catalog/icons/new/oliveOil.png"
/>
<meta property="og:url" content="https://www.dogalyasam.net" />
<meta property="og:type" content="website" />
<meta name="twitter:card" content="summary_large_image" />
<link rel="canonical" href="https://www.dogalyasam.net" />
<style>
  :root {
    --main-green: #556b2f;
    --secondary-green: #4a5d23;
    --light-green: #d4edd4;
    --accent-brown: #8b4513;
    --text-color: #2d3748;
    --bg-cream: #f9f7f2;
    --gold: #d4af37;
  }

  * {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
  }

  #content-bottom {
    font-family: "Segoe UI", sans-serif;
    line-height: 1.6;
    margin: 0;
    padding: 0;
    background: var(--bg-cream);
    color: var(--text-color);
    text-align: center;
  }

  .container-cold {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2.5rem;
  }

  h1,
  h2,
  h3 {
    color: var(--main-green);
    margin-bottom: 1.5rem;
    font-weight: 600;
  }

  .section-title-cold {
    font-size: 3rem;
    text-align: center;
    margin-bottom: 2.5rem;
    position: relative;
    padding-bottom: 1rem;
  }

  .section-title-cold::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 3px;
    background: var(--gold);
  }

  .text-center {
    text-align: center;
  }

  .mt-4 {
    margin-top: 1.5rem;
  }

  .mt-8 {
    margin-top: 2.5rem;
  }

  .mt-12 {
    margin-top: 3.5rem;
  }

  .mb-3 {
    margin-bottom: 1.25rem;
  }

  .mb-6 {
    margin-bottom: 2rem;
  }

  .text-lg {
    font-size: 2rem;
  }

  .italic {
    font-style: italic;
  }

  .leading-relaxed {
    line-height: 1.75;
  }

  .grid {
    display: grid;
  }

  .grid-cols-1 {
    grid-template-columns: 1fr;
  }

  .gap-6 {
    gap: 2rem;
  }

  /* Hero Section */
  .hero-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2.5rem;
    align-items: center;
    margin-top: 2.5rem;
    margin-bottom: 3.5rem;
  }

  .hero-content {
    padding: 2.5rem;
    text-align: left;
  }

  .hero-title {
    font-size: 3rem;
    color: var(--main-green);
    margin-bottom: 1.5rem;
  }

  .hero-subtitle {
    font-size: 1.75rem;
    color: var(--accent-brown);
    margin-bottom: 2rem;
    font-style: italic;
  }

  .hero-description {
    font-size: 2rem;
    margin-bottom: 2.5rem;
  }

  /* Feature Boxes */
  .feature-box {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    margin-bottom: 2.5rem;
    display: flex;
    flex-direction: column;
    height: 100%;
    text-align: center;
  }

  .feature-box:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px rgba(0, 0, 0, 0.15);
  }

  .feature-icon {
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
  }

  .custom-icon {
    width: 70px;
    height: 70px;
    object-fit: contain;
  }

  .flex-icon {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  /* Product Features Grid */
  .grid-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 2.5rem;
  }

  /* Production Process */
  .production-process {
    text-align: center;
    margin: 3.5rem 0;
  }

  .process-image-cold {
    max-width: 300px;
    margin: 0 auto 2.5rem;
    display: block;
  }

  .process-features {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2.5rem;
  }

  .process-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 2rem;
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
  }

  .process-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px rgba(0, 0, 0, 0.15);
  }

  .process-icon {
    font-size: 3rem;
    color: var(--main-green);
    margin-bottom: 1.5rem;
  }

  /* New Production Features Layout */
  .production-features {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 2.5rem;
    align-items: center;
    margin: 2.5rem 0;
  }

  .production-center {
    text-align: center;
  }

  .product-image-cold {
    max-width: 450px;
    border-radius: 10px;
    filter: drop-shadow(0 10px 15px rgba(0, 0, 0, 0.2));
    animation: float 3s ease-in-out infinite;
  }

  @keyframes float {
    0% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-15px);
    }
    100% {
      transform: translateY(0px);
    }
  }

  .production-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 3rem;
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    text-align: center;
  }

  .production-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px rgba(0, 0, 0, 0.15);
  }

  .production-icon {
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
  }

  .production-text {
    width: 100%;
    text-align: center;
  }

  .production-text h3 {
    margin-bottom: 1rem;
    color: var(--main-green);
  }

  .production-bottom {
    max-width: 600px;
    margin: 2.5rem auto 0;
  }

  .production-left .production-item {
    margin-right: 1.5rem;
  }

  .production-right .production-item {
    margin-left: 1.5rem;
  }

  /* Video Container */
  .video-container-cold {
    position: relative;
    padding-bottom: 56.25%; /* 16:9 */
    height: 0;
    overflow: hidden;
    margin: 1.5rem 0;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  }

  .video-container-cold iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 10px;
  }

  /* Why Us Section */
  .why-us {
    background: linear-gradient(
        to right,
        rgba(255, 255, 255, 0.9),
        rgba(255, 255, 255, 0.7)
      ),
      url("https://images.unsplash.com/photo-1595475207225-428b62bda831?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1000&q=80");
    background-size: cover;
    background-position: center;
    padding: 3.5rem;
    border-radius: 10px;
    margin: 3.5rem 0;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  }

  .why-us-content {
    max-width: 800px;
    margin: 0 auto;
  }

  /* FAQ and Map Section */
  .faq-map-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2.5rem;
    margin: 3.5rem 0;
  }

  .faq-section {
    flex: 1;
  }

  .map-section {
    flex: 1;
  }

  .faq-item {
    margin-bottom: 1.5rem;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  }

  .faq-question {
    background: white;
    padding: 1.5rem;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
    text-align: left;
    transition: all 0.3s ease;
  }

  .faq-item.active .faq-question {
    background: var(--light-green);
    color: var(--main-green);
  }

  .faq-answer {
    padding: 0;
    background: #f5f5f5;
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease-in-out;
    text-align: left;
  }

  .faq-item.active .faq-answer {
    padding: 1.5rem;
    max-height: 300px;
  }

  .faq-question::after {
    content: "+";
    font-size: 2rem;
    transition: transform 0.3s ease;
  }

  .faq-item.active .faq-question::after {
    content: "-";
    transform: rotate(360deg);
  }

  .map-container-cold {
    min-height: 300px;
    background: #f0f0f0;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 100%;
    position: relative;
  }

  .map-container-cold iframe {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    border: none;
  }

  .map-container-cold span {
    color: #aaa;
    font-size: 1.4rem;
  }

  /* Garden Section */
  .garden-section {
    margin: 3.5rem 0;
  }

  .garden-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2.5rem;
    margin-top: 2.5rem;
  }

  .garden-image-cold {
    width: 100%;
    height: 300px;
    object-fit: cover;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  }

  .garden-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    text-align: left;
  }

  /* Customer Reviews Section */
  .reviews-container-cold {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 2.5rem 0;
  }

  .review-icon {
    margin-bottom: 2.5rem;
  }

  .review-main-icon {
    width: 150px;
    height: auto;
  }

  .reviews-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2.5rem;
    width: 100%;
  }

  .review-card {
    background: white;
    border-radius: 10px;
    padding: 2rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
  }

  .review-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
  }

  .review-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 1rem;
  }

  .review-header h3 {
    color: var(--main-green);
    margin: 0;
  }

  .stars {
    display: flex;
  }

  .star-icon {
    width: 20px;
    height: 20px;
    margin-left: 2px;
  }

  .review-card p {
    font-style: italic;
    text-align: left;
  }

  /* Other Products Section */
  .other-products {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2.5rem;
    margin-top: 2.5rem;
  }

  .other-product-card {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    text-align: center;
    display: flex;
    flex-direction: column;
    height: 100%;
    text-decoration: none;
    color: var(--text-color);
  }

  .other-product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
  }

  .other-product-image-cold {
    height: 250px;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    padding: 1.5rem;
  }

  .other-product-image-cold img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.5s ease;
  }

  .other-product-card:hover .other-product-image-cold img {
    transform: scale(1.1);
    filter: drop-shadow(0 10px 15px rgba(0, 0, 0, 0.2));
  }

  .other-product-icon {
    font-size: 3.5rem;
    color: var(--main-green);
  }

  .other-product-content {
    padding: 2rem;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    text-align: left;
  }

  .other-product-content h3 {
    margin-bottom: 1rem;
    color: var(--main-green);
  }

  .other-product-content p {
    margin-bottom: 1.5rem;
    flex-grow: 1;
  }

  .product-link {
    display: inline-block;
    padding: 1rem 1.5rem;
    background: var(--main-green);
    color: white;
    text-decoration: none;
    border-radius: 5px;
    font-weight: 500;
    transition: all 0.3s ease;
    margin-top: auto;
  }

  .product-link:hover {
    background: var(--secondary-green);
    transform: translateY(-2px);
  }

  /* Image Placeholders */
  .image-placeholder-cold {
    width: 100%;
    height: 300px;
    background: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 1.5rem 0;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  }

  .image-placeholder-cold span {
    color: #aaa;
    font-size: 1.4rem;
  }

  /* Animations */
  .fade-in {
    opacity: 0;
    animation: fadeIn 1s ease-in forwards;
  }

  .slide-in {
    opacity: 0;
    transform: translateY(20px);
    animation: slideIn 1s ease-out forwards;
  }

  .slide-in-delay-1 {
    animation-delay: 0.2s;
  }

  .slide-in-delay-2 {
    animation-delay: 0.4s;
  }

  .slide-in-delay-3 {
    animation-delay: 0.6s;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes slideIn {
    from {
      transform: translateY(20px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  /* Olive Rope Decorations */
  .olive-rope {
    position: absolute;
    width: 200px;
    height: 200px;
    background-image: url("https://www.dogalyasam.net/image/catalog/icons/new/oliveRobe.png");
    background-size: contain;
    background-repeat: no-repeat;
    z-index: -1;
    opacity: 0.8;
  }

  .top-left {
    top: 0;
    left: 0;
    transform: rotate(-45deg);
  }

  .top-right {
    top: 0;
    right: 0;
    transform: rotate(45deg);
  }

  /* Media Queries */
  @media (max-width: 992px) {
    .hero-section {
      grid-template-columns: 1fr;
    }

    .process-features {
      grid-template-columns: 1fr;
    }

    .faq-map-section {
      grid-template-columns: 1fr;
      gap: 3.5rem;
      display: flex;
      flex-direction: column;
    }

    .faq-section {
      order: 1;
    }

    .map-section {
      order: 2;
    }

    .garden-grid {
      grid-template-columns: 1fr;
    }

    .garden-grid img {
      order: -1;
      margin-bottom: 2rem;
    }

    .other-products {
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }

    .reviews-grid {
      grid-template-columns: 1fr;
      gap: 2rem;
    }

    /* Production Features Responsive */
    .production-features {
      display: flex;
      flex-direction: column;
      gap: 2rem;
    }

    .production-left {
      order: 1;
      display: flex;
      flex-direction: column;
    }

    .production-center {
      order: 2;
      margin: 2.5rem 0;
    }

    .production-right {
      order: 3;
      display: flex;
      flex-direction: column;
    }

    .production-left .production-item,
    .production-right .production-item {
      margin: 0 0 2rem 0;
    }
  }

  @media (max-width: 768px) {
    .section-title-cold {
      font-size: 2.5rem;
    }

    .hero-title {
      font-size: 2.5rem;
    }

    .container-cold {
      padding: 1.5rem;
    }

    .why-us {
      padding: 2.5rem 1.5rem;
    }

    /* Mobile Production Features */
    .product-image-cold {
      max-width: 380px;
    }

    .production-item {
      padding: 1.5rem;
    }

    .production-icon {
      width: 50px;
      height: 50px;
      font-size: 1.7rem;
    }

    .production-text h3 {
      font-size: 2.6rem;
    }
  }
</style>

<div class="container-cold">
  <!-- Title Section -->
  <div class="text-center">
    <h1 class="section-title-cold fade-in">Doğal Yaşam Zeytinyağları</h1>
    <p
      class="text-center italic text-lg slide-in"
      style="color: var(--accent-brown)"
    >
      Soğuk Sıkım Zeytinyağı
    </p>
  </div>

  <!-- Hero Section with Video and Text -->
  <section class="hero-section">
    <div class="video-container-cold slide-in slide-in-delay-1">
      <iframe
        src="https://www.youtube.com/embed/8VgLRLqZ6_s"
        frameborder="0"
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
        allowfullscreen=""
        title="Doğal Yaşam Zeytinyağı Üretim Süreci"
      ></iframe>
    </div>
    <div class="hero-content slide-in slide-in-delay-2">
      <h2 class="hero-title">Kendi Bahçelerimiz, Kendi Fabrikamız</h2>
      <p class="hero-subtitle">Doğadan Sofranıza</p>
      <p class="hero-description">
        Fabrikamızda sadece kendi zeytinlerimizi işliyoruz, başka üreticilere
        veya dışarıya hiçbir şekilde sıkım yapmıyoruz. Bu şekilde fabrikamızda
        kaliteli temiz zeytin girişini sağlıyor ve en yüksek kalitede zeytinyağı
        üretebiliyoruz.
      </p>
    </div>
  </section>

  <!-- Why Us Section -->
  <section class="mt-12">
    <h2 class="section-title-cold">
      Neden Zeytinyağını Doğal Yaşam’dan Almalısınız?
    </h2>
    <p class="text-lg leading-relaxed">
      Zeytinyağı almak sadece bir gıda alışverişi değildir; bir yaşam tarzı, bir
      sağlık yatırımı, bir kültür tercihi ve doğaya karşı duruşunuzu gösteren
      bir karardır.
    </p>
    <p class="text-lg leading-relaxed">
      Cevap basit: Çünkü biz zeytinyağını yaşıyoruz.
    </p>
  </section>

  <!-- Product Features Section -->
  <section class="mt-12">
    <h2 class="section-title-cold">Soğuk Sıkım Zeytinyağı Özellikleri</h2>
    <div class="grid-features">
      <div class="feature-box">
        <div class="feature-icon">
          <img
            src="https://www.dogalyasam.net/image/catalog/icons/new/OliveLeafe.png"
            alt="Zeytin Yaprağı İkonu"
            class="custom-icon"
          />
        </div>
        <h3>Erken Hasat</h3>
        <p>
          Zeytinlerimiz Eylül ve Ekim aylarında, henüz tam olgunlaşmadan, yeşil
          haldeyken erken hasat döneminde tek tek elle toplanır.
        </p>
      </div>

      <div class="feature-box">
        <div class="feature-icon">
          <img
            src="https://www.dogalyasam.net/image/catalog/icons/new/star.png"
            alt="Yıldız İkonu"
            class="custom-icon"
          />
        </div>
        <h3>Düşük Asit</h3>
        <p>
          Zeytinyağımızın serbest yağ asitliği oranı %0.5'in altındadır. Düşük
          asit oranı, taze, meyvemsi ve hafif aromalı zeytinyağı anlamına gelir.
        </p>
      </div>

      <div class="feature-box">
        <div class="feature-icon">
          <img
            src="https://www.dogalyasam.net/image/catalog/icons/new/oilTime.png"
            alt="Kar Tanesi İkonu"
            class="custom-icon"
          />
        </div>
        <h3>Beklemeden Sıkım</h3>
        <p>
          Toplanan zeytinler, zaman kaybetmeden aynı gün içerisinde işlenir.
          Böylece zeytinin doğallığı bozulmaz, besin değerleri korunur.
        </p>
      </div>

      <div class="feature-box">
        <div class="feature-icon">
          <img
            src="https://www.dogalyasam.net/image/catalog/icons/new/snowflake.png"
            alt="Premium Yaprak İkonu"
            class="custom-icon"
          />
        </div>
        <h3>Soğuk Sıkım</h3>
        <p>
          Kullandığımız soğuk sıkım yöntemi, zeytinyağında bulunan vitaminler,
          antioksidanlar, polifenoller ve aromatik bileşenlerin kaybolmasını
          engelleyerek, besin değerini en yüksek seviyede tutar.
        </p>
      </div>
    </div>
  </section>

  <!-- Production Process Section -->
  <section class="mt-12">
    <h2 class="section-title-cold">Nasıl Üretiyoruz?</h2>
    <div class="production-features">
      <div class="production-left">
        <div class="production-item">
          <div class="production-icon">
            <img
              src="https://www.dogalyasam.net/image/catalog/icons/new/coldTemperature.png"
              alt="Zeytinyağı Şişesi İkonu"
              class="custom-icon"
            />
          </div>
          <div class="production-text">
            <h3>Soğuk Sıkım</h3>
            <p>
              Doğallığını ve besin değerini koruyan, zeytinin saf lezzeti ilk
              günkü tazeliğinde.
            </p>
          </div>
        </div>

        <div class="production-item">
          <div class="production-icon">
            <img
              src="https://www.dogalyasam.net/image/catalog/icons/new/check.png"
              alt="Doğal İkonu"
              class="custom-icon"
            />
          </div>
          <div class="production-text">
            <h3>%100 Doğal</h3>
            <p>
              Katkısız, doğadan geldiği gibi, sadece zeytin ve emeğin buluşması.
            </p>
          </div>
        </div>
      </div>

      <div class="production-center">
        <img
          src="https://www.dogalyasam.net/image/catalog/icons/new/coldPacket.png"
          alt="Zeytinyağı Tenekesi"
          class="product-image-cold"
        />
      </div>

      <div class="production-right">
        <div class="production-item">
          <div class="production-icon">
            <img
              src="https://www.dogalyasam.net/image/catalog/icons/new/oil.png"
              alt="Yağ Damlası İkonu"
              class="custom-icon"
            />
          </div>
          <div class="production-text">
            <h3>Düşük Asit</h3>
            <p>Damakta yumuşak bir tat bırakan, hafif ve kaliteli lezzet.</p>
          </div>
        </div>

        <div class="production-item">
          <div class="production-icon">
            <img
              src="https://www.dogalyasam.net/image/catalog/icons/new/PreLefe.png"
              alt="Zeytinyağı Dökme İkonu"
              class="custom-icon"
            />
          </div>
          <div class="production-text">
            <h3>Zengin Aromatik Yapı</h3>
            <p>
              Her damlasında doğanın kokusu; yeşil meyvemsi, çimen ve taze
              lezzet notaları.
            </p>
          </div>
        </div>
      </div>
    </div>

    <div class="production-bottom">
      <div class="production-item">
        <div class="production-icon">
          <img
            src="https://www.dogalyasam.net/image/catalog/icons/new/olive.png"
            alt="Zeytin İkonu"
            class="custom-icon"
          />
        </div>
        <div class="production-text">
          <h3>Erken Hasat</h3>
          <p>
            Dalından tam vaktinde toplanan zeytinlerle daha yoğun aroma, daha
            fazla sağlık.
          </p>
        </div>
      </div>
    </div>
  </section>

  <!-- FAQ and Map Section -->
  <section class="faq-map-section">
    <div class="map-section">
      <h2 class="section-title-cold">Bahçemizin Konumu</h2>
      <div class="map-container-cold">
        <iframe
          src="https://www.google.com/maps/embed?pb=!1m10!1m8!1m3!1d203923.19801876709!2d36.18020420449887!3d37.002639536855916!3m2!1i1024!2i768!4f13.1!5e1!3m2!1str!2str!4v1747155157388!5m2!1str!2str"
          width="600"
          height="450"
          style="border: 0"
          allowfullscreen=""
          loading="lazy"
          referrerpolicy="no-referrer-when-downgrade"
        ></iframe>
      </div>
    </div>
    <div class="faq-section">
      <h2 class="section-title-cold">Sıkça Sorulan Sorular</h2>
      <div class="faq-item">
        <div class="faq-question">
          <span>Zeytinyağınızın asit oranı nedir?</span>
        </div>
        <div class="faq-answer">
          <p>
            Zeytinyağımızın serbest yağ asitliği oranı %0.5'in altındadır. Bu
            düşük asit oranı, yağın tazeliğini ve kalitesini gösterir.
          </p>
        </div>
      </div>
      <div class="faq-item">
        <div class="faq-question">
          <span>Zeytinyağınız nasıl saklanmalıdır?</span>
        </div>
        <div class="faq-answer">
          <p>
            Zeytinyağınızı serin ve karanlık bir yerde, ışık geçirmeyen bir
            kapta saklamanızı öneririz. Oda sıcaklığında muhafaza edilebilir.
          </p>
        </div>
      </div>
      <div class="faq-item">
        <div class="faq-question">
          <span>Zeytinyağınızın raf ömrü nedir?</span>
        </div>
        <div class="faq-answer">
          <p>
            Zeytinyağımızın raf ömrü 2 yıldır. Ancak, taze zeytinyağı daha
            lezzetli ve sağlıklıdır. Açıldıktan sonra 6 ay içinde tüketmenizi
            öneririz.
          </p>
        </div>
      </div>
      <div class="faq-item">
        <div class="faq-question">
          <span>Zeytinyağınızı nasıl tüketmeliyim?</span>
        </div>
        <div class="faq-answer">
          <p>
            Kaliteli zeytinyağımızı çiğ olarak salatalarda, ekmek üzerinde veya
            yemeklere son dokunuş olarak kullanmanızı öneririz. Yüksek ısıda
            kızartma için kullanılması, içerdiği değerli besin öğelerinin
            kaybına neden olabilir.
          </p>
        </div>
      </div>
    </div>
  </section>

  <!-- Our Garden Section -->
  <section class="garden-section" style="padding-top: 3rem">
    <h2 class="section-title-cold">Bahçemiz</h2>
    <div class="garden-grid">
      <div class="garden-content slide-in">
        <h3>
          <img
            src="https://www.dogalyasam.net/image/catalog/icons/new/leafeGreen.png"
            alt="Yaprak İkonu"
            style="
              width: 30px;
              height: 30px;
              margin-right: 10px;
              vertical-align: middle;
            "
          />
          Asırlık Zeytin Ağaçları
        </h3>
        <p class="mb-3">
          Bahçemiz, Doğu Akdeniz'in bereketli toprakları üzerinde, Osmaniye ile
          Hatay arasındaki Amanosların Akyar Dağı eteklerinde yer almaktadır. Bu
          bölge, 300 metre rakımı, yıl boyunca aldığı güneş ışığı, temiz havası
          ve organik yapılı toprakları sayesinde zeytin yetiştiriciliği için
          Türkiye'nin en verimli zeytin havzalarından biridir.
        </p>
        <p>
          Bahçemizdeki zeytin ağaçlarının bazıları 100 yılı aşkın süredir bu
          topraklarda büyüyor. Bu asırlık ağaçlar, daha derin köklere sahip
          olduğundan, daha zengin minerallere erişebiliyor.
        </p>
      </div>
      <img
        src="https://www.dogalyasam.net/image/catalog/icons/new/olivePhoto0001.jpg?ixlib=rb-4.0.3&amp;ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&amp;auto=format&amp;fit=crop&amp;w=800&amp;q=80"
        alt="Zeytin Bahçesi"
        class="garden-image-cold slide-in slide-in-delay-1"
      />
    </div>
    <div class="garden-grid mt-8">
      <img
        src="https://www.dogalyasam.net/image/catalog/icons/new/olivePhoto0003.jpg?ixlib=rb-4.0.3&amp;ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&amp;auto=format&amp;fit=crop&amp;w=800&amp;q=80"
        alt="Zeytin Hasadı"
        class="garden-image-cold slide-in"
      />
      <div class="garden-content slide-in slide-in-delay-1">
        <h3>
          <img
            src="https://www.dogalyasam.net/image/catalog/icons/new/OliveLeafe.png"
            alt="Zeytin Yaprağı İkonu"
            style="
              width: 30px;
              height: 30px;
              margin-right: 10px;
              vertical-align: middle;
            "
          />
          Doğal Tarım Yöntemleri
        </h3>
        <p class="mb-3">
          Her bir damla zeytinyağımız, bu özel coğrafyanın, doğallığın ve emeğin
          eşsiz birleşimidir. Zeytinlerimiz Eylül ve Ekim aylarında, henüz tam
          olgunlaşmadan, yeşil haldeyken erken hasat döneminde tek tek elle
          toplanır.
        </p>
        <p>
          Bahçemizde kimyasal gübre ve ilaç kullanmıyoruz. Bunun yerine, kompost
          ve organik gübrelerle toprağı besliyoruz. Zararlılarla mücadelede
          doğal yöntemler kullanıyoruz.
        </p>
      </div>
    </div>
  </section>

  <!-- Customer Reviews Section -->
  <section class="mt-12">
    <h2 class="section-title-cold">Müşteri Yorumları</h2>
    <div class="reviews-container">
      <div class="review-icon">
        <img
          src="https://www.dogalyasam.net/image/catalog/icons/new/CustomerReviews.png"
          alt="Müşteri Yorumları"
          class="review-main-icon"
        />
      </div>
      <div class="reviews-grid">
        <div class="review-card">
          <div class="review-header">
            <h3>Ahmet Y.</h3>
            <div class="stars">
              <img
                src="https://www.dogalyasam.net/image/catalog/icons/new/star.png"
                alt="Yıldız"
                class="star-icon"
              />
              <img
                src="https://www.dogalyasam.net/image/catalog/icons/new/star.png"
                alt="Yıldız"
                class="star-icon"
              />
              <img
                src="https://www.dogalyasam.net/image/catalog/icons/new/star.png"
                alt="Yıldız"
                class="star-icon"
              />
              <img
                src="https://www.dogalyasam.net/image/catalog/icons/new/star.png"
                alt="Yıldız"
                class="star-icon"
              />
              <img
                src="https://www.dogalyasam.net/image/catalog/icons/new/star.png"
                alt="Yıldız"
                class="star-icon"
              />
            </div>
          </div>
          <p>
            "Yıllardır Doğal Yaşam'ın zeytinyağını kullanıyorum. Tadı, kokusu ve
            kalitesi gerçekten eşsiz. Özellikle erken hasat zeytinyağı favorim."
          </p>
        </div>
        <div class="review-card">
          <div class="review-header">
            <h3>Ayşe K.</h3>
            <div class="stars">
              <img
                src="https://www.dogalyasam.net/image/catalog/icons/new/star.png"
                alt="Yıldız"
                class="star-icon"
              />
              <img
                src="https://www.dogalyasam.net/image/catalog/icons/new/star.png"
                alt="Yıldız"
                class="star-icon"
              />
              <img
                src="https://www.dogalyasam.net/image/catalog/icons/new/star.png"
                alt="Yıldız"
                class="star-icon"
              />
              <img
                src="https://www.dogalyasam.net/image/catalog/icons/new/star.png"
                alt="Yıldız"
                class="star-icon"
              />
              <img
                src="https://www.dogalyasam.net/image/catalog/icons/new/star.png"
                alt="Yıldız"
                class="star-icon"
              />
            </div>
          </div>
          <p>
            "Salatalarda, yemeklerde kullandığım bu zeytinyağı gerçekten fark
            yaratıyor. Doğal ve saf olduğunu her damlasında
            hissedebiliyorsunuz."
          </p>
        </div>
        <div class="review-card">
          <div class="review-header">
            <h3>Mehmet S.</h3>
            <div class="stars">
              <img
                src="https://www.dogalyasam.net/image/catalog/icons/new/star.png"
                alt="Yıldız"
                class="star-icon"
              />
              <img
                src="https://www.dogalyasam.net/image/catalog/icons/new/star.png"
                alt="Yıldız"
                class="star-icon"
              />
              <img
                src="https://www.dogalyasam.net/image/catalog/icons/new/star.png"
                alt="Yıldız"
                class="star-icon"
              />
              <img
                src="https://www.dogalyasam.net/image/catalog/icons/new/star.png"
                alt="Yıldız"
                class="star-icon"
              />
              <img
                src="https://www.dogalyasam.net/image/catalog/icons/new/star.png"
                alt="Yıldız"
                class="star-icon"
              />
            </div>
          </div>
          <p>
            "Doğal Yaşam'ın zeytinyağını bir kez denedikten sonra başka marka
            kullanamaz oldum. Kalitesi, aroması ve tazeliği ile kesinlikle
            tavsiye ediyorum."
          </p>
        </div>
      </div>
    </div>
  </section>

  <!-- Other Products Section -->
  <section class="mt-12">
    <h2 class="section-title-cold">Diğer Zeytinyağlarımız</h2>
    <div class="other-products">
      <a
        href="https://www.dogalyasam.net/zeytinyagi/naturel-sizma-zeytinyagi/"
        class="other-product-card slide-in"
      >
        <div class="other-product-image-cold">
          <img
            src="https://www.dogalyasam.net/image/catalog/icons/new/olivePhoto0006.jpg?ixlib=rb-4.0.3&amp;ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&amp;auto=format&amp;fit=crop&amp;w=800&amp;q=80"
            alt="Organik Sızma Zeytinyağı"
          />
        </div>
        <div class="other-product-content">
          <h3>Erken Hasat</h3>
          <p>
            Erken hasat zeytinyağımız, zeytinlerin tam olgunlaşmadan
            toplanmasıyla elde edilir. Daha yoğun bir aromaya sahiptir.
          </p>
        </div>
      </a>
      <a
        href="https://www.dogalyasam.net/zeytinyagi/naturel-birinci-zeytinyagi-yemeklik-zeytinyagi/"
        class="other-product-card slide-in slide-in-delay-1"
      >
        <div class="other-product-image-cold">
          <img
            src="https://www.dogalyasam.net/image/catalog/icons/new/olivePhoto0004.jpg?ixlib=rb-4.0.3&amp;ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&amp;auto=format&amp;fit=crop&amp;w=800&amp;q=80"
            alt="Teneke Zeytinyağı"
          />
        </div>
        <div class="other-product-content">
          <h3>Birinci Zeytinyağı</h3>
          <p>
            Birinci zeytinyağımız, en yüksek kalitede zeytinlerden üretilir.
            Özel cam şişede sunulur.
          </p>
        </div>
      </a>
      <a
        href="https://www.dogalyasam.net/zeytinyagi/delice-zeytinyagi"
        class="other-product-card slide-in slide-in-delay-2"
      >
        <div class="other-product-image-cold">
          <img
            src="https://www.dogalyasam.net/image/catalog/icons/new/deliceOile.jpg?ixlib=rb-4.0.3&amp;ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&amp;auto=format&amp;fit=crop&amp;w=800&amp;q=80"
            alt="Zeytinyağı Dökme"
          />
        </div>
        <div class="other-product-content">
          <h3>Delice Zeytinyağı</h3>
          <p>
            Delice zeytinyağımız, özel zeytin türlerinden üretilir ve eşsiz bir
            lezzete sahiptir. Farklı lezzetler arayanlar için ideal.
          </p>
        </div>
      </a>
    </div>
  </section>
</div>

<script>
  document.querySelectorAll(".faq-question").forEach((item) => {
    item.addEventListener("click", () => {
      item.parentElement.classList.toggle("active");
    });
  });

  // Animasyonlar için ekstra script
  window.addEventListener("load", () => {
    const elements = document.querySelectorAll(".fade-in, .slide-in");
    elements.forEach((element, index) => {
      setTimeout(() => {
        element.style.opacity = "1";
        element.style.transform = "translateY(0)";
      }, index * 200);
    });
  });
</script>
