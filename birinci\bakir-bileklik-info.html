<!DOCTYPE html>
<html lang="tr">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Dalga Model Bakır Bileklik - Ür<PERSON>n <PERSON></title>
    <style>
      /* Reset and Base Styles */
      .copper-product * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      .copper-product {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
        line-height: 1.6;
        color: #1d1d1f;
        background: #f5f5f7;
        min-height: 100vh;
      }

      /* Copper Color Palette */

      .copper-accent {
        background: linear-gradient(
          135deg,
          #8b4513 0%,
          #a0522d 50%,
          #cd853f 100%
        );
      }

      .copper-light {
        background: linear-gradient(
          135deg,
          #deb887 0%,
          #f4a460 50%,
          #d2b48c 100%
        );
      }

      /* Container */
      .copper-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
      }

      /* Hero Section */
      .copper-hero {
        position: relative;
        height: 100vh;
        min-height: 600px;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
        background: linear-gradient(
          135deg,
          rgba(184, 115, 51, 0.1) 0%,
          rgba(205, 127, 50, 0.05) 100%
        );
      }

      .copper-hero-image {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
        z-index: 1;
      }

      .copper-hero-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 2;
      }

      .copper-hero-content {
        position: relative;
        z-index: 3;
        text-align: center;
        color: white;
        max-width: 800px;
        padding: 0 20px;
      }

      .copper-hero-title {
        font-size: 64px;
        font-weight: 700;
        margin-bottom: 20px;
        text-shadow: 0 2px 20px rgba(0, 0, 0, 0.3);
        letter-spacing: -2px;
      }

      .copper-hero-subtitle {
        font-size: 24px;
        font-weight: 400;
        margin-bottom: 40px;
        opacity: 0.9;
        text-shadow: 0 1px 10px rgba(0, 0, 0, 0.3);
      }

      /* Floating Callouts */
      .copper-callouts {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 4;
        pointer-events: none;
      }

      .copper-callout {
        position: absolute;
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 24px;
        padding: 28px;
        max-width: 300px;
        pointer-events: auto;
        cursor: pointer;
        transition: all 0.5s cubic-bezier(0.23, 1, 0.32, 1);
        overflow: hidden;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12),
          0 2px 8px rgba(0, 0, 0, 0.08);
      }

      /* Glassmorphism effect */
      .copper-callout::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          135deg,
          rgba(255, 255, 255, 0.25) 0%,
          rgba(0, 0, 0, 0.2) 50%,
          rgba(184, 115, 51, 0.1) 100%
        );
        border-radius: 24px;
        z-index: -1;
      }

      /* Hover glow effect */
      .copper-callout::after {
        content: "";
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        background: linear-gradient(
          135deg,
          rgba(184, 115, 51, 0.6),
          rgba(205, 127, 50, 0.4),
          rgba(210, 105, 30, 0.6)
        );
        border-radius: 26px;
        z-index: -2;
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      .copper-callout:hover::after {
        opacity: 1;
      }

      .copper-callout:hover {
        transform: translateY(-12px) scale(1.02);
        background: rgba(255, 255, 255, 0.15);
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2),
          0 8px 24px rgba(184, 115, 51, 0.15);
      }

      /* Better positioning */
      .copper-callout-1 {
        top: 8%;
        left: 5%;
        animation: float1 6s ease-in-out infinite;
      }

      .copper-callout-2 {
        top: 15%;
        right: 5%;
        animation: float2 7s ease-in-out infinite;
        animation-delay: 1s;
      }

      .copper-callout-3 {
        bottom: 20%;
        left: 8%;
        animation: float3 8s ease-in-out infinite;
        animation-delay: 2s;
      }

      .copper-callout-4 {
        bottom: 8%;
        right: 8%;
        animation: float4 6.5s ease-in-out infinite;
        animation-delay: 0.5s;
      }

      /* Modern icon design */
      .copper-callout-icon {
        width: 64px;
        height: 64px;
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 20px;
        font-size: 28px;
        color: white;
        position: relative;
        transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
      }
      .copper-callout-icon img {
        width: 90px;
        height: 90px;
        object-fit: contain;
      }
      .copper-mobile-icon img {
        width: 70px;
        height: 70px;
        object-fit: contain;
      }
      .copper-callout:hover .copper-callout-icon {
        transform: scale(1.1) rotateY(15deg);
        box-shadow: 0 12px 36px rgba(0, 0, 0, 0.2),
          inset 0 1px 0 rgba(255, 255, 255, 0.3);
      }

      /* Typography improvements */
      .copper-callout h3 {
        font-size: 20px;
        font-weight: 700;
        color: white;
        margin-bottom: 12px;
        text-align: center;
        letter-spacing: -0.5px;
        text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
      }

      .copper-callout p {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.9);
        line-height: 1.6;
        margin: 0;
        text-align: center;
        font-weight: 400;
        text-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
      }

      /* Premium badge redesign */
      .copper-callout-badge {
        position: absolute;
        top: -6px;
        right: -6px;
        background: linear-gradient(135deg, #ff6b35, #f7931e);
        color: white;
        font-size: 9px;
        font-weight: 800;
        padding: 6px 10px;
        border-radius: 16px;
        text-transform: uppercase;
        letter-spacing: 0.8px;
        box-shadow: 0 4px 16px rgba(255, 107, 53, 0.4),
          0 2px 4px rgba(0, 0, 0, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
      }

      /* Individual float animations */
      @keyframes float1 {
        0%,
        100% {
          transform: translateY(0px) rotate(0deg);
        }
        50% {
          transform: translateY(-15px) rotate(1deg);
        }
      }

      @keyframes float2 {
        0%,
        100% {
          transform: translateY(0px) rotate(0deg);
        }
        50% {
          transform: translateY(-20px) rotate(-1deg);
        }
      }

      @keyframes float3 {
        0%,
        100% {
          transform: translateY(0px) rotate(0deg);
        }
        50% {
          transform: translateY(-18px) rotate(1.5deg);
        }
      }

      @keyframes float4 {
        0%,
        100% {
          transform: translateY(0px) rotate(0deg);
        }
        50% {
          transform: translateY(-12px) rotate(-0.5deg);
        }
      }

      /* Content Sections */
      .copper-content {
        background: white;
        padding: 80px 0;
      }

      .copper-section {
        margin-bottom: 120px;
      }

      .copper-section-title {
        font-size: 48px;
        font-weight: 700;
        color: #1d1d1f;
        text-align: center;
        margin-bottom: 60px;
        letter-spacing: -1px;
      }

      .copper-highlight {
        background: linear-gradient(45deg, #ffcf2c, #ff7703);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      .copper-info-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 80px;
        align-items: center;
        margin-bottom: 80px;
      }

      .copper-info-content h3 {
        font-size: 32px;
        font-weight: 600;
        color: #1d1d1f;
        margin-bottom: 24px;
        line-height: 1.2;
      }

      .copper-info-content p {
        color: #6e6e73;
        font-size: 18px;
        line-height: 1.6;
        margin-bottom: 32px;
      }

      .copper-feature-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
      }

      .copper-feature-card {
        background: #f5f5f7;
        border-radius: 16px;
        padding: 24px;
        transition: all 0.3s ease;
        border: 1px solid transparent;
      }

      .copper-feature-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        border-color: rgba(184, 115, 51, 0.2);
      }

      .copper-feature-card h4 {
        font-size: 18px;
        font-weight: 600;
        color: #1d1d1f;
        margin-bottom: 8px;
      }

      .copper-feature-card p {
        font-size: 16px;
        color: #6e6e73;
        margin: 0;
        line-height: 1.5;
      }

      .copper-info-image {
        position: relative;
        border-radius: 24px;
        overflow: hidden;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
      }

      .copper-info-image img {
        width: 100%;
        height: 400px;
        object-fit: cover;
        transition: transform 0.3s ease;
      }

      .copper-info-image:hover img {
        transform: scale(1.05);
      }

      .copper-badge {
        position: absolute;
        top: 20px;
        right: 20px;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        color: #1d1d1f;
        padding: 12px 20px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 14px;
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
      }

      /* Care Instructions */
      .copper-care-section {
        background: #f5f5f7;
        padding: 40px 0;
      }

      .copper-care-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 32px;
        margin-top: 60px;
      }

      .copper-care-card {
        background: white;
        border-radius: 24px;
        padding: 40px 24px;
        text-align: center;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
        border: 1px solid transparent;
      }

      .copper-care-card:hover {
        transform: translateY(-8px);
        border-color: rgba(184, 115, 51, 0.2);
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.12);
      }

      .copper-care-icon {
        width: 80px;
        height: 80px;
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 24px;
        color: white;
        font-size: 32px;
      }
      .copper-care-icon img {
        width: 130px;
        height: 130px;
        object-fit: contain;
      }
      .copper-care-card h3 {
        font-size: 20px;
        font-weight: 600;
        color: #1d1d1f;
        margin-bottom: 12px;
      }

      .copper-care-card p {
        color: #6e6e73;
        font-size: 16px;
        line-height: 1.5;
        margin: 0;
      }

      /* Mobile Features Section */
      .copper-mobile-features {
        display: none;
        background: white;
        padding: 40px 0;
        border-top: 1px solid #f0f0f0;
      }

      .copper-mobile-features-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 24px;
      }

      .copper-mobile-feature {
        background: #f8f9fa;
        border-radius: 16px;
        padding: 24px 16px;
        text-align: center;
        transition: all 0.3s ease;
        border: 1px solid transparent;
      }

      .copper-mobile-feature:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
        border-color: rgba(184, 115, 51, 0.2);
      }

      .copper-mobile-icon {
        width: 56px;
        height: 56px;
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 16px;
        font-size: 24px;
        color: white;
      }

      .copper-mobile-feature h3 {
        font-size: 16px;
        font-weight: 600;
        color: #1d1d1f;
        margin-bottom: 8px;
      }

      .copper-mobile-feature p {
        font-size: 13px;
        color: #6e6e73;
        line-height: 1.4;
        margin: 0;
      }

      /* Animations */
      @keyframes fadeInUp {
        from {
          opacity: 0;
          transform: translateY(60px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .fade-in-up {
        animation: fadeInUp 0.8s ease-out forwards;
      }

      .delay-1 {
        animation-delay: 0.2s;
      }
      .delay-2 {
        animation-delay: 0.4s;
      }
      .delay-3 {
        animation-delay: 0.6s;
      }
      .delay-4 {
        animation-delay: 0.8s;
      }

      /* Responsive Design */
      @media (max-width: 1200px) {
        .copper-callout {
          max-width: 260px;
          padding: 24px;
        }

        .copper-callout-1 {
          left: 3%;
        }
        .copper-callout-2 {
          right: 3%;
        }
        .copper-callout-3 {
          left: 5%;
        }
        .copper-callout-4 {
          right: 3%;
        }
      }

      @media (max-width: 1024px) {
        .copper-hero-title {
          font-size: 48px;
        }

        .copper-hero-subtitle {
          font-size: 20px;
        }

        .copper-info-grid {
          grid-template-columns: 1fr;
          gap: 60px;
        }

        .copper-care-grid {
          grid-template-columns: repeat(2, 1fr);
          gap: 24px;
        }

        .copper-section-title {
          font-size: 40px;
        }

        /* Tablet callout layout */
        .copper-callout {
          position: relative;
          margin: 0;
          max-width: 280px;
          animation: none !important;
          transform: none !important;
        }

        .copper-callouts {
          position: relative;
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 24px;
          padding: 60px 20px;
          background: rgba(0, 0, 0, 0.1);
          backdrop-filter: blur(10px);
          border-radius: 24px;
          margin: 40px 20px;
        }

        .copper-callout-1,
        .copper-callout-2,
        .copper-callout-3,
        .copper-callout-4 {
          position: relative;
          top: auto;
          left: auto;
          right: auto;
          bottom: auto;
        }

        .copper-callout::after {
          display: none;
        }
      }

      @media (max-width: 768px) {
        .copper-hero {
          height: 60vh;
          min-height: 400px;
        }

        .copper-hero-title {
          font-size: 36px;
          letter-spacing: -1px;
        }

        .copper-hero-subtitle {
          font-size: 18px;
        }

        .copper-section-title {
          font-size: 32px;
        }

        .copper-info-content h3 {
          font-size: 24px;
        }

        .copper-info-content p {
          font-size: 16px;
        }

        .copper-feature-grid {
          grid-template-columns: 1fr;
          gap: 16px;
        }

        .copper-care-grid {
          grid-template-columns: 1fr;
          gap: 20px;
        }

        .copper-content {
          padding: 60px 0;
        }

        .copper-section {
          margin-bottom: 80px;
        }

        .copper-care-section {
          padding: 60px 0;
        }

        /* Mobile hero image positioning */
        .copper-hero-image {
          object-position: 35% center; /* Move image to the right */
        }

        /* Hide callouts completely on mobile - they'll be shown separately */
        .copper-callouts {
          display: none;
        }

        /* Show mobile features section on mobile */
        .copper-mobile-features {
          display: block;
        }

        .copper-mobile-features-grid {
          grid-template-columns: repeat(2, 1fr);
          gap: 16px;
        }

        .copper-mobile-feature {
          padding: 20px 12px;
        }

        .copper-mobile-icon {
          width: 48px;
          height: 48px;
          font-size: 20px;
          margin-bottom: 12px;
        }
      }

      @media (max-width: 480px) {
        .copper-hero-title {
          font-size: 28px;
        }

        .copper-callout {
          max-width: 280px;
          padding: 16px;
        }

        .copper-care-card {
          padding: 32px 20px;
        }
      }
    </style>
  </head>
  <body>
    <div class="copper-product">
      <!-- Hero Section -->
      <section class="copper-hero">
        <img
          src="https://www.dogalyasam.net/image/catalog/icons/new/Bakir/dalga-design.jpg"
          alt="Dalga Model Bakır Bileklik"
          class="copper-hero-image"
        />
        <div class="copper-hero-overlay"></div>

        <!-- Floating Callouts -->
        <div class="copper-callouts">
          <div class="copper-callout copper-callout-1">
            <div class="copper-callout-badge">Premium</div>
            <div class="copper-callout-icon">
              <img
                src="https://www.dogalyasam.net/image/catalog/icons/new/Bakir/copp-3.png"
                alt=""
              />
            </div>
            <h3>El İşçiliği</h3>
            <p>
              Her detay özenle işlenmiş, benzersiz dalga desenleri ile ustalık
              eseri
            </p>
          </div>

          <div class="copper-callout copper-callout-2">
            <div class="copper-callout-icon">
              <img
                src="https://www.dogalyasam.net/image/catalog/icons/new/Bakir/copp-14.png"
                alt=""
              />
            </div>
            <h3>Dalga Deseni</h3>
            <p>
              Denizin sakin enerjisini yansıtan akışkan formlar ve doğal ritim
            </p>
          </div>

          <div class="copper-callout copper-callout-3">
            <div class="copper-callout-badge">%100 Saf</div>
            <div class="copper-callout-icon">
              <img
                src="https://www.dogalyasam.net/image/catalog/icons/new/Bakir/copp-4.png"
                alt=""
              />
            </div>
            <h3>Saf Bakır</h3>
            <p>
              Premium kalite saf bakır malzemeden üretilmiş, hipoalerjenik yapı
            </p>
          </div>

          <div class="copper-callout copper-callout-4">
            <div class="copper-callout-icon">
              <img
                src="https://www.dogalyasam.net/image/catalog/icons/new/Bakir/copp-15.png"
                alt=""
              />
            </div>
            <h3>Doğal Parlaklık</h3>
            <p>
              Işığın her açısında büyülü yansımalar oluşturan özel yüzey işlemi
            </p>
          </div>
        </div>
      </section>

      <!-- Mobile Features Section (only visible on mobile) -->
      <section class="copper-mobile-features">
        <div class="copper-container">
          <div class="copper-mobile-features-grid">
            <div class="copper-mobile-feature">
              <div class="copper-mobile-icon copper-gradient">
                <img
                  src="https://www.dogalyasam.net/image/catalog/icons/new/Bakir/copp-9.png"
                  alt=""
                />
              </div>
              <h3>El İşçiliği</h3>
              <p>
                Her detay özenle işlenmiş, benzersiz dalga desenleri ile ustalık
                eseri
              </p>
            </div>

            <div class="copper-mobile-feature">
              <div class="copper-mobile-icon">
                <img
                  src="https://www.dogalyasam.net/image/catalog/icons/new/Bakir/copp-7.png"
                  alt=""
                />
              </div>
              <h3>Dalga Deseni</h3>
              <p>
                Denizin sakin enerjisini yansıtan akışkan formlar ve doğal ritim
              </p>
            </div>

            <div class="copper-mobile-feature">
              <div class="copper-mobile-icon">
                <img
                  src="https://www.dogalyasam.net/image/catalog/icons/new/Bakir/copp-4.png"
                  alt=""
                />
              </div>
              <h3>Saf Bakır</h3>
              <p>
                Premium kalite saf bakır malzemeden üretilmiş, hipoalerjenik
                yapı
              </p>
            </div>

            <div class="copper-mobile-feature">
              <div class="copper-mobile-icon">
                <img
                  src="https://www.dogalyasam.net/image/catalog/icons/new/Bakir/copp-8.png"
                  alt=""
                />
              </div>
              <h3>Doğal Parlaklık</h3>
              <p>
                Işığın her açısında büyülü yansımalar oluşturan özel yüzey
                işlemi
              </p>
            </div>
          </div>
        </div>
      </section>

      <!-- Content Sections -->
      <div class="copper-content">
        <div class="copper-container">
          <!-- Product Description -->
          <section class="copper-section">
            <h2 class="copper-section-title fade-in-up">
              Doğanın <span class="copper-highlight">Sakin Enerjisi</span>
            </h2>

            <div class="copper-info-grid">
              <div class="copper-info-content fade-in-up delay-1">
                <h3>Benzersiz Tasarım Felsefesi</h3>
                <p>
                  İnce ve akışkan dalga desenleri, saf bakır levha üzerinde
                  ustalıkla işlenerek deniz kıyısının huzurunu yansıtır. Her bir
                  dalga kıvrımı, özel çekiç darbeleriyle derinlik kazanır ve
                  ışık vurdukça bileklik üzerinde hareket eden yansımalar
                  yaratır.
                </p>

                <div class="copper-feature-grid">
                  <div class="copper-feature-card">
                    <h4>El İşçiliği Detaylar</h4>
                    <p>Ustalıkla işlenmiş her detay</p>
                  </div>
                  <div class="copper-feature-card">
                    <h4>Dalga Desenleri</h4>
                    <p>Doğanın ritmini yansıtan formlar</p>
                  </div>
                  <div class="copper-feature-card">
                    <h4>Saf Bakır Malzeme</h4>
                    <p>%100 premium kalite bakır</p>
                  </div>
                  <div class="copper-feature-card">
                    <h4>Doğal Parlaklık</h4>
                    <p>Işığın büyülü yansımaları</p>
                  </div>
                </div>
              </div>

              <div class="copper-info-image fade-in-up delay-2">
                <img
                  src="https://www.dogalyasam.net/image/cache/catalog/image/Bak%C4%B1r%20Bileklikler/yeni2/dalga-550x550.jpg"
                  alt="Bakır İşçiliği Detayları"
                />
                <div class="copper-badge">El İşçiliği</div>
              </div>
            </div>
          </section>

          <!-- Production Process -->
          <section class="copper-section">
            <h2 class="copper-section-title fade-in-up">
              <span class="copper-highlight">Üretim Süreci</span> ve Kalite
            </h2>

            <div class="copper-info-grid">
              <div class="copper-info-image fade-in-up delay-1">
                <img
                  src="https://www.dogalyasam.net/image/catalog/icons/new/Bakir/copper-KB-2.jpg"
                  alt="Bakır Üretim Süreci"
                />
                <div class="copper-badge">%100 Saf</div>
              </div>

              <div class="copper-info-content fade-in-up delay-2">
                <h3>Premium Kalite Garantisi</h3>
                <p>
                  %100 saf bakır kullanılarak, parça önce şekillendirildikten
                  sonra dalga desenleri dikkatlice uygulanır. Biyolojik cilt
                  dostu kaplama işlemiyle korunarak oksitlenme ve kararmaya
                  karşı direnç kazanır. Hassas ciltler için güvenli kullanım
                  vadeder.
                </p>

                <div class="copper-feature-grid">
                  <div class="copper-feature-card">
                    <h4>Hipoalerjenik Yapı</h4>
                    <p>Hassas ciltler için güvenli</p>
                  </div>
                  <div class="copper-feature-card">
                    <h4>Ayarlanabilir</h4>
                    <p>Farklı bilek genişliklerine uyum</p>
                  </div>
                  <div class="copper-feature-card">
                    <h4>Oksitlenme Koruması</h4>
                    <p>Uzun ömürlü parlaklık</p>
                  </div>
                  <div class="copper-feature-card">
                    <h4>Vücut Uyumu</h4>
                    <p>Doğal ısı etkileşimi</p>
                  </div>
                </div>
              </div>
            </div>
          </section>

          <!-- Style Combinations -->
          <section class="copper-section">
            <h2 class="copper-section-title fade-in-up">
              <span class="copper-highlight">Stil</span> Kombinasyonları
            </h2>

            <div class="copper-info-grid">
              <div class="copper-info-content fade-in-up delay-1">
                <h3>Çok Yönlü Kullanım</h3>
                <p>
                  Günlük sokak stilinizde spor şıklığınızı tamamlayabileceği
                  gibi, ofiste de zarif bir detay olarak öne çıkabilir. Minimal
                  kol saatleriyle uyumlu pairing yapabilirsiniz. İnciler, deri
                  bilezikler veya renkli iplerle katmanlı kullanım yaratıcı bir
                  görünüm sağlar.
                </p>

                <div class="copper-feature-grid">
                  <div class="copper-feature-card">
                    <h4>Ofis Şıklığı</h4>
                    <p>Profesyonel görünümde zarafet</p>
                  </div>
                  <div class="copper-feature-card">
                    <h4>Spor Kombinler</h4>
                    <p>Aktif yaşamda stil</p>
                  </div>
                  <div class="copper-feature-card">
                    <h4>Saat Uyumu</h4>
                    <p>Minimal aksesuarlarla uyum</p>
                  </div>
                  <div class="copper-feature-card">
                    <h4>Katmanlı Stil</h4>
                    <p>Yaratıcı kombinasyonlar</p>
                  </div>
                </div>
              </div>

              <div class="copper-info-image fade-in-up delay-2">
                <img
                  src="https://www.dogalyasam.net/image/catalog/icons/new/Bakir/copper-KB-3.jpg"
                  alt="Stil Kombinasyonları"
                />
                <div class="copper-badge">Çok Yönlü</div>
              </div>
            </div>
          </section>
        </div>
      </div>

      <!-- Care Instructions -->
      <section class="copper-care-section">
        <div class="copper-container">
          <h2 class="copper-section-title fade-in-up">
            <span class="copper-highlight">Bakım</span> Talimatları
          </h2>

          <div class="copper-care-grid">
            <div class="copper-care-card fade-in-up delay-1">
              <div class="copper-care-icon">
                <img
                  src="https://www.dogalyasam.net/image/catalog/icons/new/Bakir/copp-1.png"
                  alt=""
                />
              </div>
              <h3>Düzenli Temizlik</h3>
              <p>
                Yumuşak, kuru bir bezle düzenli silme, bakır yüzeyin doğal
                parlaklığının korunmasına yardımcı olur.
              </p>
            </div>

            <div class="copper-care-card fade-in-up delay-2">
              <div class="copper-care-icon">
                <img
                  src="https://www.dogalyasam.net/image/catalog/icons/new/Bakir/copp-11.png"
                  alt=""
                />
              </div>
              <h3>Su Teması</h3>
              <p>
                Duş alırken çıkarmanız önerilir. Su ile temas sonrası hemen
                kurulatın.
              </p>
            </div>

            <div class="copper-care-card fade-in-up delay-3">
              <div class="copper-care-icon copper-light">
                <img
                  src="https://www.dogalyasam.net/image/catalog/icons/new/Bakir/copp-17.png"
                  alt=""
                />
              </div>
              <h3>Saklama</h3>
              <p>
                Kullanmadığınız zamanlarda kuru ve havalandırılmış bir yerde
                saklayın.
              </p>
            </div>

            <div class="copper-care-card fade-in-up delay-4">
              <div class="copper-care-icon copper-gradient">
                <img
                  src="https://www.dogalyasam.net/image/catalog/icons/new/Bakir/copp-16.png"
                  alt=""
                />
              </div>
              <h3>Uzun Ömür</h3>
              <p>
                Doğru bakımla uzun yıllar boyunca formunu kaybetmeden
                kullanabilirsiniz.
              </p>
            </div>
          </div>
        </div>
      </section>
    </div>

    <script>
      // Intersection Observer for scroll animations
      const observerOptions = {
        threshold: 0.1,
        rootMargin: "0px 0px -50px 0px",
      };

      const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.style.opacity = "1";
            entry.target.style.transform = "translateY(0)";
          }
        });
      }, observerOptions);

      // Initialize animations
      document.addEventListener("DOMContentLoaded", () => {
        // Set initial state for animated elements
        document.querySelectorAll(".fade-in-up").forEach((el) => {
          el.style.opacity = "0";
          el.style.transform = "translateY(60px)";
          el.style.transition = "all 0.8s ease-out";
          observer.observe(el);
        });

        // Add smooth scrolling for better UX
        document.documentElement.style.scrollBehavior = "smooth";
      });

      // Enhanced callout interactions
      document.querySelectorAll(".copper-callout").forEach((callout) => {
        callout.addEventListener("mouseenter", () => {
          callout.style.transform = "translateY(-8px) scale(1.02)";
        });

        callout.addEventListener("mouseleave", () => {
          callout.style.transform = "translateY(0) scale(1)";
        });
      });
    </script>
  </body>
</html>
