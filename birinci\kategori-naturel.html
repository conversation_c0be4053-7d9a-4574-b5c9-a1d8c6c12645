<!DOCTYPE html>
<html lang="tr">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title><PERSON><PERSON><PERSON></title>
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <style>
      /* Reset and Base Styles */
      .naturel-category * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      .naturel-category {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
        line-height: 1.6;
        color: #374151;
      }

      /* Gradients */
      .naturel-olive-gradient {
        background: linear-gradient(
          135deg,
          #8b9a5b 0%,
          #a4b494 50%,
          #c8d5b9 100%
        );
      }

      .naturel-cream-gradient {
        background: linear-gradient(
          135deg,
          #f5f5dc 0%,
          #fff8dc 50%,
          #fffacd 100%
        );
      }

      /* Animations */
      @keyframes naturelFadeInUp {
        from {
          opacity: 0;
          transform: translateY(30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .naturel-fade-in-up {
        animation: naturelFadeInUp 0.8s ease-out forwards;
      }

      .naturel-delay-1 {
        animation-delay: 0.2s;
      }
      .naturel-delay-2 {
        animation-delay: 0.4s;
      }
      .naturel-delay-3 {
        animation-delay: 0.6s;
      }

      /* Hover Effects */
      .naturel-hover-lift {
        transition: all 0.3s ease;
      }

      .naturel-hover-lift:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
      }

      /* Price Highlight */
      .naturel-price-highlight {
        background: linear-gradient(45deg, #8b9a5b, #a4b494);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      /* Container */
      .naturel-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 16px;
      }

      /* Hero Section */
      .naturel-hero {
        padding: 80px 16px;
        min-height: 100vh;
        display: flex;
        align-items: center;
      }

      .naturel-hero-content {
        text-align: center;
        margin-bottom: 48px;
      }

      .naturel-hero h1 {
        font-size: 60px;
        font-weight: bold;
        margin-bottom: 16px;
        color: #1f2937;
      }

      .naturel-hero .naturel-subtitle {
        font-size: 20px;
        color: #6b7280;
        max-width: 800px;
        margin: 0 auto;
      }

      /* Grid Layout */
      .naturel-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 48px;
        align-items: center;
      }

      /* Product Image */
      .naturel-image-container {
        position: relative;
      }

      .naturel-image-bg {
        position: absolute;
        inset: 0;
        border-radius: 24px;
        transform: rotate(3deg);
        opacity: 0.2;
      }

      .naturel-product-image {
        position: relative;
        border-radius: 24px;
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        width: 100%;
        max-width: 500px;
        margin: 0 auto;
        display: block;
      }

      /* Product Info */
      .naturel-info-card {
        background: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(8px);
        border-radius: 24px;
        padding: 32px;
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
      }

      .naturel-info-header {
        display: flex;
        align-items: center;
        margin-bottom: 24px;
      }

      .naturel-icon-container {
        padding: 16px;
        border-radius: 16px;
        margin-right: 16px;
      }

      .naturel-icon {
        color: white;
        font-size: 24px;
      }

      .naturel-info-title {
        font-size: 24px;
        font-weight: bold;
        color: #1f2937;
      }

      .naturel-info-subtitle {
        color: #6b7280;
      }

      /* Features List */
      .naturel-features {
        margin-bottom: 32px;
      }

      .naturel-feature {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
      }

      .naturel-feature i {
        color: #16a34a;
        margin-right: 12px;
      }

      .naturel-feature span {
        color: #374151;
      }

      /* Price Box */
      .naturel-price-box {
        background: linear-gradient(to right, #f0fdf4, #fef3c7);
        border-radius: 16px;
        padding: 24px;
        margin-bottom: 24px;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      .naturel-price-info p:first-child {
        color: #6b7280;
        font-size: 14px;
      }

      .naturel-price {
        font-size: 30px;
        font-weight: bold;
      }

      .naturel-price-icon {
        padding: 12px;
        border-radius: 50%;
      }

      /* Button */
      .naturel-btn {
        width: 100%;
        color: white;
        font-weight: bold;
        padding: 16px 24px;
        border-radius: 16px;
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;
        transform: scale(1);
      }

      .naturel-btn:hover {
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        transform: scale(1.05);
      }

      /* Features Section */
      .naturel-features-section {
        padding: 80px 16px;
        background: white;
      }

      .naturel-section-header {
        text-align: center;
        margin-bottom: 64px;
      }

      .naturel-section-title {
        font-size: 36px;
        font-weight: bold;
        color: #1f2937;
        margin-bottom: 16px;
      }

      .naturel-divider {
        width: 96px;
        height: 4px;
        margin: 0 auto;
        border-radius: 2px;
      }

      .naturel-features-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 32px;
      }

      .naturel-feature-card {
        text-align: center;
      }

      .naturel-feature-icon {
        width: 80px;
        height: 80px;
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 24px;
        transform: rotate(12deg);
      }

      .naturel-feature-icon i {
        color: white;
        font-size: 24px;
        transform: rotate(-12deg);
      }

      .naturel-feature-title {
        font-size: 20px;
        font-weight: bold;
        color: #1f2937;
        margin-bottom: 12px;
      }

      .naturel-feature-desc {
        color: #6b7280;
      }

      /* Responsive */
      @media (max-width: 1024px) {
        .naturel-grid {
          grid-template-columns: 1fr;
          gap: 32px;
        }

        .naturel-hero h1 {
          font-size: 48px;
        }

        .naturel-features-grid {
          grid-template-columns: 1fr;
          gap: 24px;
        }

        /* Reset order for mobile */
        .naturel-grid > div {
          order: unset !important;
        }
      }

      @media (max-width: 768px) {
        .naturel-hero {
          padding: 40px 16px;
        }

        .naturel-hero h1 {
          font-size: 36px;
        }

        .naturel-hero .naturel-subtitle {
          font-size: 18px;
        }

        .naturel-section-title {
          font-size: 30px;
        }

        .naturel-info-card {
          padding: 24px;
        }

        .naturel-features-section {
          padding: 60px 16px;
        }

        .naturel-section-header {
          margin-bottom: 48px;
        }
      }
    </style>
  </head>
  <body>
    <div class="naturel-category naturel-cream-gradient">
      <!-- Hero Section -->
      <section class="naturel-hero">
        <div class="naturel-container">
          <div class="naturel-hero-content naturel-fade-in-up">
            <h1>
              <span style="color: #1f2937">Natürel</span>
              <span class="naturel-price-highlight">Birinci Zeytinyağı</span>
            </h1>
            <p class="naturel-subtitle">
              Amanos Dağları'ndan doğal lezzet, dengeli tat ve zengin aroma
            </p>
          </div>

          <div class="naturel-grid">
            <!-- Product Image -->
            <div class="naturel-fade-in-up naturel-delay-1">
              <div class="naturel-image-container">
                <div class="naturel-image-bg naturel-olive-gradient"></div>
                <img
                  src="https://images.unsplash.com/photo-1474979266404-7eaacbcd87c5?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                  alt="Natürel Birinci Zeytinyağı"
                  class="naturel-product-image naturel-hover-lift"
                />
              </div>
            </div>

            <!-- Product Info -->
            <div class="naturel-fade-in-up naturel-delay-2">
              <div class="naturel-info-card naturel-hover-lift">
                <div class="naturel-info-header">
                  <div class="naturel-icon-container naturel-olive-gradient">
                    <i class="fas fa-leaf naturel-icon"></i>
                  </div>
                  <div>
                    <h2 class="naturel-info-title">Olgun Hasat</h2>
                    <p class="naturel-info-subtitle">Kasım-Aralık dönemi</p>
                  </div>
                </div>

                <div class="naturel-features">
                  <div class="naturel-feature">
                    <i class="fas fa-check-circle"></i>
                    <span>%0.8 - %2.0 asitlik oranı</span>
                  </div>
                  <div class="naturel-feature">
                    <i class="fas fa-check-circle"></i>
                    <span>35°C doğal sıkım</span>
                  </div>
                  <div class="naturel-feature">
                    <i class="fas fa-check-circle"></i>
                    <span>Kimyasal işlem yok</span>
                  </div>
                </div>

                <div class="naturel-price-box">
                  <div class="naturel-price-info">
                    <p>1 Litre Fiyat</p>
                    <p class="naturel-price naturel-price-highlight">₺150</p>
                  </div>
                  <div class="naturel-price-icon naturel-olive-gradient">
                    <i
                      class="fas fa-tag"
                      style="color: white; font-size: 20px"
                    ></i>
                  </div>
                </div>

                <button class="naturel-btn naturel-olive-gradient">
                  <i class="fas fa-shopping-cart" style="margin-right: 8px"></i>
                  Hemen Sipariş Ver
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Features Section -->
      <section class="naturel-features-section">
        <div class="naturel-container">
          <div class="naturel-section-header naturel-fade-in-up">
            <h2 class="naturel-section-title">
              Neden
              <span class="naturel-price-highlight">Natürel Birinci?</span>
            </h2>
            <div class="naturel-divider naturel-olive-gradient"></div>
          </div>

          <div class="naturel-features-grid">
            <!-- Feature 1 -->
            <div
              class="naturel-feature-card naturel-fade-in-up naturel-delay-1 naturel-hover-lift"
            >
              <div class="naturel-feature-icon naturel-olive-gradient">
                <i class="fas fa-mountain"></i>
              </div>
              <h3 class="naturel-feature-title">Amanos Dağları</h3>
              <p class="naturel-feature-desc">
                Amanos Dağları'nın eteklerinde yetişen zeytin ağaçlarından
              </p>
            </div>

            <!-- Feature 2 -->
            <div
              class="naturel-feature-card naturel-fade-in-up naturel-delay-2 naturel-hover-lift"
            >
              <div
                class="naturel-feature-icon naturel-olive-gradient"
                style="transform: rotate(-12deg)"
              >
                <i class="fas fa-seedling" style="transform: rotate(12deg)"></i>
              </div>
              <h3 class="naturel-feature-title">Dengeli Aroma</h3>
              <p class="naturel-feature-desc">
                Çağla, badem ve çimen notalarıyla zenginleşen tat profili
              </p>
            </div>

            <!-- Feature 3 -->
            <div
              class="naturel-feature-card naturel-fade-in-up naturel-delay-3 naturel-hover-lift"
            >
              <div class="naturel-feature-icon naturel-olive-gradient">
                <i class="fas fa-utensils"></i>
              </div>
              <h3 class="naturel-feature-title">Çok Amaçlı</h3>
              <p class="naturel-feature-desc">
                Sıcak yemekler, kızartmalar ve salatalarda kullanım
              </p>
            </div>
          </div>
        </div>
      </section>

      <!-- Usage Section -->
      <section class="naturel-hero naturel-cream-gradient">
        <div class="naturel-container">
          <div class="naturel-section-header naturel-fade-in-up">
            <h2 class="naturel-section-title">
              Kullanım <span class="naturel-price-highlight">Alanları</span>
            </h2>
            <p class="naturel-subtitle">
              Günlük mutfak kullanımı için oldukça uygundur
            </p>
          </div>

          <div style="margin-top: 64px">
            <!-- Usage 1 - Sıcak Yemeklerde -->
            <div class="naturel-grid" style="margin-bottom: 64px">
              <div class="naturel-fade-in-up naturel-delay-1">
                <img
                  src="https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                  alt="Sıcak yemeklerde zeytinyağı kullanımı"
                  class="naturel-product-image naturel-hover-lift"
                />
              </div>
              <div class="naturel-fade-in-up naturel-delay-2">
                <div class="naturel-info-card naturel-hover-lift">
                  <h3
                    class="naturel-section-title"
                    style="font-size: 30px; margin-bottom: 16px"
                  >
                    Sıcak Yemeklerde
                  </h3>
                  <p
                    style="font-size: 18px; color: #6b7280; margin-bottom: 24px"
                  >
                    Dengeli aromasıyla sıcak yemeklerinizde mükemmel uyum
                    sağlar. Asit oranının sağladığı yumuşak içimiyle lezzet
                    dengesi yaratır.
                  </p>
                  <div class="naturel-feature">
                    <i class="fas fa-check-circle"></i>
                    <span>Yumuşak ve dengeli aroma</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Usage 2 - Kızartmalarda -->
            <div class="naturel-grid" style="margin-bottom: 64px">
              <div class="naturel-fade-in-up naturel-delay-1" style="order: 2">
                <div class="naturel-info-card naturel-hover-lift">
                  <h3
                    class="naturel-section-title"
                    style="font-size: 30px; margin-bottom: 16px"
                  >
                    Kızartmalarda
                  </h3>
                  <p
                    style="font-size: 18px; color: #6b7280; margin-bottom: 24px"
                  >
                    Kıvamlı yapısı sayesinde kızartmalarda ideal performans
                    gösterir. Yoğun aromasıyla yemeklerinize derinlik katar.
                  </p>
                  <div class="naturel-feature">
                    <i class="fas fa-check-circle"></i>
                    <span>Kıvamlı yapı ve yoğun aroma</span>
                  </div>
                </div>
              </div>
              <div class="naturel-fade-in-up naturel-delay-2" style="order: 1">
                <img
                  src="https://images.unsplash.com/photo-1571091718767-18b5b1457add?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                  alt="Kızartmalarda zeytinyağı kullanımı"
                  class="naturel-product-image naturel-hover-lift"
                />
              </div>
            </div>

            <!-- Usage 3 - Salatalarda -->
            <div class="naturel-grid">
              <div class="naturel-fade-in-up naturel-delay-1">
                <img
                  src="https://images.unsplash.com/photo-1512621776951-a57141f2eefd?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                  alt="Salatalarda zeytinyağı kullanımı"
                  class="naturel-product-image naturel-hover-lift"
                />
              </div>
              <div class="naturel-fade-in-up naturel-delay-2">
                <div class="naturel-info-card naturel-hover-lift">
                  <h3
                    class="naturel-section-title"
                    style="font-size: 30px; margin-bottom: 16px"
                  >
                    Salatalarda
                  </h3>
                  <p
                    style="font-size: 18px; color: #6b7280; margin-bottom: 24px"
                  >
                    Çağla, badem ve çimen notalarıyla salatalarınıza özel bir
                    tat katar. Sızma zeytinyağlarına kıyasla daha sade ve
                    dengeli lezzet.
                  </p>
                  <div class="naturel-feature">
                    <i class="fas fa-check-circle"></i>
                    <span>Çağla, badem ve çimen notaları</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>

    <script>
      // Smooth scroll animations
      const naturelObserverOptions = {
        threshold: 0.1,
        rootMargin: "0px 0px -50px 0px",
      };

      const naturelObserver = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.style.opacity = "1";
            entry.target.style.transform = "translateY(0)";
          }
        });
      }, naturelObserverOptions);

      // Observe all animated elements
      document.querySelectorAll(".naturel-fade-in-up").forEach((el) => {
        el.style.opacity = "0";
        el.style.transform = "translateY(30px)";
        naturelObserver.observe(el);
      });
    </script>
  </body>
</html>
